# Solución: Error 401 invalid_client - Google OAuth

## ✅ Problema Solucionado

**Error**: `Error 401: invalid_client` al usar Google OAuth
**Causa**: Credenciales placeholder configuradas en Supabase
**Solución**: Google OAuth temporalmente deshabilitado hasta configurar credenciales reales

## Estado Actual de Autenticación

### ✅ Completamente Funcional
- **Email/Password Login**: `http://localhost:3007/login`
- **Email/Password Register**: `http://localhost:3007/register`
- **Gestión de sesiones**: Automática con Supabase
- **Rutas protegidas**: Funcionando correctamente
- **Logout**: Funcional con redirección

### ⚠️ Google OAuth: Configuración Pendiente
- Temporalmente deshabilitado en Supabase
- Botones mostrados como "Configuración pendiente"
- Listo para habilitar una vez tengas credenciales reales

## Cómo Configurar Google OAuth (Paso a Paso)

### Paso 1: Crear Proyecto en Google Cloud Console

1. **Accede a Google Cloud Console**:
   - Ve a [console.cloud.google.com](https://console.cloud.google.com/)
   - Inicia sesión con tu cuenta de Google

2. **Crear nuevo proyecto**:
   - Clic en el selector de proyecto (arriba izquierda)
   - "New Project" → Nombre: "Emma Studio Auth"
   - Clic en "Create"

### Paso 2: Configurar OAuth Consent Screen

1. **Ir a OAuth consent screen**:
   - Menú lateral → "APIs & Services" → "OAuth consent screen"

2. **Configurar la pantalla de consentimiento**:
   - User Type: **External** (para uso público)
   - App name: "Emma Studio"
   - User support email: tu email
   - Developer contact: tu email
   - Clic en "Save and Continue"

3. **Scopes** (siguiente pantalla):
   - Clic en "Save and Continue" (usar scopes por defecto)

4. **Test users** (siguiente pantalla):
   - Agregar tu email como test user
   - Clic en "Save and Continue"

### Paso 3: Crear Credenciales OAuth 2.0

1. **Ir a Credentials**:
   - Menú lateral → "APIs & Services" → "Credentials"

2. **Crear OAuth 2.0 Client ID**:
   - Clic en "Create Credentials" → "OAuth 2.0 Client ID"
   - Application type: **Web application**
   - Name: "Emma Studio OAuth Client"

3. **Configurar URLs autorizadas** (MUY IMPORTANTE):
   ```
   Authorized JavaScript origins:
   - http://localhost:3007
   - https://pthewpjbegkgomvyhkin.supabase.co

   Authorized redirect URIs:
   - https://pthewpjbegkgomvyhkin.supabase.co/auth/v1/callback
   ```

4. **Guardar y copiar credenciales**:
   - Clic en "Create"
   - **Copia el Client ID** (algo como: 123456789-abc123.apps.googleusercontent.com)
   - **Copia el Client Secret** (algo como: GOCSPX-abc123def456)

### Paso 4: Configurar en Supabase

Una vez tengas las credenciales reales, envíamelas y yo las configuraré en Supabase, o puedes hacerlo tú:

1. **Ir a Supabase Dashboard**:
   - [supabase.com/dashboard](https://supabase.com/dashboard)
   - Proyecto: "EMMA ai"

2. **Configurar Google Provider**:
   - Authentication → Providers → Google
   - Enable Google provider: ✅
   - Client ID: [pegar tu Client ID]
   - Client Secret: [pegar tu Client Secret]
   - Save

### Paso 5: Habilitar en el Código

Una vez configurado en Supabase, avísame y habilitaré los botones de Google OAuth en el código.

## Pruebas Actuales Disponibles

### Autenticación Email/Password (100% Funcional)

1. **Registro**:
   ```
   URL: http://localhost:3007/register
   - Completa el formulario
   - Automáticamente redirige al dashboard
   ```

2. **Login**:
   ```
   URL: http://localhost:3007/login
   - Usa credenciales registradas
   - Automáticamente redirige al dashboard
   ```

3. **Logout**:
   ```
   - Desde el sidebar del dashboard
   - Desde la página de perfil (/dashboard/perfil)
   - Automáticamente redirige al login
   ```

4. **Rutas Protegidas**:
   ```
   - Intenta acceder a /dashboard sin login
   - Automáticamente redirige a /login
   ```

## Comandos para Habilitar Google OAuth

Una vez tengas las credenciales, puedo ejecutar estos comandos para habilitarlo:

```bash
# Configurar credenciales en Supabase
supabase auth update --google-client-id="TU_CLIENT_ID" --google-secret="TU_CLIENT_SECRET"

# Habilitar Google OAuth
supabase auth update --google-enabled=true
```

## Errores Comunes y Soluciones

### Error: "unauthorized_client"
- **Causa**: URLs de redirección incorrectas
- **Solución**: Verificar que las URLs en Google Cloud Console coincidan exactamente

### Error: "access_denied"
- **Causa**: Usuario canceló la autenticación
- **Solución**: Normal, no requiere acción

### Error: "invalid_client"
- **Causa**: Client ID o Secret incorrectos
- **Solución**: Verificar credenciales en Google Cloud Console y Supabase

## Próximos Pasos

1. ✅ **Usar autenticación email/password** (funcional ahora)
2. 🔧 **Crear proyecto en Google Cloud Console**
3. 🔧 **Configurar OAuth consent screen**
4. 🔧 **Crear credenciales OAuth 2.0**
5. 🔧 **Enviarme las credenciales para configurar**
6. ✅ **Habilitar Google OAuth en el código**

## Contacto

Cuando tengas las credenciales de Google OAuth, compártelas conmigo y habilitaré inmediatamente Google OAuth en Emma Studio.

**Credenciales necesarias**:
- Google Client ID
- Google Client Secret

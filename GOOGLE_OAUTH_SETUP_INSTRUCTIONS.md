# Configuración de Google OAuth para Emma Studio

## Estado Actual ✅

La autenticación con Supabase está **completamente funcional** para:
- ✅ **Registro con email/password**: `http://localhost:3007/register`
- ✅ **Login con email/password**: `http://localhost:3007/login`
- ✅ **Gestión de sesiones**: Persistencia automática de autenticación
- ✅ **Rutas protegidas**: Redirección automática a login si no está autenticado
- ⚠️ **Google OAuth**: Habilitado en Supabase pero requiere credenciales de Google

## Configuración de Google OAuth (Pendiente)

### Paso 1: Crear Proyecto en Google Cloud Console

1. **Accede a Google Cloud Console**:
   - Ve a [Google Cloud Console](https://console.cloud.google.com/)
   - Inicia sesión con tu cuenta de Google

2. **Crear o seleccionar proyecto**:
   - Crea un nuevo proyecto o selecciona uno existente
   - Nombre sugerido: "Emma Studio Auth"

### Paso 2: Habilitar APIs

1. **Navega a APIs & Services**:
   - En el menú lateral: "APIs & Services" → "Library"

2. **Habilitar Google Identity API**:
   - Busca "Google Identity API" o "Google+ API"
   - Haz clic en "Enable"

### Paso 3: Crear Credenciales OAuth 2.0

1. **Ir a Credenciales**:
   - "APIs & Services" → "Credentials"

2. **Crear OAuth 2.0 Client ID**:
   - Clic en "Create Credentials" → "OAuth 2.0 Client ID"
   - Tipo de aplicación: **"Web application"**
   - Nombre: "Emma Studio OAuth"

3. **Configurar URLs autorizadas** (MUY IMPORTANTE):
   ```
   Authorized JavaScript origins:
   - http://localhost:3007
   - https://pthewpjbegkgomvyhkin.supabase.co

   Authorized redirect URIs:
   - https://pthewpjbegkgomvyhkin.supabase.co/auth/v1/callback
   ```

4. **Guardar credenciales**:
   - Copia el **Client ID** y **Client Secret**

### Paso 4: Configurar Supabase

1. **Accede al Dashboard de Supabase**:
   - Ve a [Supabase Dashboard](https://supabase.com/dashboard)
   - Selecciona el proyecto "EMMA ai"

2. **Configurar Google Provider**:
   - Ve a "Authentication" → "Providers"
   - Busca "Google" y haz clic en configurar
   - Pega el **Client ID** y **Client Secret** de Google
   - Asegúrate de que esté habilitado

### Paso 5: Habilitar Google OAuth en el Código

Una vez tengas las credenciales configuradas, actualiza el código:

```typescript
// En client/src/pages/login-page.tsx
// Cambiar el botón deshabilitado por:
<button
  className="w-full flex items-center justify-center gap-2 bg-gray-50 border border-gray-200 rounded-lg p-3 hover:bg-gray-100 transition-all duration-300 text-gray-700 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
  onClick={handleGoogleLogin}
  disabled={googleLoginMutation.isPending}
>
  <span>{googleLoginMutation.isPending ? "Conectando..." : "Iniciar con Google"}</span>
</button>
```

## Pruebas de Autenticación

### Autenticación Email/Password (Funcional Ahora)

1. **Registro**:
   - Ve a `http://localhost:3007/register`
   - Completa el formulario con email y contraseña
   - Deberías ser redirigido al dashboard

2. **Login**:
   - Ve a `http://localhost:3007/login`
   - Usa las credenciales que registraste
   - Deberías ser redirigido al dashboard

3. **Logout**:
   - Usa el hook `useAuth()` y llama `logoutMutation.mutate()`

### Google OAuth (Después de configuración)

1. **Login con Google**:
   - Clic en "Iniciar con Google"
   - Redirección a Google para autenticación
   - Regreso automático al dashboard

## Configuración de Producción

Para producción, agrega estas URLs a Google OAuth:
```
Authorized JavaScript origins:
- https://tu-dominio.com

Authorized redirect URIs:
- https://pthewpjbegkgomvyhkin.supabase.co/auth/v1/callback
```

## Solución de Problemas

### Error: "Unsupported provider: missing OAuth client ID"
- Verifica que las credenciales de Google estén configuradas en Supabase
- Asegúrate de que el Google Provider esté habilitado

### Error: "unauthorized_client"
- Verifica que las URLs de redirección estén correctamente configuradas
- Asegúrate de usar HTTPS en producción

### Error: "access_denied"
- El usuario canceló la autenticación con Google
- Esto es normal y no requiere acción

## Archivos Modificados

- ✅ `client/src/hooks/use-auth.tsx` - Migrado a Supabase
- ✅ `client/src/pages/login-page.tsx` - Actualizado para Supabase
- ✅ `client/src/pages/register-page.tsx` - Actualizado para Supabase
- ✅ `client/src/lib/protected-route.tsx` - Habilitada autenticación real
- ✅ `.env` - Configurado con credenciales de Supabase

## Contacto

Si necesitas ayuda con la configuración, proporciona:
1. Screenshots de errores específicos
2. Configuración actual de Google Cloud Console
3. Estado de la configuración en Supabase Dashboard

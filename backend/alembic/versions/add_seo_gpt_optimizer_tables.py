"""Add SEO GPT Optimizer tables

Revision ID: add_seo_gpt_optimizer_tables
Revises: add_seo_analyses_table
Create Date: 2024-12-16 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision = 'add_seo_gpt_optimizer_tables'
down_revision = 'add_seo_analyses_table'
branch_labels = None
depends_on = None

def upgrade():
    """Create SEO GPT Optimizer tables."""
    
    # Create seo_gpt_projects table
    op.create_table('seo_gpt_projects',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('project_id', sa.String(length=64), nullable=False),
        sa.Column('user_id', sa.String(length=64), nullable=True),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('topic', sa.String(length=255), nullable=False),
        sa.Column('target_language', sa.String(length=10), nullable=True),
        sa.Column('content_type', sa.Enum('ARTICLE', 'GUIDE', 'TUTORIAL', 'COMPARISON', 'LIST', 'FAQ', name='contenttype'), nullable=True),
        sa.Column('status', sa.Enum('DRAFT', 'RESEARCHING', 'WRITING', 'OPTIMIZING', 'COMPLETED', 'PUBLISHED', name='projectstatus'), nullable=True),
        sa.Column('content_text', sa.Text(), nullable=True),
        sa.Column('content_length', sa.Integer(), nullable=True),
        sa.Column('word_count', sa.Integer(), nullable=True),
        sa.Column('current_gpt_rank_score', sa.Float(), nullable=True),
        sa.Column('target_gpt_rank_score', sa.Float(), nullable=True),
        sa.Column('best_gpt_rank_score', sa.Float(), nullable=True),
        sa.Column('seo_score', sa.Float(), nullable=True),
        sa.Column('target_keywords', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('published_at', sa.DateTime(), nullable=True),
        sa.Column('research_data', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for seo_gpt_projects
    op.create_index(op.f('ix_seo_gpt_projects_id'), 'seo_gpt_projects', ['id'], unique=False)
    op.create_index(op.f('ix_seo_gpt_projects_project_id'), 'seo_gpt_projects', ['project_id'], unique=True)
    op.create_index(op.f('ix_seo_gpt_projects_user_id'), 'seo_gpt_projects', ['user_id'], unique=False)
    op.create_index(op.f('ix_seo_gpt_projects_topic'), 'seo_gpt_projects', ['topic'], unique=False)
    op.create_index(op.f('ix_seo_gpt_projects_target_language'), 'seo_gpt_projects', ['target_language'], unique=False)
    op.create_index(op.f('ix_seo_gpt_projects_content_type'), 'seo_gpt_projects', ['content_type'], unique=False)
    op.create_index(op.f('ix_seo_gpt_projects_status'), 'seo_gpt_projects', ['status'], unique=False)
    op.create_index(op.f('ix_seo_gpt_projects_current_gpt_rank_score'), 'seo_gpt_projects', ['current_gpt_rank_score'], unique=False)
    op.create_index(op.f('ix_seo_gpt_projects_created_at'), 'seo_gpt_projects', ['created_at'], unique=False)
    
    # Create content_analyses table
    op.create_table('content_analyses',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('analysis_id', sa.String(length=64), nullable=False),
        sa.Column('project_id', sa.Integer(), nullable=True),
        sa.Column('content_version', sa.Integer(), nullable=True),
        sa.Column('analysis_type', sa.String(length=50), nullable=True),
        sa.Column('semantic_similarity_score', sa.Float(), nullable=True),
        sa.Column('logical_coherence_score', sa.Float(), nullable=True),
        sa.Column('authority_signals_score', sa.Float(), nullable=True),
        sa.Column('citability_score', sa.Float(), nullable=True),
        sa.Column('clarity_score', sa.Float(), nullable=True),
        sa.Column('completeness_score', sa.Float(), nullable=True),
        sa.Column('gpt_rank_score', sa.Float(), nullable=True),
        sa.Column('confidence_level', sa.String(length=20), nullable=True),
        sa.Column('score_grade', sa.String(length=5), nullable=True),
        sa.Column('word_count', sa.Integer(), nullable=True),
        sa.Column('character_count', sa.Integer(), nullable=True),
        sa.Column('paragraph_count', sa.Integer(), nullable=True),
        sa.Column('sentence_count', sa.Integer(), nullable=True),
        sa.Column('improvement_suggestions', sa.Text(), nullable=True),
        sa.Column('component_details', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('processing_time', sa.Float(), nullable=True),
        sa.ForeignKeyConstraint(['project_id'], ['seo_gpt_projects.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for content_analyses
    op.create_index(op.f('ix_content_analyses_id'), 'content_analyses', ['id'], unique=False)
    op.create_index(op.f('ix_content_analyses_analysis_id'), 'content_analyses', ['analysis_id'], unique=True)
    op.create_index(op.f('ix_content_analyses_project_id'), 'content_analyses', ['project_id'], unique=False)
    op.create_index(op.f('ix_content_analyses_analysis_type'), 'content_analyses', ['analysis_type'], unique=False)
    op.create_index(op.f('ix_content_analyses_gpt_rank_score'), 'content_analyses', ['gpt_rank_score'], unique=False)
    op.create_index(op.f('ix_content_analyses_created_at'), 'content_analyses', ['created_at'], unique=False)
    
    # Create gpt_rank_history table
    op.create_table('gpt_rank_history',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('project_id', sa.Integer(), nullable=True),
        sa.Column('gpt_rank_score', sa.Float(), nullable=False),
        sa.Column('score_change', sa.Float(), nullable=True),
        sa.Column('score_grade', sa.String(length=5), nullable=False),
        sa.Column('semantic_similarity', sa.Float(), nullable=True),
        sa.Column('logical_coherence', sa.Float(), nullable=True),
        sa.Column('authority_signals', sa.Float(), nullable=True),
        sa.Column('citability_score', sa.Float(), nullable=True),
        sa.Column('clarity_score', sa.Float(), nullable=True),
        sa.Column('completeness_score', sa.Float(), nullable=True),
        sa.Column('content_length', sa.Integer(), nullable=True),
        sa.Column('trigger_event', sa.String(length=100), nullable=True),
        sa.Column('user_action', sa.String(length=100), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['project_id'], ['seo_gpt_projects.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for gpt_rank_history
    op.create_index(op.f('ix_gpt_rank_history_id'), 'gpt_rank_history', ['id'], unique=False)
    op.create_index(op.f('ix_gpt_rank_history_project_id'), 'gpt_rank_history', ['project_id'], unique=False)
    op.create_index(op.f('ix_gpt_rank_history_gpt_rank_score'), 'gpt_rank_history', ['gpt_rank_score'], unique=False)
    op.create_index(op.f('ix_gpt_rank_history_created_at'), 'gpt_rank_history', ['created_at'], unique=False)
    
    # Create keyword_research table
    op.create_table('keyword_research',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('research_id', sa.String(length=64), nullable=False),
        sa.Column('project_id', sa.Integer(), nullable=True),
        sa.Column('topic', sa.String(length=255), nullable=False),
        sa.Column('target_language', sa.String(length=10), nullable=True),
        sa.Column('research_type', sa.String(length=50), nullable=True),
        sa.Column('intent_analysis', sa.Text(), nullable=True),
        sa.Column('google_results', sa.Text(), nullable=True),
        sa.Column('social_insights', sa.Text(), nullable=True),
        sa.Column('gpt_reference', sa.Text(), nullable=True),
        sa.Column('entities_and_questions', sa.Text(), nullable=True),
        sa.Column('content_opportunities', sa.Text(), nullable=True),
        sa.Column('research_confidence', sa.Float(), nullable=True),
        sa.Column('processing_time', sa.Float(), nullable=True),
        sa.Column('total_sources_analyzed', sa.Integer(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['project_id'], ['seo_gpt_projects.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for keyword_research
    op.create_index(op.f('ix_keyword_research_id'), 'keyword_research', ['id'], unique=False)
    op.create_index(op.f('ix_keyword_research_research_id'), 'keyword_research', ['research_id'], unique=True)
    op.create_index(op.f('ix_keyword_research_project_id'), 'keyword_research', ['project_id'], unique=False)
    op.create_index(op.f('ix_keyword_research_topic'), 'keyword_research', ['topic'], unique=False)
    op.create_index(op.f('ix_keyword_research_status'), 'keyword_research', ['status'], unique=False)
    op.create_index(op.f('ix_keyword_research_created_at'), 'keyword_research', ['created_at'], unique=False)
    
    # Create composite indexes for better performance
    op.create_index('idx_seo_gpt_projects_user_status', 'seo_gpt_projects', ['user_id', 'status'], unique=False)
    op.create_index('idx_seo_gpt_projects_topic_language', 'seo_gpt_projects', ['topic', 'target_language'], unique=False)
    op.create_index('idx_content_analyses_project_created', 'content_analyses', ['project_id', 'created_at'], unique=False)
    op.create_index('idx_gpt_rank_history_project_created', 'gpt_rank_history', ['project_id', 'created_at'], unique=False)
    op.create_index('idx_keyword_research_topic_status', 'keyword_research', ['topic', 'status'], unique=False)

def downgrade():
    """Drop SEO GPT Optimizer tables."""
    
    # Drop composite indexes
    op.drop_index('idx_keyword_research_topic_status', table_name='keyword_research')
    op.drop_index('idx_gpt_rank_history_project_created', table_name='gpt_rank_history')
    op.drop_index('idx_content_analyses_project_created', table_name='content_analyses')
    op.drop_index('idx_seo_gpt_projects_topic_language', table_name='seo_gpt_projects')
    op.drop_index('idx_seo_gpt_projects_user_status', table_name='seo_gpt_projects')
    
    # Drop keyword_research table
    op.drop_index(op.f('ix_keyword_research_created_at'), table_name='keyword_research')
    op.drop_index(op.f('ix_keyword_research_status'), table_name='keyword_research')
    op.drop_index(op.f('ix_keyword_research_topic'), table_name='keyword_research')
    op.drop_index(op.f('ix_keyword_research_project_id'), table_name='keyword_research')
    op.drop_index(op.f('ix_keyword_research_research_id'), table_name='keyword_research')
    op.drop_index(op.f('ix_keyword_research_id'), table_name='keyword_research')
    op.drop_table('keyword_research')
    
    # Drop gpt_rank_history table
    op.drop_index(op.f('ix_gpt_rank_history_created_at'), table_name='gpt_rank_history')
    op.drop_index(op.f('ix_gpt_rank_history_gpt_rank_score'), table_name='gpt_rank_history')
    op.drop_index(op.f('ix_gpt_rank_history_project_id'), table_name='gpt_rank_history')
    op.drop_index(op.f('ix_gpt_rank_history_id'), table_name='gpt_rank_history')
    op.drop_table('gpt_rank_history')
    
    # Drop content_analyses table
    op.drop_index(op.f('ix_content_analyses_created_at'), table_name='content_analyses')
    op.drop_index(op.f('ix_content_analyses_gpt_rank_score'), table_name='content_analyses')
    op.drop_index(op.f('ix_content_analyses_analysis_type'), table_name='content_analyses')
    op.drop_index(op.f('ix_content_analyses_project_id'), table_name='content_analyses')
    op.drop_index(op.f('ix_content_analyses_analysis_id'), table_name='content_analyses')
    op.drop_index(op.f('ix_content_analyses_id'), table_name='content_analyses')
    op.drop_table('content_analyses')
    
    # Drop seo_gpt_projects table
    op.drop_index(op.f('ix_seo_gpt_projects_created_at'), table_name='seo_gpt_projects')
    op.drop_index(op.f('ix_seo_gpt_projects_current_gpt_rank_score'), table_name='seo_gpt_projects')
    op.drop_index(op.f('ix_seo_gpt_projects_status'), table_name='seo_gpt_projects')
    op.drop_index(op.f('ix_seo_gpt_projects_content_type'), table_name='seo_gpt_projects')
    op.drop_index(op.f('ix_seo_gpt_projects_target_language'), table_name='seo_gpt_projects')
    op.drop_index(op.f('ix_seo_gpt_projects_topic'), table_name='seo_gpt_projects')
    op.drop_index(op.f('ix_seo_gpt_projects_user_id'), table_name='seo_gpt_projects')
    op.drop_index(op.f('ix_seo_gpt_projects_project_id'), table_name='seo_gpt_projects')
    op.drop_index(op.f('ix_seo_gpt_projects_id'), table_name='seo_gpt_projects')
    op.drop_table('seo_gpt_projects')

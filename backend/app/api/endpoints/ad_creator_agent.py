"""
API endpoints for the Ad Creator Agent.
Specialized agent for ad creation workflows that feels like <PERSON>.
"""

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from app.services.ad_creator_agent_service import AdCreatorAgentService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/ad-creator-agent", tags=["Ad Creator Agent"])

# Initialize the ad creator agent service
ad_creator_agent = AdCreatorAgentService()


class AdCreatorChatRequest(BaseModel):
    """Request model for ad creator agent chat."""
    message: str = Field(..., description="User's message to the ad creator agent")
    context: Optional[Dict[str, Any]] = Field(default=None, description="Context including platform, current state, etc.")


class AdCreatorChatResponse(BaseModel):
    """Response model for ad creator agent chat."""
    response: str = Field(..., description="Agent's response")
    suggestions: list[str] = Field(default=[], description="Contextual suggestions for the user")
    field_suggestions: Dict[str, str] = Field(default={}, description="Suggestions for form field population")
    metadata: Dict[str, Any] = Field(default={}, description="Additional metadata")


class GenerateFieldRequest(BaseModel):
    """Request model for generating specific fields."""
    field_type: str = Field(..., description="Type of field to generate (headline, punchline, cta)")
    platform: str = Field(..., description="Platform for the ad (facebook, instagram, linkedin, etc.)")
    product_description: str = Field(..., description="Description of the product/service")


class GenerateFieldResponse(BaseModel):
    """Response model for field generation."""
    content: str = Field(..., description="Generated content for the field")
    field_type: str = Field(..., description="Type of field that was generated")
    platform: str = Field(..., description="Platform the content was optimized for")


@router.post("/chat", response_model=AdCreatorChatResponse)
async def chat_with_ad_creator_agent(request: AdCreatorChatRequest):
    """
    Chat with the specialized ad creator agent.
    This agent feels like Emma but is focused on ad creation workflows.
    """
    try:
        logger.info(f"Ad creator agent chat request: {request.message[:100]}...")
        
        # Process the chat request
        result = await ad_creator_agent.chat(
            message=request.message,
            context=request.context
        )
        
        return AdCreatorChatResponse(
            response=result["response"],
            suggestions=result.get("suggestions", []),
            field_suggestions=result.get("field_suggestions", {}),
            metadata=result.get("metadata", {})
        )
        
    except Exception as e:
        logger.error(f"Error in ad creator agent chat: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error processing chat request: {str(e)}"
        )


@router.post("/generate-field", response_model=GenerateFieldResponse)
async def generate_specific_field(request: GenerateFieldRequest):
    """
    Generate specific field content (headline, punchline, CTA) using the ad creator agent.
    """
    try:
        logger.info(f"Generating {request.field_type} for {request.platform}")
        
        # Validate field type
        valid_fields = ["headline", "punchline", "cta"]
        if request.field_type not in valid_fields:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid field type. Must be one of: {valid_fields}"
            )
        
        # Prepare context for field generation
        context = {
            "platform": request.platform,
            "productDescription": request.product_description,
            "task": f"generate_{request.field_type}"
        }
        
        # Generate the field content
        content = await ad_creator_agent.generate_specific_field(
            field_type=request.field_type,
            context=context
        )
        
        return GenerateFieldResponse(
            content=content,
            field_type=request.field_type,
            platform=request.platform
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating {request.field_type}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error generating {request.field_type}: {str(e)}"
        )


@router.get("/status")
async def get_ad_creator_agent_status():
    """Get the status of the ad creator agent."""
    try:
        return {
            "status": "online",
            "agent_name": "Emma Ad Creator",
            "capabilities": [
                "chat_interaction",
                "headline_generation",
                "punchline_generation", 
                "cta_generation",
                "description_optimization",
                "platform_specific_advice"
            ],
            "supported_platforms": [
                "facebook",
                "instagram", 
                "linkedin",
                "youtube",
                "google",
                "twitter"
            ],
            "ai_available": ad_creator_agent.ai_provider.is_ai_available()
        }
        
    except Exception as e:
        logger.error(f"Error getting ad creator agent status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting status: {str(e)}"
        )


class SuggestionsRequest(BaseModel):
    """Request model for getting contextual suggestions."""
    platform: str = Field(..., description="Platform for the ad")
    task: str = Field(default="general", description="Current task or context")
    message: str = Field(default="", description="Current user message for context")


@router.post("/suggestions")
async def get_contextual_suggestions(request: SuggestionsRequest):
    """
    Get contextual suggestions for the ad creator interface.
    """
    try:
        suggestions = ad_creator_agent._generate_contextual_suggestions(
            message=request.message,
            platform=request.platform,
            task=request.task
        )

        return {
            "suggestions": suggestions,
            "platform": request.platform,
            "task": request.task
        }

    except Exception as e:
        logger.error(f"Error getting suggestions: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting suggestions: {str(e)}"
        )


# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check for the ad creator agent service."""
    return {
        "status": "healthy",
        "service": "ad_creator_agent",
        "timestamp": logger.info("Health check requested")
    }

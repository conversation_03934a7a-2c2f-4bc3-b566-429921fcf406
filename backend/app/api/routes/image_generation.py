"""
Image Generation API Routes
Endpoints para generar imágenes con Ideogram AI
"""

import os
import httpx
import logging
from typing import Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from app.core.config import get_settings
from app.core.security import get_current_user
from app.models.user import User

logger = logging.getLogger(__name__)
router = APIRouter()
settings = get_settings()

class ImageGenerationRequest(BaseModel):
    prompt: str = Field(..., min_length=1, max_length=1000, description="Descripción de la imagen a generar")
    style: str = Field(default="realistic", description="Estilo de la imagen")
    model: str = Field(default="ideogram-3.0-quality", description="Modelo de Ideogram a usar")
    aspect_ratio: str = Field(default="1:1", description="Relación de aspecto")
    magic_prompt_option: str = Field(default="AUTO", description="Opción de prompt mágico")

class ImageGenerationResponse(BaseModel):
    success: bool
    imageUrl: str
    prompt: str
    style: str
    message: Optional[str] = None

@router.post("/generate-image", response_model=ImageGenerationResponse)
async def generate_image(
    request: ImageGenerationRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Generar imagen usando Ideogram AI
    """
    try:
        # Verificar que tenemos la API key
        ideogram_api_key = os.getenv("IDEOGRAM_API_KEY")
        if not ideogram_api_key:
            raise HTTPException(
                status_code=500,
                detail="Ideogram API key no configurada"
            )

        # Preparar el payload para Ideogram
        payload = {
            "image_request": {
                "model": request.model,
                "prompt": request.prompt,
                "aspect_ratio": request.aspect_ratio,
                "magic_prompt_option": request.magic_prompt_option,
                "seed": None,
                "style_type": "AUTO",
                "negative_prompt": "blurry, low quality, distorted, watermark, text, signature"
            }
        }

        # Headers para la API de Ideogram
        headers = {
            "Api-Key": ideogram_api_key,
            "Content-Type": "application/json"
        }

        logger.info(f"Generando imagen con Ideogram: {request.prompt[:50]}...")

        # Hacer la petición a Ideogram
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                "https://api.ideogram.ai/generate",
                json=payload,
                headers=headers
            )

            if response.status_code != 200:
                logger.error(f"Error de Ideogram API: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Error al generar imagen: {response.text}"
                )

            result = response.json()
            
            # Extraer la URL de la imagen
            if "data" in result and len(result["data"]) > 0:
                image_url = result["data"][0]["url"]
                
                logger.info(f"Imagen generada exitosamente: {image_url}")
                
                return ImageGenerationResponse(
                    success=True,
                    imageUrl=image_url,
                    prompt=request.prompt,
                    style=request.style,
                    message="Imagen generada exitosamente"
                )
            else:
                logger.error(f"Respuesta inesperada de Ideogram: {result}")
                raise HTTPException(
                    status_code=500,
                    detail="No se pudo obtener la imagen generada"
                )

    except httpx.TimeoutException:
        logger.error("Timeout al generar imagen con Ideogram")
        raise HTTPException(
            status_code=408,
            detail="Timeout al generar imagen. Inténtalo de nuevo."
        )
    except httpx.RequestError as e:
        logger.error(f"Error de conexión con Ideogram: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail="Error de conexión con el servicio de generación de imágenes"
        )
    except Exception as e:
        logger.error(f"Error inesperado al generar imagen: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Error interno del servidor al generar imagen"
        )

@router.get("/image-styles")
async def get_image_styles():
    """
    Obtener estilos disponibles para generación de imágenes
    """
    styles = [
        {
            "id": "realistic",
            "name": "Realista",
            "description": "Fotografías realistas y detalladas",
            "example_prompt": "Professional photograph of a modern office"
        },
        {
            "id": "illustration",
            "name": "Ilustración",
            "description": "Estilo ilustrativo y artístico",
            "example_prompt": "Beautiful illustration of a sunset landscape"
        },
        {
            "id": "digital-art",
            "name": "Arte Digital",
            "description": "Arte digital moderno y creativo",
            "example_prompt": "Digital art of a futuristic city"
        },
        {
            "id": "minimalist",
            "name": "Minimalista",
            "description": "Diseño limpio y simple",
            "example_prompt": "Minimalist design of a coffee cup"
        },
        {
            "id": "vintage",
            "name": "Vintage",
            "description": "Estilo retro y nostálgico",
            "example_prompt": "Vintage style poster of a classic car"
        },
        {
            "id": "abstract",
            "name": "Abstracto",
            "description": "Arte abstracto y conceptual",
            "example_prompt": "Abstract representation of creativity"
        }
    ]
    
    return {
        "success": True,
        "styles": styles
    }

@router.get("/image-models")
async def get_image_models():
    """
    Obtener modelos disponibles de Ideogram
    """
    models = [
        {
            "id": "ideogram-3.0-quality",
            "name": "Ideogram 3.0 Quality",
            "description": "Máxima calidad y detalle",
            "recommended": True
        },
        {
            "id": "ideogram-3.0-speed",
            "name": "Ideogram 3.0 Speed",
            "description": "Generación rápida",
            "recommended": False
        }
    ]
    
    return {
        "success": True,
        "models": models
    }

# Prompt enhancement utilities
def enhance_prompt_for_style(prompt: str, style: str) -> str:
    """
    Mejorar el prompt según el estilo seleccionado
    """
    style_enhancements = {
        "realistic": "professional high-quality photograph, photorealistic, detailed, sharp focus, professional lighting",
        "illustration": "beautiful illustration, artistic style, vibrant colors, detailed artwork",
        "digital-art": "digital art, modern digital painting, artistic, creative composition",
        "minimalist": "minimalist design, clean, simple, elegant, white background",
        "vintage": "vintage style, retro aesthetic, nostalgic mood, classic composition",
        "abstract": "abstract representation, conceptual art, creative interpretation"
    }
    
    enhancement = style_enhancements.get(style, "")
    if enhancement:
        return f"{prompt}, {enhancement}"
    return prompt

@router.post("/enhance-prompt")
async def enhance_prompt(
    prompt: str,
    style: str = "realistic",
    current_user: User = Depends(get_current_user)
):
    """
    Mejorar un prompt para generación de imágenes
    """
    try:
        enhanced = enhance_prompt_for_style(prompt, style)
        
        return {
            "success": True,
            "original_prompt": prompt,
            "enhanced_prompt": enhanced,
            "style": style
        }
    except Exception as e:
        logger.error(f"Error al mejorar prompt: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Error al mejorar el prompt"
        )

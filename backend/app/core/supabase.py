"""
Supabase client configuration for Emma Studio backend
"""

import logging
from typing import Optional, Dict, Any, List
from supabase import create_client, Client

logger = logging.getLogger(__name__)

# Supabase configuration - using the same values as frontend for consistency
SUPABASE_URL = "https://pthewpjbegkgomvyhkin.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB0aGV3cGpiZWdrZ29tdnloa2luIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MjM1NDMsImV4cCI6MjA2NDI5OTU0M30.bskxkyZ9meYb2cpZZGmS_FAS2Wyjs4j_lOPnJqh1s0k"

# Create Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)

logger.info(f"Supabase client initialized for URL: {SUPABASE_URL}")


class SupabaseService:
    """Service class for Supabase database operations"""
    
    def __init__(self):
        self.client = supabase
    
    async def save_design_analysis(
        self,
        user_id: str,
        analysis_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Save a design analysis to the database
        
        Args:
            user_id: The ID of the user who owns this analysis
            analysis_data: The analysis data to save
            
        Returns:
            The saved analysis record or None if failed
        """
        try:
            # Prepare the data for insertion
            insert_data = {
                "user_id": user_id,
                "original_filename": analysis_data.get("original_filename", "unknown.jpg"),
                "file_size": analysis_data.get("file_size"),
                "file_type": analysis_data.get("file_type"),
                "file_url": analysis_data.get("file_url"),
                "tool_type": "visual_complexity_analyzer",
                "analysis_version": "1.0",
                "overall_score": analysis_data.get("score", 0),
                "complexity_scores": analysis_data.get("complexity", {}),
                "analysis_areas": analysis_data.get("areas", []),
                "recommendations": analysis_data.get("recommendations", []),
                "ai_analysis_summary": analysis_data.get("analysis_summary"),
                "gemini_analysis": analysis_data.get("gemini_analysis"),
                "agent_message": analysis_data.get("agent_message"),
                "visuai_insights": analysis_data.get("visuai_insights"),
                "analysis_duration_ms": analysis_data.get("analysis_duration_ms"),
                "status": "completed",
                "is_favorite": False,
                "tags": []
            }
            
            # Insert into database
            result = self.client.schema("api").table("design_analyses").insert(insert_data).execute()
            
            if result.data:
                logger.info(f"Successfully saved design analysis for user {user_id}")
                return result.data[0]
            else:
                logger.error(f"Failed to save design analysis: {result}")
                return None
                
        except Exception as e:
            logger.error(f"Error saving design analysis: {str(e)}")
            return None
    
    async def save_design_upload(
        self,
        user_id: str,
        filename: str,
        file_size: int,
        file_type: str,
        file_url: str,
        file_hash: Optional[str] = None,
        analysis_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Save information about an uploaded design file
        
        Args:
            user_id: The ID of the user who uploaded the file
            filename: Original filename
            file_size: Size of the file in bytes
            file_type: MIME type of the file
            file_url: URL where the file is stored
            file_hash: Optional hash of the file content
            analysis_id: Optional ID of the related analysis
            
        Returns:
            The saved upload record or None if failed
        """
        try:
            insert_data = {
                "user_id": user_id,
                "original_filename": filename,
                "file_size": file_size,
                "file_type": file_type,
                "file_url": file_url,
                "file_hash": file_hash,
                "upload_source": "design_complexity_analyzer",
                "is_processed": analysis_id is not None,
                "analysis_id": analysis_id
            }
            
            result = self.client.schema("api").table("design_uploads").insert(insert_data).execute()
            
            if result.data:
                logger.info(f"Successfully saved design upload for user {user_id}")
                return result.data[0]
            else:
                logger.error(f"Failed to save design upload: {result}")
                return None
                
        except Exception as e:
            logger.error(f"Error saving design upload: {str(e)}")
            return None
    
    async def get_user_analyses(
        self,
        user_id: str,
        limit: int = 50,
        tool_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get design analyses for a specific user
        
        Args:
            user_id: The ID of the user
            limit: Maximum number of records to return
            tool_type: Optional filter by tool type
            
        Returns:
            List of analysis records
        """
        try:
            query = self.client.schema("api").table("design_analyses").select("*").eq("user_id", user_id)
            
            if tool_type:
                query = query.eq("tool_type", tool_type)
            
            query = query.order("created_at", desc=True).limit(limit)
            
            result = query.execute()
            
            if result.data:
                logger.info(f"Retrieved {len(result.data)} analyses for user {user_id}")
                return result.data
            else:
                logger.info(f"No analyses found for user {user_id}")
                return []
                
        except Exception as e:
            logger.error(f"Error retrieving user analyses: {str(e)}")
            return []
    
    async def get_analysis_by_id(
        self,
        analysis_id: str,
        user_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get a specific analysis by ID (with user verification for security)
        
        Args:
            analysis_id: The ID of the analysis
            user_id: The ID of the user (for security check)
            
        Returns:
            The analysis record or None if not found/not authorized
        """
        try:
            result = self.client.schema("api").table("design_analyses").select("*").eq("id", analysis_id).eq("user_id", user_id).execute()
            
            if result.data and len(result.data) > 0:
                logger.info(f"Retrieved analysis {analysis_id} for user {user_id}")
                return result.data[0]
            else:
                logger.warning(f"Analysis {analysis_id} not found or not authorized for user {user_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error retrieving analysis: {str(e)}")
            return None
    
    async def update_analysis_favorite(
        self,
        analysis_id: str,
        user_id: str,
        is_favorite: bool
    ) -> bool:
        """
        Update the favorite status of an analysis
        
        Args:
            analysis_id: The ID of the analysis
            user_id: The ID of the user (for security check)
            is_favorite: New favorite status
            
        Returns:
            True if successful, False otherwise
        """
        try:
            result = self.client.schema("api").table("design_analyses").update({
                "is_favorite": is_favorite
            }).eq("id", analysis_id).eq("user_id", user_id).execute()
            
            if result.data:
                logger.info(f"Updated favorite status for analysis {analysis_id}")
                return True
            else:
                logger.error(f"Failed to update favorite status for analysis {analysis_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating favorite status: {str(e)}")
            return False
    
    async def delete_analysis(
        self,
        analysis_id: str,
        user_id: str
    ) -> bool:
        """
        Delete an analysis (with user verification for security)
        
        Args:
            analysis_id: The ID of the analysis
            user_id: The ID of the user (for security check)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            result = self.client.schema("api").table("design_analyses").delete().eq("id", analysis_id).eq("user_id", user_id).execute()
            
            if result.data:
                logger.info(f"Deleted analysis {analysis_id} for user {user_id}")
                return True
            else:
                logger.error(f"Failed to delete analysis {analysis_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting analysis: {str(e)}")
            return False


# Global instance
supabase_service = SupabaseService()


def get_supabase_service() -> SupabaseService:
    """
    Dependency function to get the Supabase service instance
    """
    return supabase_service

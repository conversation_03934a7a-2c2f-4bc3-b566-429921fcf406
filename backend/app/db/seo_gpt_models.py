"""
SEO & GPT Optimizer™ - Database Models
Database models for storing SEO GPT Optimizer data and analysis results
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from enum import Enum

from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, ForeignKey, Index, JSON, Enum as SQLEnum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

# Use the same base as the main models
from app.db.models import Base

class ProjectStatus(str, Enum):
    """Status enum for SEO GPT projects."""
    DRAFT = "draft"
    RESEARCHING = "researching"
    WRITING = "writing"
    OPTIMIZING = "optimizing"
    COMPLETED = "completed"
    PUBLISHED = "published"

class ContentType(str, Enum):
    """Content type enum."""
    ARTICLE = "article"
    GUIDE = "guide"
    TUTORIAL = "tutorial"
    COMPARISON = "comparison"
    LIST = "list"
    FAQ = "faq"

class SEOGPTProject(Base):
    """Model for SEO GPT Optimizer projects."""
    
    __tablename__ = "seo_gpt_projects"
    
    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(String(64), unique=True, index=True, nullable=False)
    user_id = Column(String(64), index=True, nullable=True)
    
    # Project basic info
    title = Column(String(255), nullable=False)
    topic = Column(String(255), nullable=False, index=True)
    target_language = Column(String(10), default="es", index=True)
    content_type = Column(SQLEnum(ContentType), default=ContentType.ARTICLE, index=True)
    status = Column(SQLEnum(ProjectStatus), default=ProjectStatus.DRAFT, index=True)
    
    # Content
    content_text = Column(Text, nullable=True)
    content_length = Column(Integer, default=0)
    word_count = Column(Integer, default=0)
    
    # GPT Rank Score
    current_gpt_rank_score = Column(Float, default=0.0, index=True)
    target_gpt_rank_score = Column(Float, default=80.0)
    best_gpt_rank_score = Column(Float, default=0.0)
    
    # SEO Metrics
    seo_score = Column(Float, default=0.0)
    target_keywords = Column(Text, nullable=True)  # JSON string
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    published_at = Column(DateTime, nullable=True)
    
    # Research data (JSON)
    _research_data = Column("research_data", Text)
    
    # Relationships
    content_analyses = relationship("ContentAnalysis", back_populates="project", cascade="all, delete-orphan")
    gpt_rank_history = relationship("GPTRankHistory", back_populates="project", cascade="all, delete-orphan")
    keyword_research = relationship("KeywordResearch", back_populates="project", cascade="all, delete-orphan")
    
    @property
    def research_data(self) -> Optional[Dict[str, Any]]:
        """Get research data as dictionary."""
        if self._research_data:
            try:
                return json.loads(self._research_data)
            except (json.JSONDecodeError, TypeError):
                return None
        return None
    
    @research_data.setter
    def research_data(self, value: Optional[Dict[str, Any]]):
        """Set research data from dictionary."""
        if value is not None:
            self._research_data = json.dumps(value)
        else:
            self._research_data = None
    
    @property
    def target_keywords_list(self) -> List[str]:
        """Get target keywords as list."""
        if self.target_keywords:
            try:
                return json.loads(self.target_keywords)
            except (json.JSONDecodeError, TypeError):
                return []
        return []
    
    @target_keywords_list.setter
    def target_keywords_list(self, value: List[str]):
        """Set target keywords from list."""
        self.target_keywords = json.dumps(value) if value else None

class ContentAnalysis(Base):
    """Model for storing real-time content analysis results."""
    
    __tablename__ = "content_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(String(64), unique=True, index=True, nullable=False)
    project_id = Column(Integer, ForeignKey("seo_gpt_projects.id"), index=True)
    
    # Analysis metadata
    content_version = Column(Integer, default=1)
    analysis_type = Column(String(50), default="full", index=True)  # full, partial, quick
    
    # GPT Rank components
    semantic_similarity_score = Column(Float, default=0.0)
    logical_coherence_score = Column(Float, default=0.0)
    authority_signals_score = Column(Float, default=0.0)
    citability_score = Column(Float, default=0.0)
    clarity_score = Column(Float, default=0.0)
    completeness_score = Column(Float, default=0.0)
    
    # Overall scores
    gpt_rank_score = Column(Float, default=0.0, index=True)
    confidence_level = Column(String(20), default="medium")
    score_grade = Column(String(5), default="C")
    
    # Content statistics
    word_count = Column(Integer, default=0)
    character_count = Column(Integer, default=0)
    paragraph_count = Column(Integer, default=0)
    sentence_count = Column(Integer, default=0)
    
    # Analysis results (JSON)
    _improvement_suggestions = Column("improvement_suggestions", Text)
    _component_details = Column("component_details", Text)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    processing_time = Column(Float, default=0.0)  # Processing time in seconds
    
    # Relationships
    project = relationship("SEOGPTProject", back_populates="content_analyses")
    
    @property
    def improvement_suggestions(self) -> List[Dict[str, Any]]:
        """Get improvement suggestions as list."""
        if self._improvement_suggestions:
            try:
                return json.loads(self._improvement_suggestions)
            except (json.JSONDecodeError, TypeError):
                return []
        return []
    
    @improvement_suggestions.setter
    def improvement_suggestions(self, value: List[Dict[str, Any]]):
        """Set improvement suggestions from list."""
        self._improvement_suggestions = json.dumps(value) if value else None
    
    @property
    def component_details(self) -> Optional[Dict[str, Any]]:
        """Get component details as dictionary."""
        if self._component_details:
            try:
                return json.loads(self._component_details)
            except (json.JSONDecodeError, TypeError):
                return None
        return None
    
    @component_details.setter
    def component_details(self, value: Optional[Dict[str, Any]]):
        """Set component details from dictionary."""
        if value is not None:
            self._component_details = json.dumps(value)
        else:
            self._component_details = None

class GPTRankHistory(Base):
    """Model for tracking GPT Rank score history over time."""
    
    __tablename__ = "gpt_rank_history"
    
    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("seo_gpt_projects.id"), index=True)
    
    # Score tracking
    gpt_rank_score = Column(Float, nullable=False, index=True)
    score_change = Column(Float, default=0.0)  # Change from previous score
    score_grade = Column(String(5), nullable=False)
    
    # Component scores
    semantic_similarity = Column(Float, default=0.0)
    logical_coherence = Column(Float, default=0.0)
    authority_signals = Column(Float, default=0.0)
    citability_score = Column(Float, default=0.0)
    clarity_score = Column(Float, default=0.0)
    completeness_score = Column(Float, default=0.0)
    
    # Context
    content_length = Column(Integer, default=0)
    trigger_event = Column(String(100), nullable=True)  # What triggered this analysis
    user_action = Column(String(100), nullable=True)   # User action that led to this
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    
    # Relationships
    project = relationship("SEOGPTProject", back_populates="gpt_rank_history")

class KeywordResearch(Base):
    """Model for storing keyword research data."""
    
    __tablename__ = "keyword_research"
    
    id = Column(Integer, primary_key=True, index=True)
    research_id = Column(String(64), unique=True, index=True, nullable=False)
    project_id = Column(Integer, ForeignKey("seo_gpt_projects.id"), index=True)
    
    # Research metadata
    topic = Column(String(255), nullable=False, index=True)
    target_language = Column(String(10), default="es")
    research_type = Column(String(50), default="comprehensive")  # comprehensive, quick, competitor
    
    # Research results (JSON)
    _intent_analysis = Column("intent_analysis", Text)
    _google_results = Column("google_results", Text)
    _social_insights = Column("social_insights", Text)
    _gpt_reference = Column("gpt_reference", Text)
    _entities_and_questions = Column("entities_and_questions", Text)
    _content_opportunities = Column("content_opportunities", Text)
    
    # Research metrics
    research_confidence = Column(Float, default=0.0)
    processing_time = Column(Float, default=0.0)
    total_sources_analyzed = Column(Integer, default=0)
    
    # Status
    status = Column(String(20), default="completed", index=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    
    # Relationships
    project = relationship("SEOGPTProject", back_populates="keyword_research")
    
    @property
    def intent_analysis(self) -> Optional[Dict[str, Any]]:
        """Get intent analysis as dictionary."""
        if self._intent_analysis:
            try:
                return json.loads(self._intent_analysis)
            except (json.JSONDecodeError, TypeError):
                return None
        return None
    
    @intent_analysis.setter
    def intent_analysis(self, value: Optional[Dict[str, Any]]):
        """Set intent analysis from dictionary."""
        if value is not None:
            self._intent_analysis = json.dumps(value)
        else:
            self._intent_analysis = None
    
    # Similar properties for other JSON fields...
    @property
    def google_results(self) -> Optional[Dict[str, Any]]:
        if self._google_results:
            try:
                return json.loads(self._google_results)
            except (json.JSONDecodeError, TypeError):
                return None
        return None
    
    @google_results.setter
    def google_results(self, value: Optional[Dict[str, Any]]):
        if value is not None:
            self._google_results = json.dumps(value)
        else:
            self._google_results = None

    @property
    def social_insights(self) -> Optional[Dict[str, Any]]:
        """Get social insights as dictionary."""
        if self._social_insights:
            try:
                return json.loads(self._social_insights)
            except (json.JSONDecodeError, TypeError):
                return None
        return None

    @social_insights.setter
    def social_insights(self, value: Optional[Dict[str, Any]]):
        """Set social insights from dictionary."""
        if value is not None:
            self._social_insights = json.dumps(value)
        else:
            self._social_insights = None

    @property
    def gpt_reference(self) -> Optional[Dict[str, Any]]:
        """Get GPT reference as dictionary."""
        if self._gpt_reference:
            try:
                return json.loads(self._gpt_reference)
            except (json.JSONDecodeError, TypeError):
                return None
        return None

    @gpt_reference.setter
    def gpt_reference(self, value: Optional[Dict[str, Any]]):
        """Set GPT reference from dictionary."""
        if value is not None:
            self._gpt_reference = json.dumps(value)
        else:
            self._gpt_reference = None

    @property
    def entities_and_questions(self) -> Optional[Dict[str, Any]]:
        """Get entities and questions as dictionary."""
        if self._entities_and_questions:
            try:
                return json.loads(self._entities_and_questions)
            except (json.JSONDecodeError, TypeError):
                return None
        return None

    @entities_and_questions.setter
    def entities_and_questions(self, value: Optional[Dict[str, Any]]):
        """Set entities and questions from dictionary."""
        if value is not None:
            self._entities_and_questions = json.dumps(value)
        else:
            self._entities_and_questions = None

    @property
    def content_opportunities(self) -> Optional[Dict[str, Any]]:
        """Get content opportunities as dictionary."""
        if self._content_opportunities:
            try:
                return json.loads(self._content_opportunities)
            except (json.JSONDecodeError, TypeError):
                return None
        return None

    @content_opportunities.setter
    def content_opportunities(self, value: Optional[Dict[str, Any]]):
        """Set content opportunities from dictionary."""
        if value is not None:
            self._content_opportunities = json.dumps(value)
        else:
            self._content_opportunities = None

# Create indexes for better performance
Index('idx_seo_gpt_projects_user_status', SEOGPTProject.user_id, SEOGPTProject.status)
Index('idx_seo_gpt_projects_topic_language', SEOGPTProject.topic, SEOGPTProject.target_language)
Index('idx_content_analyses_project_created', ContentAnalysis.project_id, ContentAnalysis.created_at)
Index('idx_gpt_rank_history_project_created', GPTRankHistory.project_id, GPTRankHistory.created_at)
Index('idx_keyword_research_topic_status', KeywordResearch.topic, KeywordResearch.status)

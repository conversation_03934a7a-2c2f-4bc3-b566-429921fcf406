"""
Coherence Analyzer Module
Analyzes logical coherence and flow of content
"""

import logging
import asyncio
import re
from typing import Dict, Any, List

import google.generativeai as genai
from app.core.config import settings

logger = logging.getLogger(__name__)

class CoherenceAnalyzer:
    """Analyzes logical coherence and flow of content."""
    
    def __init__(self):
        self.gemini_model = None
        
        # Initialize Gemini AI
        if settings.GEMINI_API_KEY:
            try:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("✅ Coherence Analyzer - Gemini AI initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Gemini AI: {e}")
                self.gemini_model = None
        else:
            logger.warning("⚠️ GEMINI_API_KEY not found. Coherence analysis will use rule-based approach.")
    
    async def calculate_logical_coherence(self, content: str) -> float:
        """
        Calculate logical coherence and flow of content.
        
        Args:
            content: Content to analyze
            
        Returns:
            Logical coherence score (0-100)
        """
        try:
            if not self.gemini_model:
                return self._fallback_logical_coherence(content)
            
            prompt = self._build_coherence_analysis_prompt(content)
            
            response = await asyncio.to_thread(
                self.gemini_model.generate_content, prompt
            )
            
            score_text = response.text.strip()
            score_match = re.search(r'\b(\d+(?:\.\d+)?)\b', score_text)
            
            if score_match:
                score = float(score_match.group(1))
                return min(max(score, 0), 100)
            else:
                return self._fallback_logical_coherence(content)
                
        except Exception as e:
            logger.error(f"❌ Logical coherence calculation failed: {str(e)}")
            return self._fallback_logical_coherence(content)
    
    def _build_coherence_analysis_prompt(self, content: str) -> str:
        """Build prompt for coherence analysis."""
        return f"""
        Evalúa la coherencia lógica del siguiente contenido en una escala de 0-100:
        
        {content[:2000]}
        
        Considera:
        - Flujo lógico entre ideas
        - Transiciones entre párrafos
        - Estructura argumentativa
        - Consistencia en el mensaje
        - Progresión clara de conceptos
        - Conexión entre introducción, desarrollo y conclusión
        - Uso apropiado de conectores lógicos
        
        Responde solo con un número entre 0 y 100.
        """
    
    def _fallback_logical_coherence(self, content: str) -> float:
        """Fallback logical coherence calculation."""
        try:
            score = 60.0  # Base score
            
            paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
            sentences = [s.strip() for s in content.split('.') if s.strip()]
            
            # Check paragraph structure
            if len(paragraphs) >= 4:
                score += 20
            elif len(paragraphs) >= 3:
                score += 15
            elif len(paragraphs) >= 2:
                score += 10
            
            # Check for transition words
            transitions = [
                'además', 'por otro lado', 'sin embargo', 'por lo tanto', 'en consecuencia',
                'asimismo', 'también', 'igualmente', 'del mismo modo', 'en contraste',
                'no obstante', 'por el contrario', 'en primer lugar', 'finalmente'
            ]
            transition_count = sum(1 for trans in transitions if trans in content.lower())
            score += min(transition_count * 2, 15)
            
            # Check for logical connectors
            connectors = [
                'porque', 'ya que', 'debido a', 'como resultado', 'por esta razón',
                'dado que', 'puesto que', 'de modo que', 'de manera que', 'así que'
            ]
            connector_count = sum(1 for conn in connectors if conn in content.lower())
            score += min(connector_count * 2, 10)
            
            # Check for consistent topic development
            if len(sentences) > 0:
                # Simple check for topic consistency (repeated key terms)
                words = content.lower().split()
                word_freq = {}
                for word in words:
                    if len(word) > 4:  # Only consider meaningful words
                        word_freq[word] = word_freq.get(word, 0) + 1
                
                # If there are recurring themes (words), it suggests coherence
                recurring_themes = sum(1 for freq in word_freq.values() if freq >= 3)
                score += min(recurring_themes * 1, 10)
            
            return min(score, 100)
            
        except Exception as e:
            logger.error(f"❌ Fallback coherence calculation failed: {str(e)}")
            return 60.0
    
    def analyze_paragraph_flow(self, content: str) -> Dict[str, Any]:
        """Analyze flow between paragraphs."""
        try:
            paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
            
            if len(paragraphs) < 2:
                return {
                    "paragraph_count": len(paragraphs),
                    "flow_score": 50,
                    "flow_quality": "insufficient_paragraphs"
                }
            
            flow_indicators = {
                "has_introduction": False,
                "has_conclusion": False,
                "uses_transitions": False,
                "maintains_topic": False,
                "logical_progression": False
            }
            
            # Check for introduction (first paragraph mentions main topic)
            first_para = paragraphs[0].lower()
            last_para = paragraphs[-1].lower()
            
            # Simple heuristic: introduction often contains general terms
            intro_indicators = ['es', 'son', 'se define', 'consiste', 'significa']
            flow_indicators["has_introduction"] = any(indicator in first_para for indicator in intro_indicators)
            
            # Check for conclusion (last paragraph often summarizes or concludes)
            conclusion_indicators = ['conclusión', 'resumen', 'importante', 'recordar', 'finalmente']
            flow_indicators["has_conclusion"] = any(indicator in last_para for indicator in conclusion_indicators)
            
            # Check for transitions between paragraphs
            transition_words = [
                'además', 'por otro lado', 'sin embargo', 'por lo tanto',
                'asimismo', 'también', 'no obstante', 'en contraste'
            ]
            
            transition_count = 0
            for i in range(1, len(paragraphs)):
                para_start = paragraphs[i][:100].lower()  # Check first 100 chars
                if any(trans in para_start for trans in transition_words):
                    transition_count += 1
            
            flow_indicators["uses_transitions"] = transition_count >= len(paragraphs) * 0.3
            
            # Check topic maintenance (simplified)
            all_text = ' '.join(paragraphs).lower()
            words = all_text.split()
            word_freq = {}
            for word in words:
                if len(word) > 5:  # Focus on substantial words
                    word_freq[word] = word_freq.get(word, 0) + 1
            
            # If key terms appear throughout, topic is maintained
            recurring_words = [word for word, freq in word_freq.items() if freq >= 3]
            flow_indicators["maintains_topic"] = len(recurring_words) >= 2
            
            # Check logical progression (each paragraph builds on previous)
            # Simplified: check if paragraphs have similar length (indicates planning)
            para_lengths = [len(p) for p in paragraphs]
            avg_length = sum(para_lengths) / len(para_lengths)
            length_variance = sum((length - avg_length) ** 2 for length in para_lengths) / len(para_lengths)
            flow_indicators["logical_progression"] = length_variance < (avg_length * 0.5) ** 2
            
            # Calculate flow score
            flow_score = sum(flow_indicators.values()) * 20  # Each indicator worth 20 points
            
            return {
                "paragraph_count": len(paragraphs),
                "flow_indicators": flow_indicators,
                "flow_score": flow_score,
                "transition_count": transition_count,
                "flow_quality": self._classify_flow_quality(flow_score),
                "avg_paragraph_length": avg_length,
                "paragraph_length_consistency": "high" if length_variance < (avg_length * 0.3) ** 2 else "medium" if length_variance < (avg_length * 0.6) ** 2 else "low"
            }
            
        except Exception as e:
            logger.error(f"❌ Paragraph flow analysis failed: {str(e)}")
            return {
                "flow_score": 50,
                "flow_quality": "unknown",
                "error": str(e)
            }
    
    def _classify_flow_quality(self, flow_score: float) -> str:
        """Classify flow quality based on score."""
        if flow_score >= 80:
            return "excellent"
        elif flow_score >= 60:
            return "good"
        elif flow_score >= 40:
            return "fair"
        else:
            return "poor"
    
    def analyze_argument_structure(self, content: str) -> Dict[str, Any]:
        """Analyze the argumentative structure of content."""
        try:
            content_lower = content.lower()
            
            structure_elements = {
                "has_thesis": False,
                "has_evidence": False,
                "has_examples": False,
                "has_counterarguments": False,
                "has_reasoning": False
            }
            
            # Check for thesis/main argument
            thesis_indicators = [
                'el objetivo', 'el propósito', 'la tesis', 'el argumento principal',
                'se sostiene que', 'se argumenta que', 'la hipótesis'
            ]
            structure_elements["has_thesis"] = any(indicator in content_lower for indicator in thesis_indicators)
            
            # Check for evidence
            evidence_indicators = [
                'según', 'de acuerdo con', 'los datos muestran', 'la investigación indica',
                'estudios revelan', 'estadísticas', 'evidencia', 'pruebas'
            ]
            structure_elements["has_evidence"] = any(indicator in content_lower for indicator in evidence_indicators)
            
            # Check for examples
            example_indicators = ['ejemplo', 'por ejemplo', 'como', 'tal como', 'ilustra']
            structure_elements["has_examples"] = any(indicator in content_lower for indicator in example_indicators)
            
            # Check for counterarguments
            counter_indicators = [
                'sin embargo', 'no obstante', 'por el contrario', 'aunque',
                'a pesar de', 'contrariamente', 'por otro lado'
            ]
            structure_elements["has_counterarguments"] = any(indicator in content_lower for indicator in counter_indicators)
            
            # Check for reasoning
            reasoning_indicators = [
                'porque', 'ya que', 'debido a', 'por esta razón', 'como resultado',
                'por lo tanto', 'en consecuencia', 'de modo que'
            ]
            structure_elements["has_reasoning"] = any(indicator in content_lower for indicator in reasoning_indicators)
            
            # Calculate structure score
            structure_score = sum(structure_elements.values()) * 20
            
            return {
                "structure_elements": structure_elements,
                "structure_score": structure_score,
                "argument_strength": self._classify_argument_strength(structure_score),
                "missing_elements": [element for element, present in structure_elements.items() if not present]
            }
            
        except Exception as e:
            logger.error(f"❌ Argument structure analysis failed: {str(e)}")
            return {
                "structure_score": 50,
                "argument_strength": "unknown",
                "error": str(e)
            }
    
    def _classify_argument_strength(self, structure_score: float) -> str:
        """Classify argument strength based on structure score."""
        if structure_score >= 80:
            return "very_strong"
        elif structure_score >= 60:
            return "strong"
        elif structure_score >= 40:
            return "moderate"
        else:
            return "weak"
    
    def analyze_sentence_coherence(self, content: str) -> Dict[str, Any]:
        """Analyze coherence at sentence level."""
        try:
            sentences = [s.strip() for s in content.split('.') if s.strip() and len(s.strip()) > 10]
            
            if len(sentences) < 2:
                return {
                    "sentence_count": len(sentences),
                    "coherence_score": 50,
                    "coherence_quality": "insufficient_sentences"
                }
            
            # Analyze sentence length consistency
            sentence_lengths = [len(s.split()) for s in sentences]
            avg_length = sum(sentence_lengths) / len(sentence_lengths)
            
            # Check for varied sentence structure
            length_variance = sum((length - avg_length) ** 2 for length in sentence_lengths) / len(sentence_lengths)
            
            # Check for sentence connectors
            connector_count = 0
            for sentence in sentences:
                sentence_lower = sentence.lower()
                if any(conn in sentence_lower[:20] for conn in ['además', 'también', 'asimismo', 'por otro lado']):
                    connector_count += 1
            
            # Calculate coherence metrics
            coherence_metrics = {
                "avg_sentence_length": avg_length,
                "length_variance": length_variance,
                "connector_usage": connector_count / len(sentences) if sentences else 0,
                "length_consistency": "high" if length_variance < 25 else "medium" if length_variance < 100 else "low"
            }
            
            # Calculate overall sentence coherence score
            coherence_score = 50  # Base score
            
            # Optimal sentence length (15-25 words)
            if 15 <= avg_length <= 25:
                coherence_score += 20
            elif 10 <= avg_length <= 30:
                coherence_score += 10
            
            # Good variance indicates varied structure
            if 25 <= length_variance <= 100:
                coherence_score += 15
            
            # Connector usage
            if coherence_metrics["connector_usage"] >= 0.3:
                coherence_score += 15
            
            return {
                "sentence_count": len(sentences),
                "coherence_metrics": coherence_metrics,
                "coherence_score": min(coherence_score, 100),
                "coherence_quality": self._classify_sentence_coherence(coherence_score)
            }
            
        except Exception as e:
            logger.error(f"❌ Sentence coherence analysis failed: {str(e)}")
            return {
                "coherence_score": 50,
                "coherence_quality": "unknown",
                "error": str(e)
            }
    
    def _classify_sentence_coherence(self, coherence_score: float) -> str:
        """Classify sentence coherence quality."""
        if coherence_score >= 80:
            return "excellent"
        elif coherence_score >= 65:
            return "good"
        elif coherence_score >= 50:
            return "fair"
        else:
            return "poor"

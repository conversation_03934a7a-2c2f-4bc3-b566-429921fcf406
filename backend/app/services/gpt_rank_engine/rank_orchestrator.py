"""
Rank Orchestrator Module
Main orchestrator for GPT Rank Engine Service
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional

from .semantic_analyzer import SemanticAnalyzer
from .coherence_analyzer import CoherenceAnalyzer
from .authority_analyzer import AuthorityAnalyzer
from .citability_analyzer import Citability<PERSON><PERSON>yzer
from .clarity_analyzer import Clarity<PERSON>nal<PERSON>zer
from .completeness_analyzer import CompletenessAnalyzer
from .improvement_suggestions import ImprovementSuggestionGenerator

logger = logging.getLogger(__name__)

class GPTRankEngineService:
    """
    Main GPT Rank Engine for SEO & GPT Optimizer™
    
    Calculates a comprehensive score (0-100) indicating how likely content is to be:
    - Included in LLM training datasets
    - Cited by AI models
    - Considered authoritative by AI systems
    """
    
    def __init__(self):
        # Initialize all analyzers
        self.semantic_analyzer = SemanticAnalyzer()
        self.coherence_analyzer = CoherenceAnalyzer()
        self.authority_analyzer = AuthorityAnalyzer()
        self.citability_analyzer = CitabilityAnalyzer()
        self.clarity_analyzer = ClarityAnalyzer()
        self.completeness_analyzer = CompletenessAnalyzer()
        self.suggestion_generator = ImprovementSuggestionGenerator()
        
        # Initialize scoring weights
        self.scoring_weights = {
            "semantic_similarity": 0.25,      # Similarity to authoritative sources
            "logical_coherence": 0.20,        # Logical flow and structure
            "authority_signals": 0.20,        # Authority and expertise indicators
            "citability_score": 0.15,         # How citable the content is
            "clarity_score": 0.10,            # Clarity and readability
            "completeness_score": 0.10        # Completeness of information
        }
        
        logger.info("✅ GPT Rank Engine Service initialized successfully")
    
    async def calculate_gpt_rank_score(
        self, 
        content: str, 
        topic: str = "",
        reference_sources: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Calculate comprehensive GPT Rank Score for content.
        
        Args:
            content: The content to analyze
            topic: Main topic/keyword for context
            reference_sources: Optional list of reference source texts for comparison
            
        Returns:
            Complete GPT Rank analysis with score and breakdown
        """
        try:
            logger.info(f"🧮 Calculating GPT Rank Score for content (length: {len(content)} chars)")
            
            # Step 1: Semantic Similarity Analysis
            semantic_score = await self.semantic_analyzer.calculate_semantic_similarity(
                content, topic, reference_sources
            )
            
            # Step 2: Logical Coherence Analysis
            coherence_score = await self.coherence_analyzer.calculate_logical_coherence(content)
            
            # Step 3: Authority Signals Analysis
            authority_score = self.authority_analyzer.calculate_authority_signals(content, topic)
            
            # Step 4: Citability Analysis
            citability_score = self.citability_analyzer.calculate_citability_score(content)
            
            # Step 5: Clarity Analysis
            clarity_score = self.clarity_analyzer.calculate_clarity_score(content)
            
            # Step 6: Completeness Analysis
            completeness_score = await self.completeness_analyzer.calculate_completeness_score(content, topic)
            
            # Calculate weighted final score
            component_scores = {
                "semantic_similarity": semantic_score,
                "logical_coherence": coherence_score,
                "authority_signals": authority_score,
                "citability_score": citability_score,
                "clarity_score": clarity_score,
                "completeness_score": completeness_score
            }
            
            final_score = sum(
                score * self.scoring_weights[component] 
                for component, score in component_scores.items()
            )
            
            # Generate improvement suggestions
            improvement_suggestions = self.suggestion_generator.generate_improvement_suggestions(
                component_scores, content, topic
            )
            
            # Generate action plan
            action_plan = self.suggestion_generator.generate_action_plan(improvement_suggestions)
            
            # Calculate confidence level
            confidence_level = self._calculate_confidence_level(component_scores)
            
            # Get detailed analysis
            detailed_analysis = await self._get_detailed_analysis(content, topic)
            
            result = {
                "status": "success",
                "gpt_rank_score": round(final_score, 1),
                "score_grade": self._get_score_grade(final_score),
                "confidence_level": confidence_level,
                "component_scores": {
                    component: round(score, 1) 
                    for component, score in component_scores.items()
                },
                "scoring_weights": self.scoring_weights,
                "content_stats": self._calculate_content_stats(content),
                "improvement_suggestions": improvement_suggestions,
                "action_plan": action_plan,
                "detailed_analysis": detailed_analysis,
                "analysis_timestamp": asyncio.get_event_loop().time()
            }
            
            logger.info(f"✅ GPT Rank Score calculated: {final_score:.1f}/100")
            return result
            
        except Exception as e:
            logger.error(f"❌ GPT Rank calculation failed: {str(e)}")
            return {
                "status": "error",
                "error_message": f"GPT Rank calculation failed: {str(e)}",
                "gpt_rank_score": 0.0
            }
    
    async def _get_detailed_analysis(self, content: str, topic: str) -> Dict[str, Any]:
        """Get detailed analysis from all components."""
        try:
            detailed_analysis = {}
            
            # Get semantic analysis details
            semantic_details = self.semantic_analyzer.analyze_terminology_precision(content, topic)
            detailed_analysis["semantic_analysis"] = semantic_details
            
            # Get coherence analysis details
            coherence_details = self.coherence_analyzer.analyze_paragraph_flow(content)
            detailed_analysis["coherence_analysis"] = coherence_details
            
            # Get authority analysis details
            authority_details = self.authority_analyzer.analyze_expertise_indicators(content)
            detailed_analysis["authority_analysis"] = authority_details
            
            # Get citability analysis details
            citability_details = self.citability_analyzer.analyze_quotable_content(content)
            detailed_analysis["citability_analysis"] = citability_details
            
            # Get clarity analysis details
            clarity_details = self.clarity_analyzer.analyze_readability_metrics(content)
            detailed_analysis["clarity_analysis"] = clarity_details
            
            # Get completeness analysis details
            completeness_details = self.completeness_analyzer.analyze_coverage_dimensions(content, topic)
            detailed_analysis["completeness_analysis"] = completeness_details
            
            return detailed_analysis
            
        except Exception as e:
            logger.error(f"❌ Detailed analysis failed: {str(e)}")
            return {"error": str(e)}
    
    def _calculate_content_stats(self, content: str) -> Dict[str, Any]:
        """Calculate basic content statistics."""
        try:
            words = content.split()
            sentences = [s.strip() for s in content.split('.') if s.strip()]
            paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
            
            return {
                "word_count": len(words),
                "character_count": len(content),
                "paragraph_count": len(paragraphs),
                "sentence_count": len(sentences),
                "avg_words_per_sentence": len(words) / len(sentences) if sentences else 0,
                "avg_sentences_per_paragraph": len(sentences) / len(paragraphs) if paragraphs else 0,
                "reading_time_minutes": len(words) / 200  # Assuming 200 words per minute
            }
        except Exception as e:
            logger.error(f"❌ Content stats calculation failed: {str(e)}")
            return {
                "word_count": 0,
                "character_count": 0,
                "paragraph_count": 0,
                "sentence_count": 0
            }
    
    def _calculate_confidence_level(self, component_scores: Dict[str, float]) -> str:
        """Calculate confidence level of the analysis."""
        try:
            scores = list(component_scores.values())
            avg_score = sum(scores) / len(scores)
            
            # Calculate variance
            variance = sum((score - avg_score) ** 2 for score in scores) / len(scores)
            
            # High confidence: high average score and low variance
            if avg_score >= 80 and variance < 100:
                return "high"
            elif avg_score >= 60 and variance < 200:
                return "medium"
            else:
                return "low"
        except:
            return "medium"
    
    def _get_score_grade(self, score: float) -> str:
        """Convert numeric score to letter grade."""
        if score >= 90:
            return "A+"
        elif score >= 85:
            return "A"
        elif score >= 80:
            return "A-"
        elif score >= 75:
            return "B+"
        elif score >= 70:
            return "B"
        elif score >= 65:
            return "B-"
        elif score >= 60:
            return "C+"
        elif score >= 55:
            return "C"
        elif score >= 50:
            return "C-"
        elif score >= 40:
            return "D"
        else:
            return "F"
    
    def get_component_weights(self) -> Dict[str, float]:
        """Get current component weights."""
        return self.scoring_weights.copy()
    
    def update_component_weights(self, new_weights: Dict[str, float]) -> bool:
        """Update component weights (must sum to 1.0)."""
        try:
            if abs(sum(new_weights.values()) - 1.0) < 0.01:  # Allow small floating point errors
                self.scoring_weights.update(new_weights)
                logger.info("✅ Component weights updated successfully")
                return True
            else:
                logger.error("❌ Component weights must sum to 1.0")
                return False
        except Exception as e:
            logger.error(f"❌ Failed to update component weights: {str(e)}")
            return False
    
    async def analyze_content_evolution(
        self, 
        content_versions: List[str], 
        topic: str
    ) -> Dict[str, Any]:
        """Analyze how content has evolved across versions."""
        try:
            if len(content_versions) < 2:
                return {"error": "Need at least 2 content versions for evolution analysis"}
            
            evolution_data = []
            
            for i, content in enumerate(content_versions):
                analysis = await self.calculate_gpt_rank_score(content, topic)
                evolution_data.append({
                    "version": i + 1,
                    "gpt_rank_score": analysis.get("gpt_rank_score", 0),
                    "component_scores": analysis.get("component_scores", {}),
                    "content_length": len(content)
                })
            
            # Calculate improvements
            improvements = []
            for i in range(1, len(evolution_data)):
                current = evolution_data[i]
                previous = evolution_data[i-1]
                
                score_change = current["gpt_rank_score"] - previous["gpt_rank_score"]
                improvements.append({
                    "from_version": i,
                    "to_version": i + 1,
                    "score_change": round(score_change, 1),
                    "improvement_type": "improvement" if score_change > 0 else "decline" if score_change < 0 else "no_change"
                })
            
            return {
                "evolution_data": evolution_data,
                "improvements": improvements,
                "total_improvement": evolution_data[-1]["gpt_rank_score"] - evolution_data[0]["gpt_rank_score"],
                "best_version": max(evolution_data, key=lambda x: x["gpt_rank_score"])["version"]
            }
            
        except Exception as e:
            logger.error(f"❌ Content evolution analysis failed: {str(e)}")
            return {"error": str(e)}

"""
Semantic Analyzer Module
Analyzes semantic similarity to authoritative sources
"""

import logging
import asyncio
import re
from typing import Dict, Any, List, Optional

import google.generativeai as genai
from app.core.config import settings

logger = logging.getLogger(__name__)

class SemanticAnalyzer:
    """Analyzes semantic similarity to authoritative sources."""
    
    def __init__(self):
        self.gemini_model = None
        
        # Initialize Gemini AI
        if settings.GEMINI_API_KEY:
            try:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("✅ Semantic Analyzer - Gemini AI initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Gemini AI: {e}")
                self.gemini_model = None
        else:
            logger.warning("⚠️ GEMINI_API_KEY not found. Semantic analysis will use fallback mode.")
    
    async def calculate_semantic_similarity(
        self, 
        content: str, 
        topic: str, 
        reference_sources: Optional[List[str]] = None
    ) -> float:
        """
        Calculate semantic similarity to authoritative sources.
        
        Args:
            content: Content to analyze
            topic: Main topic for context
            reference_sources: Optional reference source texts
            
        Returns:
            Semantic similarity score (0-100)
        """
        try:
            if not self.gemini_model:
                return self._fallback_semantic_similarity(content, topic)
            
            prompt = self._build_semantic_analysis_prompt(content, topic, reference_sources)
            
            response = await asyncio.to_thread(
                self.gemini_model.generate_content, prompt
            )
            
            # Extract numeric score
            score_text = response.text.strip()
            score_match = re.search(r'\b(\d+(?:\.\d+)?)\b', score_text)
            
            if score_match:
                score = float(score_match.group(1))
                return min(max(score, 0), 100)  # Clamp between 0-100
            else:
                return self._fallback_semantic_similarity(content, topic)
                
        except Exception as e:
            logger.error(f"❌ Semantic similarity calculation failed: {str(e)}")
            return self._fallback_semantic_similarity(content, topic)
    
    def _build_semantic_analysis_prompt(
        self, 
        content: str, 
        topic: str, 
        reference_sources: Optional[List[str]] = None
    ) -> str:
        """Build prompt for semantic similarity analysis."""
        base_prompt = f"""
        Analiza la similitud semántica del siguiente contenido con fuentes autoritativas típicas sobre "{topic}".
        
        Contenido a analizar:
        {content[:2000]}
        
        Evalúa en una escala de 0-100:
        - Similitud con el estilo de Wikipedia
        - Similitud con publicaciones académicas
        - Similitud con documentación técnica oficial
        - Uso de terminología precisa y estándar
        - Estructura típica de fuentes autoritativas
        - Neutralidad y objetividad del tono
        - Uso de evidencia y referencias
        
        Responde solo con un número entre 0 y 100.
        """
        
        if reference_sources:
            reference_text = "\n".join(reference_sources[:2])  # Limit to avoid token limits
            base_prompt += f"""
            
            Compara también con estas fuentes de referencia:
            {reference_text[:1000]}
            """
        
        return base_prompt
    
    def _fallback_semantic_similarity(self, content: str, topic: str) -> float:
        """Fallback semantic similarity calculation using rule-based approach."""
        try:
            score = 50.0  # Base score
            content_lower = content.lower()
            
            # Check for topic relevance
            if topic and topic.lower() in content_lower:
                score += 10
            
            # Check for formal language indicators
            formal_indicators = [
                'según', 'de acuerdo con', 'estudios muestran', 'investigación indica',
                'se define como', 'consiste en', 'se caracteriza por', 'es importante',
                'los datos sugieren', 'la evidencia muestra', 'análisis revela'
            ]
            formal_count = sum(1 for indicator in formal_indicators if indicator in content_lower)
            score += min(formal_count * 3, 20)
            
            # Check for structure indicators (Wikipedia-like)
            structure_indicators = [
                content.count('\n\n') >= 2,  # Multiple paragraphs
                '1.' in content or '2.' in content,  # Numbered lists
                '•' in content or '-' in content,  # Bullet points
                len(content.split('.')) >= 5  # Multiple sentences
            ]
            structure_score = sum(structure_indicators) * 5
            score += structure_score
            
            # Check for citations or references
            citation_indicators = [
                'fuente:', 'referencia:', 'según', 'basado en', 'de acuerdo con',
                'estudio de', 'investigación de', 'datos de'
            ]
            citation_count = sum(1 for indicator in citation_indicators if indicator in content_lower)
            score += min(citation_count * 4, 15)
            
            # Check for technical terminology
            technical_patterns = [
                r'\b[a-zA-Z]+ción\b',  # Words ending in -ción
                r'\b[a-zA-Z]+miento\b',  # Words ending in -miento
                r'\b[a-zA-Z]{10,}\b'  # Long words (often technical)
            ]
            
            technical_count = 0
            for pattern in technical_patterns:
                technical_count += len(re.findall(pattern, content))
            
            score += min(technical_count * 0.5, 10)
            
            # Check for neutral tone (avoid promotional language)
            promotional_words = [
                'increíble', 'amazing', 'fantástico', 'mejor del mundo', 'revolucionario',
                'único', 'exclusivo', 'garantizado', 'gratis', 'oferta especial'
            ]
            promotional_count = sum(1 for word in promotional_words if word in content_lower)
            score -= promotional_count * 3  # Penalty for promotional language
            
            # Check for balanced perspective
            balance_indicators = [
                'sin embargo', 'no obstante', 'por otro lado', 'aunque', 'pero',
                'es importante notar', 'cabe mencionar', 'hay que considerar'
            ]
            balance_count = sum(1 for indicator in balance_indicators if indicator in content_lower)
            score += min(balance_count * 3, 10)
            
            return min(max(score, 0), 100)
            
        except Exception as e:
            logger.error(f"❌ Fallback semantic similarity failed: {str(e)}")
            return 50.0
    
    def analyze_terminology_precision(self, content: str, topic: str) -> Dict[str, Any]:
        """Analyze precision of terminology used in content."""
        try:
            content_lower = content.lower()
            
            # Count technical terms
            technical_patterns = [
                r'\b[a-zA-Z]+ción\b',
                r'\b[a-zA-Z]+miento\b',
                r'\b[a-zA-Z]+idad\b',
                r'\b[a-zA-Z]+ismo\b'
            ]
            
            technical_terms = []
            for pattern in technical_patterns:
                matches = re.findall(pattern, content)
                technical_terms.extend(matches)
            
            # Count precise definitions
            definition_patterns = [
                'se define como', 'consiste en', 'es un', 'es una',
                'significa', 'se refiere a', 'se entiende por'
            ]
            definition_count = sum(1 for pattern in definition_patterns if pattern in content_lower)
            
            # Count specific vs vague language
            specific_indicators = [
                'específicamente', 'concretamente', 'en particular', 'exactamente',
                'precisamente', 'literalmente'
            ]
            vague_indicators = [
                'algo', 'cierto', 'algunos', 'varios', 'muchos', 'bastante',
                'más o menos', 'aproximadamente'
            ]
            
            specific_count = sum(1 for indicator in specific_indicators if indicator in content_lower)
            vague_count = sum(1 for indicator in vague_indicators if indicator in content_lower)
            
            # Calculate precision score
            precision_score = 0
            if technical_terms:
                precision_score += min(len(set(technical_terms)) * 2, 20)
            if definition_count > 0:
                precision_score += min(definition_count * 5, 25)
            if specific_count > vague_count:
                precision_score += 15
            
            return {
                "technical_terms_count": len(set(technical_terms)),
                "technical_terms": list(set(technical_terms))[:10],
                "definition_count": definition_count,
                "specific_language_count": specific_count,
                "vague_language_count": vague_count,
                "precision_score": min(precision_score, 100),
                "terminology_level": self._classify_terminology_level(precision_score)
            }
            
        except Exception as e:
            logger.error(f"❌ Terminology precision analysis failed: {str(e)}")
            return {
                "precision_score": 50,
                "terminology_level": "medium",
                "error": str(e)
            }
    
    def _classify_terminology_level(self, precision_score: float) -> str:
        """Classify terminology precision level."""
        if precision_score >= 80:
            return "highly_precise"
        elif precision_score >= 60:
            return "precise"
        elif precision_score >= 40:
            return "moderate"
        else:
            return "imprecise"
    
    def analyze_source_similarity_patterns(self, content: str) -> Dict[str, Any]:
        """Analyze patterns that indicate similarity to authoritative sources."""
        try:
            content_lower = content.lower()
            
            patterns = {
                "wikipedia_style": {
                    "score": 0,
                    "indicators": []
                },
                "academic_style": {
                    "score": 0,
                    "indicators": []
                },
                "technical_documentation": {
                    "score": 0,
                    "indicators": []
                },
                "news_article": {
                    "score": 0,
                    "indicators": []
                }
            }
            
            # Wikipedia style indicators
            wiki_indicators = [
                ('multiple_paragraphs', content.count('\n\n') >= 3),
                ('neutral_tone', not any(word in content_lower for word in ['yo', 'mi', 'nosotros'])),
                ('structured_content', any(marker in content for marker in ['1.', '2.', '•', '-'])),
                ('cross_references', 'véase también' in content_lower or 'ver también' in content_lower)
            ]
            
            for indicator, present in wiki_indicators:
                if present:
                    patterns["wikipedia_style"]["score"] += 25
                    patterns["wikipedia_style"]["indicators"].append(indicator)
            
            # Academic style indicators
            academic_indicators = [
                ('formal_language', any(word in content_lower for word in ['según', 'de acuerdo con', 'investigación'])),
                ('citations', any(word in content_lower for word in ['estudio', 'análisis', 'datos'])),
                ('methodology', any(word in content_lower for word in ['método', 'procedimiento', 'técnica'])),
                ('conclusions', any(word in content_lower for word in ['conclusión', 'resultado', 'hallazgo']))
            ]
            
            for indicator, present in academic_indicators:
                if present:
                    patterns["academic_style"]["score"] += 25
                    patterns["academic_style"]["indicators"].append(indicator)
            
            # Technical documentation indicators
            tech_indicators = [
                ('step_by_step', any(word in content for word in ['Paso 1', 'Paso 2', 'Primero', 'Segundo'])),
                ('technical_terms', len(re.findall(r'\b[a-zA-Z]{8,}\b', content)) > 5),
                ('precise_language', any(word in content_lower for word in ['específicamente', 'exactamente'])),
                ('examples', 'ejemplo' in content_lower or 'por ejemplo' in content_lower)
            ]
            
            for indicator, present in tech_indicators:
                if present:
                    patterns["technical_documentation"]["score"] += 25
                    patterns["technical_documentation"]["indicators"].append(indicator)
            
            # News article indicators
            news_indicators = [
                ('current_events', any(word in content_lower for word in ['recientemente', 'actualmente', 'hoy'])),
                ('quotes', '"' in content or '"' in content),
                ('attribution', any(word in content_lower for word in ['según', 'declaró', 'afirmó'])),
                ('factual_reporting', any(word in content_lower for word in ['reporta', 'informa', 'confirma']))
            ]
            
            for indicator, present in news_indicators:
                if present:
                    patterns["news_article"]["score"] += 25
                    patterns["news_article"]["indicators"].append(indicator)
            
            # Determine dominant pattern
            dominant_pattern = max(patterns.keys(), key=lambda k: patterns[k]["score"])
            
            return {
                "patterns": patterns,
                "dominant_pattern": dominant_pattern,
                "dominant_score": patterns[dominant_pattern]["score"],
                "overall_authority_score": sum(pattern["score"] for pattern in patterns.values()) / 4
            }
            
        except Exception as e:
            logger.error(f"❌ Source similarity pattern analysis failed: {str(e)}")
            return {
                "overall_authority_score": 50,
                "dominant_pattern": "unknown",
                "error": str(e)
            }

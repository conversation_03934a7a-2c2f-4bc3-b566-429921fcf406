"""
Image Generator Service using Ideogram AI v3 API.
Based on poster_service.py but adapted for general image generation without prompt modifications.
Gives users complete creative freedom by passing their prompts exactly as written.
"""

import logging
import httpx
import random
from typing import Optional, List, Dict, Any
from fastapi import UploadFile

logger = logging.getLogger(__name__)

class ImageGeneratorService:
    """Service for creating images using Ideogram AI v3 model with complete user creative freedom."""

    def __init__(self):
        self.api_key = "1rrDHIqxD4vl6tSucVKy6AIDtb_ZUnuOZ_stZOJXfGpAZE7UfyCuB6R9K_hENxWlp-su3uNDY6dC95-geYAO1g"
        self.base_url = "https://api.ideogram.ai/v1"
        
    async def generate_image(
        self,
        prompt: str,
        resolution: Optional[str] = None,
        aspect_ratio: Optional[str] = None,
        rendering_speed: str = "DEFAULT",
        magic_prompt: str = "AUTO",
        negative_prompt: Optional[str] = None,
        num_images: int = 1,
        style_type: str = "GENERAL"
    ) -> Dict[str, Any]:
        """
        Generate an image using Ideogram AI v3.

        Args:
            prompt: Description of the image to create (NO MODIFICATIONS - user has total freedom)
            resolution: Ideogram resolution (e.g., "1024x1024")
            aspect_ratio: Alternative to resolution (e.g., "1x1")
            rendering_speed: TURBO/DEFAULT/QUALITY
            magic_prompt: AUTO/ON/OFF for prompt enhancement
            negative_prompt: What to exclude from image
            num_images: Number of images to generate (1-8)
            style_type: AUTO/GENERAL/REALISTIC/DESIGN

        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}
        
        try:
            # NO PROMPT MODIFICATIONS - Use user's prompt exactly as provided
            # This gives users complete creative freedom
            user_prompt = prompt

            # Set default resolution if neither resolution nor aspect_ratio is provided
            if not resolution and not aspect_ratio:
                resolution = "1024x1024"  # Default to square format

            # Generate random seed automatically (0-2147483647)
            random_seed = random.randint(0, 2147483647)

            # Prepare form data for multipart/form-data request
            files = {
                "prompt": (None, user_prompt),
                "rendering_speed": (None, rendering_speed),
                "magic_prompt": (None, magic_prompt),
                "num_images": (None, str(num_images)),
                "style_type": (None, style_type),
                "seed": (None, str(random_seed))
            }

            # Add optional parameters
            if resolution and not aspect_ratio:
                files["resolution"] = (None, resolution)
            elif aspect_ratio and not resolution:
                files["aspect_ratio"] = (None, aspect_ratio)

            if negative_prompt:
                files["negative_prompt"] = (None, negative_prompt)

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🎨 Generating image with Ideogram: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/ideogram-v3/generate",
                    files=files,
                    headers=headers
                )
                
                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram API error {response.status_code}: {error_text}")
                    return {
                        "success": False,
                        "error": f"Ideogram API error: {response.status_code} - {error_text}"
                    }

                result = response.json()

                # Process Ideogram response (exact same structure as poster service)
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]

                    # Check if image is safe
                    if not image_data.get("is_image_safe", True):
                        logger.warning("Generated image was flagged as unsafe")
                        return {"success": False, "error": "Generated image was flagged as unsafe content"}

                    image_url = image_data.get("url")
                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt", user_prompt),
                            "metadata": {
                                "model": "ideogram-v3",
                                "resolution": image_data.get("resolution"),
                                "style_type": image_data.get("style_type"),
                                "seed": image_data.get("seed"),
                                "original_prompt": prompt,
                                "user_prompt": user_prompt,  # No modifications made
                                "rendering_speed": rendering_speed,
                                "magic_prompt": magic_prompt
                            }
                        }
                    else:
                        logger.error(f"No image URL found in response: {image_data}")
                        return {"success": False, "error": "No image URL in response"}
                else:
                    logger.error(f"No data array in response: {result}")
                    return {"success": False, "error": "No image data in response"}

        except httpx.TimeoutException:
            logger.error("Timeout calling Ideogram API")
            return {
                "success": False,
                "error": "Request timeout - please try again"
            }
        except Exception as e:
            logger.error(f"Error calling Ideogram API: {e}", exc_info=True)
            return {
                "success": False,
                "error": f"Error generating image: {str(e)}"
            }

    async def multi_turn_edit(
        self,
        previous_response_id: str,
        edit_prompt: str
    ) -> Dict[str, Any]:
        """
        Edit an existing image using multi-turn generation.
        Since Ideogram doesn't support multi-turn editing, we generate a new image.

        Args:
            previous_response_id: ID of the previous response (for metadata)
            edit_prompt: Description of the changes to make

        Returns:
            Dict with success status, image data, and metadata
        """
        try:
            # Since Ideogram doesn't support multi-turn editing, we generate a new image
            # with the edit prompt as the main prompt (NO MODIFICATIONS)
            logger.info(f"🔄 Generating new image based on edit: {edit_prompt[:100]}...")

            result = await self.generate_image(
                prompt=edit_prompt,  # Use edit prompt directly without modifications
                rendering_speed="DEFAULT",
                magic_prompt="ON"  # Use magic prompt for better results
            )

            if result["success"]:
                # Update metadata to indicate this was an edit
                if "metadata" in result:
                    result["metadata"]["type"] = "multi_turn_edit"
                    result["metadata"]["edit_prompt"] = edit_prompt
                    result["metadata"]["previous_response_id"] = previous_response_id

            return result

        except Exception as e:
            logger.error(f"Error in multi_turn_edit: {e}", exc_info=True)
            return {
                "success": False,
                "error": f"Error editing image: {str(e)}"
            }

    async def edit_with_references(
        self,
        prompt: str,
        reference_images: List[UploadFile],
        resolution: Optional[str] = None,
        aspect_ratio: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate image using reference images.
        Note: Ideogram doesn't support reference images directly, so we enhance the prompt based on image count.

        Args:
            prompt: Description of the image to create
            reference_images: List of reference images (used for prompt enhancement)
            resolution: Ideogram resolution (e.g., "1024x1024")
            aspect_ratio: Alternative to resolution (e.g., "1x1")

        Returns:
            Dict with success status, image data, and metadata
        """
        try:
            # Since Ideogram doesn't support reference images, we use the prompt as-is
            # NO AUTOMATIC ENHANCEMENTS - user has complete control
            logger.info(f"🖼️ Generating image with {len(reference_images)} references: {prompt[:100]}...")

            # Generate image with user's exact prompt and higher quality settings
            result = await self.generate_image(
                prompt=prompt,  # Use user's prompt exactly as provided
                resolution=resolution,
                aspect_ratio=aspect_ratio,
                rendering_speed="QUALITY",  # Use highest quality for reference-based generation
                magic_prompt="ON",  # Enable magic prompt for better results
                style_type="GENERAL"  # Use general style for maximum flexibility
            )

            if result["success"]:
                # Update metadata to indicate this was a reference-based generation
                if "metadata" in result:
                    result["metadata"]["type"] = "reference_edit"
                    result["metadata"]["reference_count"] = len(reference_images)
                    result["metadata"]["original_prompt"] = prompt

            return result

        except Exception as e:
            logger.error(f"Error in edit_with_references: {e}", exc_info=True)
            return {
                "success": False,
                "error": f"Error generating with references: {str(e)}"
            }

    async def edit_with_mask(
        self,
        prompt: str,
        image: UploadFile,  # Not used in Ideogram API but kept for compatibility
        mask: UploadFile    # Not used in Ideogram API but kept for compatibility
    ) -> Dict[str, Any]:
        """
        Edit image using mask-based editing.
        Since Ideogram doesn't support mask editing, we generate a new image.

        Args:
            prompt: Description of the changes to make
            image: Original image file
            mask: Mask image file

        Returns:
            Dict with success status, image data, and metadata
        """
        try:
            logger.info(f"✏️ Generating new image based on edit request: {prompt[:100]}...")

            # Since Ideogram doesn't support mask editing, we generate a new image
            # with the edit prompt (NO MODIFICATIONS - user has complete control)
            result = await self.generate_image(
                prompt=prompt,  # Use user's prompt exactly as provided
                rendering_speed="QUALITY",  # Use high quality for edits
                magic_prompt="ON",  # Enable magic prompt for better results
                style_type="GENERAL"  # Use general style for maximum flexibility
            )

            if result["success"]:
                # Update metadata to indicate this was a mask edit
                if "metadata" in result:
                    result["metadata"]["type"] = "mask_edit"
                    result["metadata"]["edit_prompt"] = prompt

            return result

        except Exception as e:
            logger.error(f"Error in edit_with_mask: {e}", exc_info=True)
            return {
                "success": False,
                "error": f"Error editing with mask: {str(e)}"
            }

# Create service instance
image_generator_service = ImageGeneratorService()

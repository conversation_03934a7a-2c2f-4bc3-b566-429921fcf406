"""
Image Storage Service - Handles local storage of generated images for reference purposes.

This service downloads and stores images locally so they can be used as style references
for generating similar images, avoiding the issue of expired Ideogram URLs.
"""

import os
import logging
import httpx
import hashlib
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class ImageStorageService:
    """Service for storing and retrieving images locally."""
    
    def __init__(self):
        """Initialize the image storage service."""
        self.storage_dir = Path("stored_images")
        self.storage_dir.mkdir(exist_ok=True)
        logger.info(f"✅ Image Storage Service initialized - Storage dir: {self.storage_dir}")
    
    def _get_image_filename(self, image_url: str) -> str:
        """Generate a unique filename for an image based on its URL."""
        # Create a hash of the URL for a unique filename
        url_hash = hashlib.md5(image_url.encode()).hexdigest()
        return f"{url_hash}.png"
    
    def _get_image_path(self, image_url: str) -> Path:
        """Get the full path for storing an image."""
        filename = self._get_image_filename(image_url)
        return self.storage_dir / filename
    
    async def store_image(self, image_url: str) -> Optional[str]:
        """
        Download and store an image locally.
        
        Args:
            image_url: URL of the image to download and store
            
        Returns:
            Local file path if successful, None if failed
        """
        try:
            image_path = self._get_image_path(image_url)
            
            # Check if already stored
            if image_path.exists():
                logger.info(f"📁 Image already stored locally: {image_path}")
                return str(image_path)
            
            # Download the image
            logger.info(f"⬇️ Downloading image for local storage: {image_url[:100]}...")
            
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.get(image_url)
                
                if response.status_code != 200:
                    logger.error(f"❌ Failed to download image: HTTP {response.status_code}")
                    return None
                
                # Save the image
                with open(image_path, 'wb') as f:
                    f.write(response.content)
                
                logger.info(f"✅ Image stored locally: {image_path}")
                return str(image_path)
                
        except Exception as e:
            logger.error(f"❌ Error storing image: {e}")
            return None
    
    def get_stored_image_path(self, image_url: str) -> Optional[str]:
        """
        Get the local path of a stored image.
        
        Args:
            image_url: Original URL of the image
            
        Returns:
            Local file path if exists, None if not stored
        """
        image_path = self._get_image_path(image_url)
        
        if image_path.exists():
            return str(image_path)
        
        return None
    
    def read_stored_image(self, image_url: str) -> Optional[bytes]:
        """
        Read a stored image as bytes.
        
        Args:
            image_url: Original URL of the image
            
        Returns:
            Image bytes if exists, None if not stored
        """
        try:
            image_path = self._get_image_path(image_url)
            
            if not image_path.exists():
                return None
            
            with open(image_path, 'rb') as f:
                return f.read()
                
        except Exception as e:
            logger.error(f"❌ Error reading stored image: {e}")
            return None
    
    def cleanup_old_images(self, max_age_days: int = 7):
        """
        Clean up old stored images.
        
        Args:
            max_age_days: Maximum age in days before deletion
        """
        try:
            import time
            current_time = time.time()
            max_age_seconds = max_age_days * 24 * 60 * 60
            
            deleted_count = 0
            
            for image_file in self.storage_dir.glob("*.png"):
                file_age = current_time - image_file.stat().st_mtime
                
                if file_age > max_age_seconds:
                    image_file.unlink()
                    deleted_count += 1
            
            if deleted_count > 0:
                logger.info(f"🧹 Cleaned up {deleted_count} old images")
                
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")


# Global service instance
image_storage_service = ImageStorageService()

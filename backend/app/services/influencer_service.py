"""
Service for creating and managing virtual influencers using Ideogram AI.
"""

import logging
import httpx
import random
import io
from typing import Dict, Any, Optional, List
from fastapi import UploadFile

logger = logging.getLogger(__name__)


class InfluencerService:
    """Service for creating virtual influencers and managing influencer content."""
    
    def __init__(self):
        # Import ideogram service for image generation
        from app.services.ideogram_service import ideogram_service
        self.ideogram_service = ideogram_service
        
    async def generate_testimonial(
        self,
        product_name: str,
        testimonial_text: str,
        influencer_style: str = "lifestyle",
        resolution: Optional[str] = None,
        aspect_ratio: Optional[str] = None,
        rendering_speed: str = "DEFAULT",
        magic_prompt: str = "AUTO",
        negative_prompt: Optional[str] = None,
        num_images: int = 1,
        style_type: str = "REALISTIC"
    ) -> Dict[str, Any]:
        """
        Generate a testimonial image with a virtual influencer.

        Args:
            product_name: Name of the product being endorsed
            testimonial_text: The testimonial text content
            influencer_style: Style of influencer (lifestyle, fitness, beauty, tech, etc.)
            resolution: Ideogram resolution (e.g., "1024x1024")
            aspect_ratio: Alternative to resolution (e.g., "1x1")
            rendering_speed: TURBO/DEFAULT/QUALITY
            magic_prompt: AUTO/ON/OFF for prompt enhancement
            negative_prompt: What to exclude from image
            num_images: Number of images to generate (1-8)
            style_type: AUTO/GENERAL/REALISTIC/DESIGN

        Returns:
            Dict with success status, image data, and metadata
        """
        try:
            # Create a detailed prompt for the testimonial
            influencer_prompt = self._create_testimonial_prompt(
                product_name, testimonial_text, influencer_style
            )
            
            logger.info(f"🎭 Generating testimonial for {product_name} with {influencer_style} influencer...")

            # Generate random seed automatically (0-2147483647)
            random_seed = random.randint(0, 2147483647)

            # Prepare form data for multipart/form-data request
            files = {
                "prompt": (None, influencer_prompt),
                "rendering_speed": (None, rendering_speed),
                "magic_prompt": (None, magic_prompt),
                "num_images": (None, str(num_images)),
                "style_type": (None, style_type),
                "seed": (None, str(random_seed))
            }

            # Add optional parameters
            if resolution and not aspect_ratio:
                files["resolution"] = (None, resolution)
            elif aspect_ratio and not resolution:
                files["aspect_ratio"] = (None, aspect_ratio)

            if negative_prompt:
                files["negative_prompt"] = (None, negative_prompt)

            # Use Ideogram service to generate the image
            result = await self.ideogram_service.generate_image_with_files(files)
            
            if result["success"]:
                # Add testimonial-specific metadata
                if "metadata" not in result:
                    result["metadata"] = {}
                result["metadata"].update({
                    "type": "testimonial",
                    "product_name": product_name,
                    "testimonial_text": testimonial_text,
                    "influencer_style": influencer_style,
                    "seed": random_seed
                })

            return result

        except Exception as e:
            logger.error(f"Error generating testimonial: {e}", exc_info=True)
            return {
                "success": False,
                "error": f"Failed to generate testimonial: {str(e)}"
            }

    async def generate_product_placement(
        self,
        product_name: str,
        placement_context: str,
        influencer_style: str = "lifestyle",
        resolution: Optional[str] = None,
        aspect_ratio: Optional[str] = None,
        rendering_speed: str = "DEFAULT",
        magic_prompt: str = "AUTO",
        negative_prompt: Optional[str] = None,
        num_images: int = 1,
        style_type: str = "REALISTIC"
    ) -> Dict[str, Any]:
        """
        Generate a product placement image with a virtual influencer.

        Args:
            product_name: Name of the product to place
            placement_context: Context for the placement (morning routine, workout, etc.)
            influencer_style: Style of influencer (lifestyle, fitness, beauty, tech, etc.)
            resolution: Ideogram resolution (e.g., "1024x1024")
            aspect_ratio: Alternative to resolution (e.g., "1x1")
            rendering_speed: TURBO/DEFAULT/QUALITY
            magic_prompt: AUTO/ON/OFF for prompt enhancement
            negative_prompt: What to exclude from image
            num_images: Number of images to generate (1-8)
            style_type: AUTO/GENERAL/REALISTIC/DESIGN

        Returns:
            Dict with success status, image data, and metadata
        """
        try:
            # Create a detailed prompt for the product placement
            placement_prompt = self._create_placement_prompt(
                product_name, placement_context, influencer_style
            )
            
            logger.info(f"🛍️ Generating product placement for {product_name} in {placement_context}...")

            # Generate random seed automatically (0-2147483647)
            random_seed = random.randint(0, 2147483647)

            # Prepare form data for multipart/form-data request
            files = {
                "prompt": (None, placement_prompt),
                "rendering_speed": (None, rendering_speed),
                "magic_prompt": (None, magic_prompt),
                "num_images": (None, str(num_images)),
                "style_type": (None, style_type),
                "seed": (None, str(random_seed))
            }

            # Add optional parameters
            if resolution and not aspect_ratio:
                files["resolution"] = (None, resolution)
            elif aspect_ratio and not resolution:
                files["aspect_ratio"] = (None, aspect_ratio)

            if negative_prompt:
                files["negative_prompt"] = (None, negative_prompt)

            # Use Ideogram service to generate the image
            result = await self.ideogram_service.generate_image_with_files(files)
            
            if result["success"]:
                # Add placement-specific metadata
                if "metadata" not in result:
                    result["metadata"] = {}
                result["metadata"].update({
                    "type": "product_placement",
                    "product_name": product_name,
                    "placement_context": placement_context,
                    "influencer_style": influencer_style,
                    "seed": random_seed
                })

            return result

        except Exception as e:
            logger.error(f"Error generating product placement: {e}", exc_info=True)
            return {
                "success": False,
                "error": f"Failed to generate product placement: {str(e)}"
            }

    def _create_testimonial_prompt(self, product_name: str, testimonial_text: str, influencer_style: str) -> str:
        """Create a detailed prompt for testimonial generation."""
        
        style_descriptions = {
            "lifestyle": "trendy lifestyle influencer, casual chic outfit, modern apartment background",
            "fitness": "athletic fitness influencer, workout attire, gym or outdoor setting",
            "beauty": "beauty influencer, glamorous makeup, elegant studio lighting",
            "tech": "tech reviewer, modern minimalist setup, clean background",
            "fashion": "fashion influencer, stylish outfit, fashionable background",
            "travel": "travel influencer, scenic location, adventure-ready outfit"
        }
        
        style_desc = style_descriptions.get(influencer_style, "lifestyle influencer, modern setting")
        
        prompt = f"""Professional testimonial photo featuring a {style_desc}. 
        The influencer is genuinely endorsing {product_name}. 
        Natural lighting, authentic expression, holding or using the product naturally. 
        High-quality commercial photography, realistic skin texture, 
        professional composition. The scene should convey trust and authenticity.
        Testimonial context: {testimonial_text[:100]}"""
        
        return prompt

    def _create_placement_prompt(self, product_name: str, placement_context: str, influencer_style: str) -> str:
        """Create a detailed prompt for product placement generation."""
        
        style_descriptions = {
            "lifestyle": "trendy lifestyle influencer, casual chic outfit",
            "fitness": "athletic fitness influencer, workout attire",
            "beauty": "beauty influencer, glamorous makeup",
            "tech": "tech reviewer, modern minimalist aesthetic",
            "fashion": "fashion influencer, stylish outfit",
            "travel": "travel influencer, adventure-ready look"
        }
        
        style_desc = style_descriptions.get(influencer_style, "lifestyle influencer")
        
        prompt = f"""Natural product placement photo featuring a {style_desc} 
        naturally incorporating {product_name} into their {placement_context}. 
        The product should be seamlessly integrated into the scene, not forced or obvious. 
        Professional photography, authentic moment, natural lighting, 
        high-quality commercial aesthetic. The influencer should look genuine and relatable.
        Context: {placement_context}"""
        
        return prompt

    async def edit_with_references(
        self,
        prompt: str,
        reference_images: List[UploadFile],
        resolution: Optional[str] = None,
        aspect_ratio: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate influencer content using reference images.
        Note: Ideogram doesn't support reference images directly, so we enhance the prompt based on image count.

        Args:
            prompt: Description of the influencer content to create
            reference_images: List of reference images (used for prompt enhancement)
            resolution: Ideogram resolution (e.g., "1024x1024")
            aspect_ratio: Alternative to resolution (e.g., "1x1")

        Returns:
            Dict with success status, image data, and metadata
        """
        try:
            logger.info(f"🎭 Generating influencer content with {len(reference_images)} reference images...")
            
            # Enhance prompt based on reference images
            enhanced_prompt = f"{prompt}. Professional influencer photography, high-quality commercial aesthetic, authentic and relatable style."
            if len(reference_images) > 1:
                enhanced_prompt += " Multiple style references considered for consistent brand aesthetic."

            # Generate random seed automatically (0-2147483647)
            random_seed = random.randint(0, 2147483647)

            # Prepare form data for multipart/form-data request
            files = {
                "prompt": (None, enhanced_prompt),
                "rendering_speed": (None, "DEFAULT"),
                "magic_prompt": (None, "ON"),
                "num_images": (None, "1"),
                "style_type": (None, "REALISTIC"),
                "seed": (None, str(random_seed))
            }

            # Add optional parameters
            if resolution and not aspect_ratio:
                files["resolution"] = (None, resolution)
            elif aspect_ratio and not resolution:
                files["aspect_ratio"] = (None, aspect_ratio)

            # Use Ideogram service to generate the image
            result = await self.ideogram_service.generate_image_with_files(files)
            
            if result["success"]:
                # Add reference-specific metadata
                if "metadata" not in result:
                    result["metadata"] = {}
                result["metadata"].update({
                    "type": "reference_based",
                    "reference_count": len(reference_images),
                    "seed": random_seed
                })

            return result

        except Exception as e:
            logger.error(f"Error generating influencer content with references: {e}", exc_info=True)
            return {
                "success": False,
                "error": f"Failed to generate influencer content with references: {str(e)}"
            }


# Create a singleton instance
influencer_service = InfluencerService()

"""
SAIO Agent Service - Search AI Optimization
The world's first REAL SAIO/GEO optimization agent based on scientific research.

This agent contains the complete knowledge base from the comprehensive SAIO research
and provides REAL optimization recommendations for Google + ChatGPT + Perplexity.
"""

import logging
import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class SAIOAgentService:
    """
    SAIO Agent - The world's first real Search AI Optimization agent.
    
    Based on comprehensive research showing:
    - 73% similarity between ChatGPT and Bing organic results
    - 100% of Perplexity-cited articles have images
    - 90% of YMYL citations are lists/how-tos
    - E-E-A-T is crucial for AI trust
    """
    
    def __init__(self):
        self.model = None
        self.saio_knowledge_base = self._load_saio_knowledge()
        self._initialize_ai()
        
        logger.info("🧠 SAIO Agent initialized - World's first real SAIO optimizer")
    
    def _initialize_ai(self):
        """Initialize AI model for SAIO analysis."""
        try:
            import google.generativeai as genai
            from app.core.config import settings
            
            if settings.GEMINI_API_KEY:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("✅ SAIO Agent AI initialized successfully")
            else:
                logger.warning("⚠️ GEMINI_API_KEY not configured - using fallback mode")
        except Exception as e:
            logger.error(f"❌ Failed to initialize SAIO Agent AI: {e}")
    
    def _load_saio_knowledge(self) -> Dict[str, Any]:
        """Load the complete SAIO knowledge base from research."""
        return {
            "core_principles": {
                "seo_foundation": {
                    "importance": "Critical - 73% similarity between ChatGPT and Bing results",
                    "focus": "Bing optimization is crucial for AI visibility",
                    "requirements": [
                        "Accessible to AI bots (OAI-SearchBot, ChatGPT-User)",
                        "Fast loading and good UX",
                        "Unique, useful, readable content",
                        "Quality backlink profile"
                    ]
                },
                "content_structure": {
                    "winning_formats": [
                        "Lists (90% of YMYL citations)",
                        "How-to guides",
                        "Step-by-step tutorials", 
                        "FAQ format",
                        "Comparison articles"
                    ],
                    "organization": [
                        "Clear headings (H1, H2, H3)",
                        "Numbered or bulleted lists",
                        "Question-answer format",
                        "Logical subsections"
                    ]
                }
            },
            "ranking_factors": {
                "bing_seo_signals": {
                    "weight": 0.25,
                    "description": "Traditional SEO optimized for Bing",
                    "indicators": [
                        "Bing Webmaster Tools registration",
                        "Sitemap submission",
                        "Clean technical SEO",
                        "Mobile optimization"
                    ]
                },
                "content_structure": {
                    "weight": 0.20,
                    "description": "AI-friendly content organization",
                    "indicators": [
                        "Lists and numbered items",
                        "How-to format",
                        "Clear Q&A sections",
                        "Step-by-step instructions"
                    ]
                },
                "authority_signals": {
                    "weight": 0.20,
                    "description": "E-E-A-T implementation",
                    "indicators": [
                        "Author bio with credentials",
                        "First-hand experience",
                        "Cited authoritative sources",
                        "Quality backlinks"
                    ]
                },
                "multimedia_content": {
                    "weight": 0.15,
                    "description": "Visual and media elements",
                    "indicators": [
                        "At least 2-3 relevant images",
                        "Embedded videos (YouTube)",
                        "Infographics or diagrams",
                        "Alt text for images"
                    ]
                },
                "content_freshness": {
                    "weight": 0.10,
                    "description": "Recency and updates",
                    "indicators": [
                        "Current year in title/content",
                        "Recent publication date",
                        "Updated information",
                        "Latest statistics/data"
                    ]
                },
                "schema_markup": {
                    "weight": 0.10,
                    "description": "Structured data implementation",
                    "indicators": [
                        "FAQ schema",
                        "HowTo schema",
                        "Article schema",
                        "Organization schema"
                    ]
                }
            },
            "ai_preferences": {
                "perplexity": {
                    "image_requirement": "100% of cited articles have images",
                    "average_images": "2-3 images per article",
                    "content_types": "Lists, guides, how-tos dominate",
                    "freshness": "Strongly favors recent content"
                },
                "chatgpt": {
                    "source_similarity": "73% overlap with Bing organic results",
                    "authority_bias": "Prefers high-authority domains",
                    "format_preference": "Clear, structured information",
                    "citation_pattern": "References multiple sources per answer"
                },
                "general_ai": {
                    "trust_signals": "E-E-A-T is crucial",
                    "content_depth": "Comprehensive coverage preferred",
                    "user_intent": "Direct answers to specific questions",
                    "multimedia": "Visual content increases citation probability"
                }
            },
            "optimization_templates": {
                "list_article": {
                    "title_format": "Los X mejores [topic] para [audience] (2025)",
                    "structure": [
                        "Introduction with clear value proposition",
                        "Numbered list with detailed explanations",
                        "Comparison table if applicable",
                        "Conclusion with recommendation"
                    ]
                },
                "how_to_guide": {
                    "title_format": "Cómo [action]: Guía paso a paso (2025)",
                    "structure": [
                        "What you'll learn/achieve",
                        "Prerequisites or requirements",
                        "Step-by-step instructions",
                        "Tips and best practices",
                        "Common mistakes to avoid"
                    ]
                },
                "comparison": {
                    "title_format": "[Option A] vs [Option B]: Comparación completa 2025",
                    "structure": [
                        "Overview of both options",
                        "Side-by-side comparison",
                        "Pros and cons for each",
                        "Recommendation based on use case"
                    ]
                }
            }
        }
    
    async def analyze_saio_score(self, content: str, title: str = "", url: str = "") -> Dict[str, Any]:
        """
        Calculate REAL SAIO score based on research-backed factors.
        
        Args:
            content: The content to analyze
            title: Page title
            url: Page URL (optional)
            
        Returns:
            Complete SAIO analysis with actionable recommendations
        """
        try:
            logger.info(f"🔍 SAIO Agent analyzing content (length: {len(content)} chars)")
            
            # Analyze each factor
            scores = {}
            recommendations = []
            
            # 1. Bing SEO Signals (25%)
            bing_score = self._analyze_bing_seo(content, title)
            scores["bing_seo_signals"] = bing_score
            
            # 2. Content Structure (20%)
            structure_score = self._analyze_content_structure(content)
            scores["content_structure"] = structure_score
            
            # 3. Authority Signals (20%)
            authority_score = self._analyze_authority_signals(content)
            scores["authority_signals"] = authority_score
            
            # 4. Multimedia Content (15%)
            multimedia_score = self._analyze_multimedia_content(content)
            scores["multimedia_content"] = multimedia_score
            
            # 5. Content Freshness (10%)
            freshness_score = self._analyze_content_freshness(content, title)
            scores["content_freshness"] = freshness_score
            
            # 6. Schema Markup (10%)
            schema_score = self._analyze_schema_potential(content)
            scores["schema_markup"] = schema_score
            
            # Calculate weighted final score
            weights = self.saio_knowledge_base["ranking_factors"]
            final_score = sum(
                scores[factor] * weights[factor]["weight"]
                for factor in scores.keys()
            )
            
            # Generate recommendations
            recommendations = self._generate_saio_recommendations(scores, content)
            
            # Determine AI citation probability
            citation_probability = self._calculate_citation_probability(scores)
            
            return {
                "saio_score": round(final_score, 1),
                "citation_probability": citation_probability,
                "component_scores": scores,
                "recommendations": recommendations,
                "ai_readiness": {
                    "google_sge": self._assess_google_readiness(scores),
                    "chatgpt": self._assess_chatgpt_readiness(scores),
                    "perplexity": self._assess_perplexity_readiness(scores)
                },
                "analysis_timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ SAIO analysis failed: {str(e)}")
            return {
                "saio_score": 0.0,
                "error": str(e),
                "analysis_timestamp": datetime.utcnow().isoformat()
            }
    
    def _analyze_bing_seo(self, content: str, title: str) -> float:
        """Analyze traditional SEO factors with Bing focus."""
        score = 50.0  # Base score
        
        # Title optimization
        if title:
            if len(title) >= 30 and len(title) <= 60:
                score += 10
            if any(year in title for year in ["2024", "2025"]):
                score += 5
        
        # Content length (Bing prefers substantial content)
        word_count = len(content.split())
        if word_count >= 300:
            score += 10
        if word_count >= 1000:
            score += 10
        
        # Keyword density and natural language
        if self._has_natural_language_patterns(content):
            score += 10
        
        # Internal structure
        if self._has_good_heading_structure(content):
            score += 15
        
        return min(score, 100)

    def _analyze_authority_signals(self, content: str) -> float:
        """Analyze E-E-A-T authority signals."""
        score = 40.0  # Base score

        # Author credentials
        author_indicators = [
            'autor', 'escrito por', 'by', 'dr.', 'doctor', 'profesor',
            'especialista', 'experto', 'certificado', 'experiencia'
        ]
        author_count = sum(1 for indicator in author_indicators if indicator in content.lower())
        score += min(author_count * 5, 20)

        # First-hand experience
        experience_indicators = [
            'mi experiencia', 'he probado', 'he usado', 'en mi caso',
            'personalmente', 'he trabajado', 'años de experiencia'
        ]
        experience_count = sum(1 for indicator in experience_indicators if indicator in content.lower())
        score += min(experience_count * 4, 16)

        # Citations and sources
        citation_patterns = [
            r'según.*estudio', r'investigación.*muestra', r'datos.*indican',
            r'fuente:', r'referencia:', r'estudio.*encontró'
        ]
        citation_count = sum(len(re.findall(pattern, content.lower())) for pattern in citation_patterns)
        score += min(citation_count * 3, 15)

        # Statistics and data
        stats_patterns = [r'\d+%', r'\d+\.\d+%', r'\d+ de cada \d+']
        stats_count = sum(len(re.findall(pattern, content)) for pattern in stats_patterns)
        score += min(stats_count * 2, 10)

        return min(score, 100)

    def _analyze_multimedia_content(self, content: str) -> float:
        """Analyze multimedia content (crucial for Perplexity)."""
        score = 20.0  # Base score

        # Image references (100% of Perplexity citations have images)
        image_patterns = [
            r'<img', r'\[imagen\]', r'\[image\]', r'figura \d+',
            r'gráfico', r'infografía', r'diagrama'
        ]
        image_count = sum(len(re.findall(pattern, content.lower())) for pattern in image_patterns)

        if image_count >= 2:  # Research shows 2-3 images average
            score += 40  # High value - critical for AI citations
        elif image_count >= 1:
            score += 25

        # Video references
        video_patterns = [
            r'youtube', r'video', r'<iframe', r'\[video\]',
            r'ver el video', r'tutorial en video'
        ]
        video_count = sum(len(re.findall(pattern, content.lower())) for pattern in video_patterns)
        if video_count >= 1:
            score += 20

        # Alt text mentions (good practice)
        if 'alt=' in content or 'texto alternativo' in content.lower():
            score += 10

        # Visual content descriptions
        visual_descriptions = [
            'como se muestra en', 'ver imagen', 'en el gráfico',
            'la siguiente figura', 'como puedes ver'
        ]
        visual_desc_count = sum(1 for desc in visual_descriptions if desc in content.lower())
        score += min(visual_desc_count * 5, 10)

        return min(score, 100)

    def _analyze_content_freshness(self, content: str, title: str) -> float:
        """Analyze content freshness (AI strongly prefers recent content)."""
        score = 30.0  # Base score

        # Current year mentions
        current_year = datetime.now().year
        if str(current_year) in content or str(current_year) in title:
            score += 30  # High value for current year
        elif str(current_year - 1) in content:
            score += 15  # Previous year still good

        # Update indicators
        update_indicators = [
            'actualizado', 'updated', 'revisado', 'última actualización',
            'datos recientes', 'información actual'
        ]
        update_count = sum(1 for indicator in update_indicators if indicator in content.lower())
        score += min(update_count * 8, 24)

        # Recent data patterns
        recent_patterns = [
            r'en 202[4-5]', r'datos de 202[4-5]', r'estudio reciente',
            r'últimos datos', r'información actualizada'
        ]
        recent_count = sum(len(re.findall(pattern, content.lower())) for pattern in recent_patterns)
        score += min(recent_count * 6, 18)

        return min(score, 100)

    def _analyze_schema_potential(self, content: str) -> float:
        """Analyze potential for structured data markup."""
        score = 40.0  # Base score

        # FAQ potential
        faq_patterns = [
            r'pregunta.*frecuente', r'faq', r'¿.*\?.*respuesta',
            r'preguntas.*comunes'
        ]
        faq_count = sum(len(re.findall(pattern, content.lower())) for pattern in faq_patterns)
        if faq_count >= 3:
            score += 25
        elif faq_count >= 1:
            score += 15

        # HowTo potential
        howto_patterns = [
            r'paso \d+', r'instrucciones', r'cómo hacer',
            r'tutorial', r'guía paso a paso'
        ]
        howto_count = sum(len(re.findall(pattern, content.lower())) for pattern in howto_patterns)
        if howto_count >= 3:
            score += 20

        # Article structure
        if self._has_good_heading_structure(content):
            score += 15

        return min(score, 100)

    def _generate_saio_recommendations(self, scores: Dict[str, float], content: str) -> List[Dict[str, Any]]:
        """Generate actionable SAIO recommendations based on research."""
        recommendations = []

        # Content Structure (most important for AI)
        if scores["content_structure"] < 70:
            recommendations.append({
                "priority": "high",
                "category": "content_structure",
                "title": "Agregar listas numeradas",
                "description": "90% de las citas en temas YMYL son listas. Convierte párrafos en listas numeradas.",
                "impact": "high",
                "research_basis": "90% of YMYL citations are lists/how-tos"
            })

        # Multimedia (critical for Perplexity)
        if scores["multimedia_content"] < 60:
            recommendations.append({
                "priority": "high",
                "category": "multimedia",
                "title": "Agregar 2-3 imágenes relevantes",
                "description": "100% de artículos citados por Perplexity tienen imágenes. Promedio: 2-3 por artículo.",
                "impact": "high",
                "research_basis": "100% of Perplexity citations have images"
            })

        # Authority Signals
        if scores["authority_signals"] < 70:
            recommendations.append({
                "priority": "medium",
                "category": "authority",
                "title": "Agregar biografía del autor",
                "description": "Incluye credenciales y experiencia del autor para mejorar E-E-A-T.",
                "impact": "medium",
                "research_basis": "E-E-A-T crucial for AI trust"
            })

        # Freshness
        if scores["content_freshness"] < 60:
            recommendations.append({
                "priority": "medium",
                "category": "freshness",
                "title": "Incluir año actual en título",
                "description": "IA favorece contenido reciente. Agrega '2025' al título.",
                "impact": "medium",
                "research_basis": "AI strongly favors recent content"
            })

        return recommendations

    def _calculate_citation_probability(self, scores: Dict[str, float]) -> str:
        """Calculate probability of being cited by AI based on scores."""
        weighted_score = sum(
            scores[factor] * self.saio_knowledge_base["ranking_factors"][factor]["weight"]
            for factor in scores.keys()
        )

        if weighted_score >= 85:
            return "muy_alta"
        elif weighted_score >= 70:
            return "alta"
        elif weighted_score >= 55:
            return "media"
        elif weighted_score >= 40:
            return "baja"
        else:
            return "muy_baja"

    def _assess_google_readiness(self, scores: Dict[str, float]) -> Dict[str, Any]:
        """Assess readiness for Google SGE."""
        return {
            "ready": scores["bing_seo_signals"] >= 70 and scores["content_structure"] >= 60,
            "key_factors": ["bing_seo_signals", "content_structure", "authority_signals"],
            "recommendation": "Focus on traditional SEO + structured content"
        }

    def _assess_chatgpt_readiness(self, scores: Dict[str, float]) -> Dict[str, Any]:
        """Assess readiness for ChatGPT citations."""
        return {
            "ready": scores["bing_seo_signals"] >= 70 and scores["authority_signals"] >= 65,
            "key_factors": ["bing_seo_signals", "authority_signals", "content_freshness"],
            "recommendation": "Optimize for Bing + build authority signals"
        }

    def _assess_perplexity_readiness(self, scores: Dict[str, float]) -> Dict[str, Any]:
        """Assess readiness for Perplexity citations."""
        return {
            "ready": scores["multimedia_content"] >= 60 and scores["content_freshness"] >= 60,
            "key_factors": ["multimedia_content", "content_freshness", "content_structure"],
            "recommendation": "Add images + keep content fresh + use lists"
        }

    def _has_natural_language_patterns(self, content: str) -> bool:
        """Check for natural language patterns."""
        patterns = [
            r'¿.*\?', r'cómo.*', r'qué.*', r'por qué.*',
            r'cuándo.*', r'dónde.*', r'quién.*'
        ]
        return any(re.search(pattern, content.lower()) for pattern in patterns)

    def _has_good_heading_structure(self, content: str) -> bool:
        """Check for good heading structure."""
        heading_patterns = [r'^#{1,6}\s', r'<h[1-6]']
        heading_count = sum(len(re.findall(pattern, content, re.MULTILINE)) for pattern in heading_patterns)
        return heading_count >= 3

    async def generate_saio_optimized_content(self, topic: str, content_type: str = "how_to") -> Dict[str, Any]:
        """Generate SAIO-optimized content using research-backed templates."""
        try:
            if not self.model:
                return {"error": "AI model not available"}

            template = self.saio_knowledge_base["optimization_templates"].get(content_type,
                self.saio_knowledge_base["optimization_templates"]["how_to_guide"])

            prompt = f"""
Crea contenido optimizado para SAIO (Search AI Optimization) sobre: {topic}

REQUISITOS BASADOS EN INVESTIGACIÓN CIENTÍFICA:
1. ESTRUCTURA (20% del score SAIO):
   - Usar listas numeradas (90% de citas IA en YMYL)
   - Formato paso a paso claro
   - Subtítulos descriptivos

2. MULTIMEDIA (15% del score):
   - Mencionar 2-3 imágenes específicas
   - Describir qué mostrarían las imágenes
   - Incluir referencias a videos si aplica

3. AUTORIDAD (20% del score):
   - Incluir experiencia personal
   - Citar fuentes autorizadas
   - Usar datos y estadísticas

4. FRESCURA (10% del score):
   - Incluir "2025" en el título
   - Mencionar información actualizada
   - Usar datos recientes

5. FORMATO: {template['title_format']}
6. ESTRUCTURA: {template['structure']}

IMPORTANTE:
- Crear contenido que ChatGPT, Perplexity y Google SGE quieran citar
- Basado en investigación real de 500 consultas analizadas
- Optimizado para Bing (73% similitud con ChatGPT)

Genera el contenido completo:
"""

            response = self.model.generate_content(prompt)

            if response and response.text:
                generated_content = response.text.strip()

                # Auto-analyze the generated content
                analysis = await self.analyze_saio_score(generated_content, topic)

                return {
                    "content": generated_content,
                    "saio_analysis": analysis,
                    "template_used": content_type,
                    "optimization_level": "research_based"
                }
            else:
                return {"error": "Failed to generate content"}

        except Exception as e:
            logger.error(f"❌ SAIO content generation failed: {str(e)}")
            return {"error": str(e)}

    def get_saio_knowledge_summary(self) -> Dict[str, Any]:
        """Get a summary of the SAIO knowledge base."""
        return {
            "research_basis": "Comprehensive analysis of 500+ AI search queries",
            "key_findings": [
                "73% similarity between ChatGPT and Bing organic results",
                "100% of Perplexity citations include images",
                "90% of YMYL citations are lists/how-tos",
                "E-E-A-T crucial for AI trust",
                "Fresh content strongly preferred"
            ],
            "optimization_factors": list(self.saio_knowledge_base["ranking_factors"].keys()),
            "supported_ai_platforms": ["Google SGE", "ChatGPT", "Perplexity", "Bing Chat"],
            "content_templates": list(self.saio_knowledge_base["optimization_templates"].keys())
        }
    
    def _analyze_content_structure(self, content: str) -> float:
        """Analyze AI-friendly content structure."""
        score = 30.0  # Base score
        
        # Lists (crucial for AI citations)
        numbered_lists = len(re.findall(r'\d+\.\s', content))
        bulleted_lists = len(re.findall(r'[•\-\*]\s', content))
        
        if numbered_lists >= 3:
            score += 25  # High value for numbered lists
        elif numbered_lists >= 1:
            score += 15
        
        if bulleted_lists >= 3:
            score += 15
        
        # How-to patterns
        how_to_patterns = [
            r'paso \d+', r'step \d+', r'cómo hacer', r'how to',
            r'instrucciones', r'tutorial', r'guía'
        ]
        how_to_count = sum(len(re.findall(pattern, content.lower())) for pattern in how_to_patterns)
        if how_to_count >= 3:
            score += 20
        
        # Q&A format
        question_patterns = [r'\?', r'¿.*\?', r'pregunta', r'respuesta']
        question_count = sum(len(re.findall(pattern, content.lower())) for pattern in question_patterns)
        if question_count >= 3:
            score += 10
        
        return min(score, 100)

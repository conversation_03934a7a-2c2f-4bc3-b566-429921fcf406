"""
Content Opportunities Analyzer Module
Analyzes content gaps and opportunities based on research data
"""

import logging
import asyncio
import json
from typing import Dict, Any, List, Set
from collections import Counter

import google.generativeai as genai
from app.core.config import settings

logger = logging.getLogger(__name__)

class ContentOpportunitiesAnalyzer:
    """Analyzes content gaps and opportunities from research data."""
    
    def __init__(self):
        self.gemini_model = None
        
        # Initialize Gemini AI
        if settings.GEMINI_API_KEY:
            try:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("✅ Content Opportunities Analyzer - Gemini AI initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Gemini AI: {e}")
                self.gemini_model = None
        else:
            logger.warning("⚠️ GEMINI_API_KEY not found. Content opportunities analysis will use rule-based approach.")
    
    async def analyze_content_opportunities(
        self, 
        topic: str, 
        google_results: Dict[str, Any], 
        gpt_reference: Dict[str, Any], 
        language: str = "es"
    ) -> Dict[str, Any]:
        """
        Analyze content gaps and opportunities.
        
        Args:
            topic: Main research topic
            google_results: Google search results
            gpt_reference: GPT reference response
            language: Target language
            
        Returns:
            Content opportunities analysis
        """
        try:
            if not self.gemini_model:
                return self._get_fallback_content_opportunities(topic)
            
            # Analyze top competitors
            competitor_analysis = self._analyze_competitors(google_results)
            
            # Use AI to identify opportunities
            ai_opportunities = await self._ai_analyze_opportunities(topic, competitor_analysis, gpt_reference)
            
            # Enhance with rule-based analysis
            rule_based_opportunities = self._rule_based_opportunities_analysis(topic, google_results, gpt_reference)
            
            # Merge results
            merged_opportunities = self._merge_opportunities(ai_opportunities, rule_based_opportunities)
            
            # Add competitive analysis
            competitive_landscape = self._analyze_competitive_landscape(google_results)
            
            logger.info(f"✅ Content opportunities analyzed for '{topic}'")
            return {
                "status": "success",
                "topic": topic,
                **merged_opportunities,
                "competitive_landscape": competitive_landscape,
                "opportunity_score": self._calculate_opportunity_score(merged_opportunities, competitive_landscape)
            }
                
        except Exception as e:
            logger.error(f"❌ Content opportunities analysis failed: {str(e)}")
            return self._get_fallback_content_opportunities(topic)
    
    def _analyze_competitors(self, google_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Analyze top competitors from Google results."""
        competitor_analysis = []
        
        for result in google_results.get('results', [])[:5]:
            competitor = {
                "title": result.get('title', ''),
                "snippet": result.get('snippet', ''),
                "url": result.get('link', ''),
                "domain": result.get('domain', ''),
                "position": result.get('position', 0),
                "title_length": len(result.get('title', '')),
                "snippet_length": len(result.get('snippet', '')),
                "content_type": self._classify_content_type(result.get('title', '')),
                "authority_indicators": self._identify_authority_indicators(result)
            }
            competitor_analysis.append(competitor)
        
        return competitor_analysis
    
    def _classify_content_type(self, title: str) -> str:
        """Classify content type based on title."""
        title_lower = title.lower()
        
        if any(word in title_lower for word in ['guía', 'guide', 'tutorial', 'cómo']):
            return "guide"
        elif any(word in title_lower for word in ['qué es', 'definición', 'what is']):
            return "definition"
        elif any(word in title_lower for word in ['mejores', 'top', 'best', 'comparación']):
            return "comparison"
        elif any(word in title_lower for word in ['lista', 'list', 'tipos']):
            return "list"
        elif any(word in title_lower for word in ['beneficios', 'ventajas', 'benefits']):
            return "benefits"
        else:
            return "article"
    
    def _identify_authority_indicators(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Identify authority indicators in search result."""
        domain = result.get('domain', '').lower()
        title = result.get('title', '').lower()
        snippet = result.get('snippet', '').lower()
        
        indicators = {
            "is_wikipedia": 'wikipedia' in domain,
            "is_government": any(ext in domain for ext in ['.gov', '.edu']),
            "is_news_site": any(news in domain for news in ['news', 'noticias', 'periodico']),
            "has_year": any(year in title + snippet for year in ['2023', '2024', '2025']),
            "has_statistics": '%' in snippet or 'estadística' in snippet,
            "has_expert_language": any(word in snippet for word in ['experto', 'especialista', 'investigación']),
            "domain_authority_score": self._estimate_domain_authority(domain)
        }
        
        return indicators
    
    def _estimate_domain_authority(self, domain: str) -> int:
        """Estimate domain authority based on domain characteristics."""
        high_authority_domains = [
            'wikipedia.org', 'youtube.com', 'amazon.com', 'facebook.com',
            'linkedin.com', 'twitter.com', 'instagram.com', 'pinterest.com',
            'reddit.com', 'quora.com', 'medium.com'
        ]
        
        if any(auth_domain in domain for auth_domain in high_authority_domains):
            return 90
        elif any(ext in domain for ext in ['.gov', '.edu']):
            return 95
        elif any(ext in domain for ext in ['.org']):
            return 70
        elif len(domain.split('.')) == 2:  # Simple domain structure
            return 60
        else:
            return 40
    
    async def _ai_analyze_opportunities(
        self, 
        topic: str, 
        competitor_analysis: List[Dict[str, Any]], 
        gpt_reference: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Use AI to analyze content opportunities."""
        try:
            prompt = f"""
            Analiza las oportunidades de contenido para el tema "{topic}" basándote en:
            
            1. Competidores actuales en Google:
            {json.dumps(competitor_analysis, indent=2)}
            
            2. Respuesta de referencia de GPT:
            {gpt_reference.get('response_text', '')[:1000]}
            
            Identifica:
            - Gaps de contenido (qué no están cubriendo los competidores)
            - Ángulos únicos para abordar el tema
            - Tipos de contenido que funcionarían mejor
            - Oportunidades de diferenciación
            - Palabras clave objetivo
            
            Responde en JSON:
            {{
                "content_gaps": ["gap 1", "gap 2"],
                "unique_angles": ["ángulo 1", "ángulo 2"],
                "recommended_content_types": ["tipo 1", "tipo 2"],
                "differentiation_opportunities": ["oportunidad 1", "oportunidad 2"],
                "target_keywords": ["keyword 1", "keyword 2"],
                "content_depth_recommendation": "superficial|medio|profundo",
                "estimated_competition_level": "bajo|medio|alto",
                "content_format_suggestions": ["formato 1", "formato 2"],
                "audience_gaps": ["audiencia no cubierta 1", "audiencia no cubierta 2"]
            }}
            """
            
            response = await asyncio.to_thread(
                self.gemini_model.generate_content, prompt
            )
            
            # Parse JSON response
            analysis_text = response.text.strip()
            json_start = analysis_text.find('{')
            json_end = analysis_text.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_text = analysis_text[json_start:json_end]
                return json.loads(json_text)
            else:
                raise ValueError("Invalid JSON response from Gemini")
                
        except Exception as e:
            logger.error(f"❌ AI opportunities analysis failed: {str(e)}")
            return {}
    
    def _rule_based_opportunities_analysis(
        self, 
        topic: str, 
        google_results: Dict[str, Any], 
        gpt_reference: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Rule-based content opportunities analysis."""
        try:
            results = google_results.get('results', [])
            
            # Analyze content types in top results
            content_types = [self._classify_content_type(result.get('title', '')) for result in results]
            content_type_counts = Counter(content_types)
            
            # Identify missing content types
            all_content_types = ['guide', 'definition', 'comparison', 'list', 'benefits', 'article']
            missing_types = [ct for ct in all_content_types if content_type_counts.get(ct, 0) == 0]
            
            # Analyze title patterns
            titles = [result.get('title', '') for result in results]
            title_lengths = [len(title) for title in titles]
            avg_title_length = sum(title_lengths) / len(title_lengths) if title_lengths else 0
            
            # Identify keyword gaps
            all_titles_text = ' '.join(titles).lower()
            common_words = Counter(all_titles_text.split())
            
            # Generate opportunities based on analysis
            content_gaps = []
            if 'guide' not in content_types:
                content_gaps.append(f"Guía completa sobre {topic}")
            if 'comparison' not in content_types:
                content_gaps.append(f"Comparación de opciones de {topic}")
            if 'benefits' not in content_types:
                content_gaps.append(f"Beneficios y ventajas de {topic}")
            
            # Suggest unique angles
            unique_angles = [
                f"Perspectiva práctica de {topic}",
                f"Casos de uso reales de {topic}",
                f"Errores comunes con {topic}",
                f"Tendencias futuras en {topic}"
            ]
            
            # Estimate competition level
            high_authority_count = sum(1 for result in results if result.get('domain', '') in [
                'wikipedia.org', 'youtube.com', 'amazon.com'
            ])
            
            if high_authority_count >= 3:
                competition_level = "alto"
            elif high_authority_count >= 1:
                competition_level = "medio"
            else:
                competition_level = "bajo"
            
            return {
                "content_gaps": content_gaps,
                "unique_angles": unique_angles,
                "recommended_content_types": missing_types or ["article", "guide"],
                "differentiation_opportunities": [
                    "Contenido más actualizado",
                    "Ejemplos más específicos",
                    "Enfoque más práctico"
                ],
                "target_keywords": [topic] + list(common_words.keys())[:5],
                "content_depth_recommendation": "medio",
                "estimated_competition_level": competition_level,
                "content_format_suggestions": ["artículo largo", "infografía", "video"],
                "audience_gaps": ["principiantes", "usuarios avanzados"]
            }
            
        except Exception as e:
            logger.error(f"❌ Rule-based opportunities analysis failed: {str(e)}")
            return {}
    
    def _merge_opportunities(self, ai_opportunities: Dict[str, Any], rule_opportunities: Dict[str, Any]) -> Dict[str, Any]:
        """Merge AI and rule-based opportunities analysis."""
        try:
            merged = {}
            
            # Merge each field
            for field in ["content_gaps", "unique_angles", "recommended_content_types", 
                         "differentiation_opportunities", "target_keywords", "content_format_suggestions", "audience_gaps"]:
                ai_items = ai_opportunities.get(field, [])
                rule_items = rule_opportunities.get(field, [])
                
                # Combine and deduplicate
                combined = []
                seen = set()
                for item in ai_items + rule_items:
                    if item.lower() not in seen:
                        combined.append(item)
                        seen.add(item.lower())
                
                merged[field] = combined[:10]  # Limit to top 10
            
            # Handle single-value fields
            merged["content_depth_recommendation"] = (
                ai_opportunities.get("content_depth_recommendation") or 
                rule_opportunities.get("content_depth_recommendation", "medio")
            )
            
            merged["estimated_competition_level"] = (
                ai_opportunities.get("estimated_competition_level") or 
                rule_opportunities.get("estimated_competition_level", "medio")
            )
            
            return merged
            
        except Exception as e:
            logger.error(f"❌ Merge opportunities failed: {str(e)}")
            return rule_opportunities if rule_opportunities else ai_opportunities
    
    def _analyze_competitive_landscape(self, google_results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the competitive landscape."""
        try:
            results = google_results.get('results', [])
            
            if not results:
                return {"competition_level": "unknown", "analysis": "No results to analyze"}
            
            # Analyze domains
            domains = [result.get('domain', '') for result in results]
            unique_domains = len(set(domains))
            
            # Analyze authority
            authority_scores = [self._estimate_domain_authority(domain) for domain in domains]
            avg_authority = sum(authority_scores) / len(authority_scores) if authority_scores else 0
            
            # Analyze content diversity
            content_types = [self._classify_content_type(result.get('title', '')) for result in results]
            content_diversity = len(set(content_types))
            
            return {
                "total_competitors": len(results),
                "unique_domains": unique_domains,
                "average_domain_authority": avg_authority,
                "content_type_diversity": content_diversity,
                "top_competitor_domains": domains[:3],
                "competition_intensity": "high" if avg_authority > 70 else "medium" if avg_authority > 50 else "low",
                "market_saturation": "high" if unique_domains < 3 else "medium" if unique_domains < 6 else "low"
            }
            
        except Exception as e:
            logger.error(f"❌ Competitive landscape analysis failed: {str(e)}")
            return {"competition_level": "unknown", "analysis": f"Analysis failed: {str(e)}"}
    
    def _calculate_opportunity_score(self, opportunities: Dict[str, Any], competitive_landscape: Dict[str, Any]) -> float:
        """Calculate overall opportunity score (0-100)."""
        try:
            score = 50.0  # Base score
            
            # Content gaps boost score
            content_gaps = len(opportunities.get("content_gaps", []))
            score += min(content_gaps * 5, 25)
            
            # Unique angles boost score
            unique_angles = len(opportunities.get("unique_angles", []))
            score += min(unique_angles * 3, 15)
            
            # Competition level affects score
            competition_level = opportunities.get("estimated_competition_level", "medio")
            if competition_level == "bajo":
                score += 10
            elif competition_level == "alto":
                score -= 10
            
            # Market saturation affects score
            market_saturation = competitive_landscape.get("market_saturation", "medium")
            if market_saturation == "low":
                score += 10
            elif market_saturation == "high":
                score -= 10
            
            return min(max(score, 0), 100)
            
        except:
            return 50.0
    
    def _get_fallback_content_opportunities(self, topic: str) -> Dict[str, Any]:
        """Provide fallback content opportunities when AI is not available."""
        return {
            "status": "fallback",
            "topic": topic,
            "content_gaps": ["Información básica", "Ejemplos prácticos"],
            "unique_angles": ["Perspectiva práctica", "Enfoque paso a paso"],
            "recommended_content_types": ["article", "guide"],
            "differentiation_opportunities": ["Más ejemplos", "Mejor estructura"],
            "target_keywords": [topic],
            "content_depth_recommendation": "medio",
            "estimated_competition_level": "medio",
            "content_format_suggestions": ["artículo", "guía"],
            "audience_gaps": ["principiantes"],
            "competitive_landscape": {"competition_level": "unknown"},
            "opportunity_score": 50.0
        }

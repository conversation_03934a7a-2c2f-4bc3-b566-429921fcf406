"""
Entity Extractor Module
Extracts entities, questions, and semantic keywords from research data
"""

import logging
import asyncio
import json
import re
from typing import Dict, Any, List
from collections import Counter

import google.generativeai as genai
from app.core.config import settings

logger = logging.getLogger(__name__)

class EntityExtractor:
    """Extracts entities and common questions from research sources."""
    
    def __init__(self):
        self.gemini_model = None
        
        # Initialize Gemini AI
        if settings.GEMINI_API_KEY:
            try:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("✅ Entity Extractor - Gemini AI initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Gemini AI: {e}")
                self.gemini_model = None
        else:
            logger.warning("⚠️ GEMINI_API_KEY not found. Entity extraction will use rule-based approach.")
    
    async def extract_entities_and_questions(
        self, 
        topic: str, 
        google_results: Dict[str, Any], 
        social_insights: Dict[str, Any], 
        gpt_reference: Dict[str, Any], 
        language: str = "es"
    ) -> Dict[str, Any]:
        """
        Extract entities and common questions from all research sources.
        
        Args:
            topic: Main research topic
            google_results: Google search results
            social_insights: Social media insights
            gpt_reference: GPT reference response
            language: Target language
            
        Returns:
            Extracted entities, questions, and semantic keywords
        """
        try:
            if not self.gemini_model:
                return self._get_fallback_entities_and_questions(topic)
            
            # Compile all text sources
            text_sources = self._compile_text_sources(google_results, social_insights, gpt_reference)
            
            # Combine all sources
            combined_text = ' '.join(text_sources)
            
            # Use AI to extract entities and questions
            ai_extraction = await self._ai_extract_entities_and_questions(topic, combined_text)
            
            # Enhance with rule-based extraction
            rule_based_extraction = self._rule_based_extraction(topic, text_sources)
            
            # Merge and deduplicate results
            merged_results = self._merge_extraction_results(ai_extraction, rule_based_extraction)
            
            # Add frequency analysis
            frequency_analysis = self._analyze_term_frequency(text_sources, merged_results)
            
            logger.info(f"✅ Entities and questions extracted for '{topic}'")
            return {
                "status": "success",
                "topic": topic,
                **merged_results,
                "frequency_analysis": frequency_analysis,
                "extraction_confidence": self._calculate_extraction_confidence(merged_results)
            }
                
        except Exception as e:
            logger.error(f"❌ Entity extraction failed: {str(e)}")
            return self._get_fallback_entities_and_questions(topic)
    
    def _compile_text_sources(
        self, 
        google_results: Dict[str, Any], 
        social_insights: Dict[str, Any], 
        gpt_reference: Dict[str, Any]
    ) -> List[str]:
        """Compile text from all research sources."""
        text_sources = []
        
        # Add Google results
        for result in google_results.get('results', []):
            text_sources.append(result.get('title', ''))
            text_sources.append(result.get('snippet', ''))
        
        # Add SERP features
        serp_features = google_results.get('serp_features', {})
        for question in serp_features.get('people_also_ask', []):
            text_sources.append(question.get('question', ''))
        
        # Add social insights
        for platform, insights in social_insights.items():
            if insights.get('status') == 'success':
                for insight in insights.get('insights', []):
                    if platform == 'reddit':
                        text_sources.append(insight.get('title', ''))
                        text_sources.append(insight.get('snippet', ''))
                    elif platform == 'quora':
                        text_sources.append(insight.get('question', ''))
                        text_sources.append(insight.get('answer_preview', ''))
        
        # Add GPT reference
        if gpt_reference.get('status') == 'success':
            text_sources.append(gpt_reference.get('response_text', ''))
        
        # Filter out empty strings
        return [text for text in text_sources if text.strip()]
    
    async def _ai_extract_entities_and_questions(self, topic: str, combined_text: str) -> Dict[str, Any]:
        """Use AI to extract entities and questions."""
        try:
            # Limit text to avoid token limits
            limited_text = combined_text[:4000]
            
            prompt = f"""
            Analiza el siguiente contenido relacionado con "{topic}" y extrae:
            
            1. ENTIDADES CLAVE (personas, lugares, conceptos, marcas, productos)
            2. PREGUNTAS COMUNES que la gente hace sobre este tema
            3. TÉRMINOS TÉCNICOS importantes
            4. CONCEPTOS RELACIONADOS
            
            Contenido a analizar:
            {limited_text}
            
            Responde en formato JSON:
            {{
                "entities": {{
                    "people": ["persona 1", "persona 2"],
                    "places": ["lugar 1", "lugar 2"],
                    "concepts": ["concepto 1", "concepto 2"],
                    "brands": ["marca 1", "marca 2"],
                    "products": ["producto 1", "producto 2"],
                    "organizations": ["organización 1", "organización 2"]
                }},
                "common_questions": [
                    "¿Pregunta frecuente 1?",
                    "¿Pregunta frecuente 2?"
                ],
                "technical_terms": [
                    "término técnico 1",
                    "término técnico 2"
                ],
                "related_concepts": [
                    "concepto relacionado 1",
                    "concepto relacionado 2"
                ],
                "semantic_keywords": [
                    "palabra clave semántica 1",
                    "palabra clave semántica 2"
                ]
            }}
            """
            
            response = await asyncio.to_thread(
                self.gemini_model.generate_content, prompt
            )
            
            # Parse JSON response
            analysis_text = response.text.strip()
            json_start = analysis_text.find('{')
            json_end = analysis_text.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_text = analysis_text[json_start:json_end]
                return json.loads(json_text)
            else:
                raise ValueError("Invalid JSON response from Gemini")
                
        except Exception as e:
            logger.error(f"❌ AI entity extraction failed: {str(e)}")
            return {}
    
    def _rule_based_extraction(self, topic: str, text_sources: List[str]) -> Dict[str, Any]:
        """Rule-based entity and question extraction."""
        try:
            combined_text = ' '.join(text_sources).lower()
            
            # Extract questions using patterns
            questions = []
            for text in text_sources:
                # Find questions
                question_matches = re.findall(r'[¿?][^¿?]*[?¿]', text)
                questions.extend([q.strip() for q in question_matches if len(q.strip()) > 10])
            
            # Extract potential entities using capitalization patterns
            entities = {
                "people": [],
                "places": [],
                "concepts": [topic],
                "brands": [],
                "products": [],
                "organizations": []
            }
            
            # Find capitalized words (potential proper nouns)
            for text in text_sources:
                words = text.split()
                for word in words:
                    if (word[0].isupper() and len(word) > 3 and 
                        word.lower() not in ['the', 'and', 'for', 'with', 'que', 'para', 'con', 'por']):
                        # Simple classification based on context
                        if any(indicator in text.lower() for indicator in ['dr.', 'prof.', 'autor']):
                            entities["people"].append(word)
                        elif any(indicator in text.lower() for indicator in ['ciudad', 'país', 'región']):
                            entities["places"].append(word)
                        else:
                            entities["concepts"].append(word)
            
            # Extract technical terms (words with specific patterns)
            technical_terms = []
            tech_patterns = [
                r'\b[a-zA-Z]+ción\b',  # Words ending in -ción
                r'\b[a-zA-Z]+miento\b',  # Words ending in -miento
                r'\b[a-zA-Z]{8,}\b'  # Long words (often technical)
            ]
            
            for pattern in tech_patterns:
                matches = re.findall(pattern, combined_text)
                technical_terms.extend(matches)
            
            # Generate semantic keywords based on frequency
            word_freq = Counter(combined_text.split())
            # Filter out common words and get meaningful keywords
            stop_words = {'el', 'la', 'de', 'que', 'y', 'en', 'un', 'es', 'se', 'no', 'te', 'lo', 'le', 'da', 'su', 'por', 'son', 'con', 'para', 'al', 'del', 'los', 'las'}
            semantic_keywords = [
                word for word, freq in word_freq.most_common(20) 
                if len(word) > 3 and word not in stop_words and freq > 1
            ]
            
            return {
                "entities": {k: list(set(v)) for k, v in entities.items()},  # Remove duplicates
                "common_questions": list(set(questions))[:10],  # Top 10 unique questions
                "technical_terms": list(set(technical_terms))[:10],
                "related_concepts": semantic_keywords[:10],
                "semantic_keywords": semantic_keywords[:15]
            }
            
        except Exception as e:
            logger.error(f"❌ Rule-based extraction failed: {str(e)}")
            return {}
    
    def _merge_extraction_results(self, ai_results: Dict[str, Any], rule_results: Dict[str, Any]) -> Dict[str, Any]:
        """Merge AI and rule-based extraction results."""
        try:
            merged = {
                "entities": {
                    "people": [],
                    "places": [],
                    "concepts": [],
                    "brands": [],
                    "products": [],
                    "organizations": []
                },
                "common_questions": [],
                "technical_terms": [],
                "related_concepts": [],
                "semantic_keywords": []
            }
            
            # Merge entities
            for category in merged["entities"].keys():
                ai_entities = ai_results.get("entities", {}).get(category, [])
                rule_entities = rule_results.get("entities", {}).get(category, [])
                
                # Combine and deduplicate
                combined = list(set(ai_entities + rule_entities))
                merged["entities"][category] = combined[:10]  # Limit to top 10
            
            # Merge other fields
            for field in ["common_questions", "technical_terms", "related_concepts", "semantic_keywords"]:
                ai_items = ai_results.get(field, [])
                rule_items = rule_results.get(field, [])
                
                # Combine and deduplicate
                combined = []
                seen = set()
                for item in ai_items + rule_items:
                    if item.lower() not in seen:
                        combined.append(item)
                        seen.add(item.lower())
                
                merged[field] = combined[:15 if field == "semantic_keywords" else 10]
            
            return merged
            
        except Exception as e:
            logger.error(f"❌ Merge extraction results failed: {str(e)}")
            return rule_results if rule_results else ai_results
    
    def _analyze_term_frequency(self, text_sources: List[str], extraction_results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze frequency of extracted terms across sources."""
        try:
            combined_text = ' '.join(text_sources).lower()
            
            # Analyze frequency of semantic keywords
            keyword_frequencies = {}
            for keyword in extraction_results.get("semantic_keywords", []):
                frequency = combined_text.count(keyword.lower())
                keyword_frequencies[keyword] = frequency
            
            # Analyze question patterns
            question_patterns = {}
            for question in extraction_results.get("common_questions", []):
                if question.startswith('¿Qué'):
                    question_patterns["definition"] = question_patterns.get("definition", 0) + 1
                elif question.startswith('¿Cómo'):
                    question_patterns["how_to"] = question_patterns.get("how_to", 0) + 1
                elif question.startswith('¿Por qué'):
                    question_patterns["explanation"] = question_patterns.get("explanation", 0) + 1
                else:
                    question_patterns["other"] = question_patterns.get("other", 0) + 1
            
            return {
                "keyword_frequencies": keyword_frequencies,
                "question_patterns": question_patterns,
                "most_frequent_keyword": max(keyword_frequencies, key=keyword_frequencies.get) if keyword_frequencies else None,
                "dominant_question_type": max(question_patterns, key=question_patterns.get) if question_patterns else None
            }
            
        except Exception as e:
            logger.error(f"❌ Term frequency analysis failed: {str(e)}")
            return {}
    
    def _calculate_extraction_confidence(self, extraction_results: Dict[str, Any]) -> float:
        """Calculate confidence score for extraction results."""
        try:
            # Count total extracted items
            total_entities = sum(len(entities) for entities in extraction_results.get("entities", {}).values())
            total_questions = len(extraction_results.get("common_questions", []))
            total_terms = len(extraction_results.get("technical_terms", []))
            total_concepts = len(extraction_results.get("related_concepts", []))
            
            total_items = total_entities + total_questions + total_terms + total_concepts
            
            # Calculate confidence based on quantity and diversity
            if total_items >= 30:
                confidence = 0.9
            elif total_items >= 20:
                confidence = 0.8
            elif total_items >= 10:
                confidence = 0.7
            elif total_items >= 5:
                confidence = 0.6
            else:
                confidence = 0.4
            
            # Bonus for diversity (having items in multiple categories)
            categories_with_items = sum(1 for category in [total_entities, total_questions, total_terms, total_concepts] if category > 0)
            diversity_bonus = categories_with_items * 0.05
            
            return min(confidence + diversity_bonus, 1.0)
            
        except:
            return 0.5
    
    def _get_fallback_entities_and_questions(self, topic: str) -> Dict[str, Any]:
        """Provide fallback entities and questions when AI is not available."""
        return {
            "status": "fallback",
            "topic": topic,
            "entities": {
                "people": [],
                "places": [],
                "concepts": [topic],
                "brands": [],
                "products": [],
                "organizations": []
            },
            "common_questions": [
                f"¿Qué es {topic}?",
                f"¿Cómo funciona {topic}?",
                f"¿Para qué sirve {topic}?",
                f"¿Cuáles son los beneficios de {topic}?",
                f"¿Dónde encontrar {topic}?"
            ],
            "technical_terms": [],
            "related_concepts": [],
            "semantic_keywords": [topic],
            "frequency_analysis": {},
            "extraction_confidence": 0.3
        }

"""
Intent Analyzer Module
Analyzes search intent behind topics using AI
"""

import logging
import asyncio
import json
from typing import Dict, Any

import google.generativeai as genai
from app.core.config import settings

logger = logging.getLogger(__name__)

class IntentAnalyzer:
    """Analyzes search intent for topics using Gemini AI."""
    
    def __init__(self):
        self.gemini_model = None
        
        # Initialize Gemini AI
        if settings.GEMINI_API_KEY:
            try:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("✅ Intent Analyzer - Gemini AI initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Gemini AI: {e}")
                self.gemini_model = None
        else:
            logger.warning("⚠️ GEMINI_API_KEY not found. Intent analysis will use fallback mode.")
    
    async def analyze_search_intent(self, topic: str, language: str = "es") -> Dict[str, Any]:
        """
        Analyze the search intent behind a topic using Gemini AI.
        
        Args:
            topic: The topic to analyze
            language: Target language for analysis
            
        Returns:
            Detailed intent analysis
        """
        try:
            if not self.gemini_model:
                return self._get_fallback_intent_analysis(topic)
            
            prompt = self._build_intent_analysis_prompt(topic, language)
            
            response = await asyncio.to_thread(
                self.gemini_model.generate_content, prompt
            )
            
            # Parse JSON response
            analysis_text = response.text.strip()
            json_start = analysis_text.find('{')
            json_end = analysis_text.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_text = analysis_text[json_start:json_end]
                intent_analysis = json.loads(json_text)
                logger.info(f"✅ Intent analysis completed for '{topic}'")
                return intent_analysis
            else:
                raise ValueError("Invalid JSON response from Gemini")
                
        except Exception as e:
            logger.error(f"❌ Intent analysis failed: {str(e)}")
            return self._get_fallback_intent_analysis(topic)
    
    def _build_intent_analysis_prompt(self, topic: str, language: str) -> str:
        """Build the prompt for intent analysis."""
        return f"""
        Analiza la intención de búsqueda para el siguiente tema: "{topic}"
        
        Proporciona un análisis detallado en formato JSON con la siguiente estructura:
        {{
            "intent_type": "informational|commercial|transactional|navigational",
            "intent_confidence": 0.0-1.0,
            "user_motivation": "descripción de qué busca el usuario",
            "search_stage": "awareness|consideration|decision",
            "content_type_preference": "artículo|guía|comparación|tutorial|lista",
            "target_audience": "descripción de la audiencia objetivo",
            "pain_points": ["punto de dolor 1", "punto de dolor 2"],
            "search_variations": ["variación 1", "variación 2", "variación 3"],
            "related_topics": ["tema relacionado 1", "tema relacionado 2"],
            "optimal_content_length": "corto|medio|largo",
            "preferred_tone": "formal|informal|técnico|conversacional",
            "seasonality": "alta|media|baja",
            "competition_level": "bajo|medio|alto",
            "commercial_intent_signals": ["señal 1", "señal 2"],
            "information_depth_needed": "superficial|medio|profundo"
        }}
        
        Responde únicamente con el JSON válido, sin texto adicional.
        """
    
    def _get_fallback_intent_analysis(self, topic: str) -> Dict[str, Any]:
        """Provide fallback intent analysis when AI is not available."""
        # Simple rule-based intent detection
        topic_lower = topic.lower()
        
        # Detect intent type based on keywords
        intent_type = "informational"  # Default
        intent_confidence = 0.5
        
        if any(word in topic_lower for word in ['comprar', 'precio', 'costo', 'barato', 'oferta']):
            intent_type = "commercial"
            intent_confidence = 0.8
        elif any(word in topic_lower for word in ['cómo', 'qué es', 'para qué', 'beneficios']):
            intent_type = "informational"
            intent_confidence = 0.9
        elif any(word in topic_lower for word in ['descargar', 'registrarse', 'suscribirse']):
            intent_type = "transactional"
            intent_confidence = 0.8
        
        # Detect content type preference
        content_type = "artículo"
        if 'cómo' in topic_lower:
            content_type = "guía"
        elif any(word in topic_lower for word in ['mejor', 'vs', 'comparar']):
            content_type = "comparación"
        elif any(word in topic_lower for word in ['lista', 'tipos', 'ejemplos']):
            content_type = "lista"
        
        # Detect content length
        optimal_length = "medio"
        if any(word in topic_lower for word in ['qué es', 'definición']):
            optimal_length = "corto"
        elif any(word in topic_lower for word in ['guía completa', 'todo sobre', 'curso']):
            optimal_length = "largo"
        
        return {
            "intent_type": intent_type,
            "intent_confidence": intent_confidence,
            "user_motivation": f"Buscar información sobre {topic}",
            "search_stage": "awareness" if intent_type == "informational" else "consideration",
            "content_type_preference": content_type,
            "target_audience": "Audiencia general interesada en el tema",
            "pain_points": ["Falta de información clara", "Necesidad de orientación"],
            "search_variations": [topic, f"qué es {topic}", f"cómo {topic}"],
            "related_topics": ["temas relacionados"],
            "optimal_content_length": optimal_length,
            "preferred_tone": "conversacional",
            "seasonality": "media",
            "competition_level": "medio",
            "commercial_intent_signals": [],
            "information_depth_needed": "medio"
        }
    
    def classify_intent_strength(self, intent_analysis: Dict[str, Any]) -> str:
        """Classify the strength of the intent signal."""
        confidence = intent_analysis.get('intent_confidence', 0.5)
        
        if confidence >= 0.8:
            return "strong"
        elif confidence >= 0.6:
            return "medium"
        else:
            return "weak"
    
    def get_content_recommendations(self, intent_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Get content recommendations based on intent analysis."""
        intent_type = intent_analysis.get('intent_type', 'informational')
        content_type = intent_analysis.get('content_type_preference', 'artículo')
        length = intent_analysis.get('optimal_content_length', 'medio')
        
        recommendations = {
            "primary_content_type": content_type,
            "recommended_length": length,
            "tone": intent_analysis.get('preferred_tone', 'conversacional'),
            "structure_suggestions": [],
            "cta_recommendations": []
        }
        
        # Add structure suggestions based on intent
        if intent_type == "informational":
            recommendations["structure_suggestions"] = [
                "Introducción clara del tema",
                "Definiciones y conceptos básicos",
                "Ejemplos prácticos",
                "Conclusión con puntos clave"
            ]
        elif intent_type == "commercial":
            recommendations["structure_suggestions"] = [
                "Comparación de opciones",
                "Pros y contras",
                "Recomendaciones específicas",
                "Llamada a la acción clara"
            ]
            recommendations["cta_recommendations"] = [
                "Incluir enlaces a productos/servicios",
                "Botones de acción visibles",
                "Información de contacto"
            ]
        
        return recommendations

"""
Social Insights Extractor Module
Extracts insights from Reddit, Quora and other social platforms
"""

import logging
import aiohttp
import re
from typing import Dict, Any, List

from app.core.config import settings

logger = logging.getLogger(__name__)

class SocialInsightsExtractor:
    """Extracts insights from social media platforms."""
    
    def __init__(self):
        self.serper_api_key = settings.SERPER_API_KEY
        
        if not self.serper_api_key:
            logger.warning("⚠️ SERPER_API_KEY not found. Social insights will be limited.")
    
    async def get_reddit_insights(self, topic: str, target_country: str = "ES", target_language: str = "es") -> Dict[str, Any]:
        """
        Get Reddit insights using Serper API with geographic targeting.

        Args:
            topic: Topic to search for on Reddit
            target_country: Target country code
            target_language: Target language code

        Returns:
            Reddit insights and discussion points
        """
        try:
            if not self.serper_api_key:
                return {"status": "unavailable", "message": "Serper API not configured"}
            
            # Search Reddit specifically
            reddit_query = f"site:reddit.com {topic}"
            
            url = "https://google.serper.dev/search"
            headers = {
                "X-API-KEY": self.serper_api_key,
                "Content-Type": "application/json"
            }
            
            # Country and language mapping
            country_mapping = {
                "ES": "es", "MX": "mx", "AR": "ar", "CO": "co", "CL": "cl",
                "PE": "pe", "VE": "ve", "US": "us", "GB": "uk", "FR": "fr",
                "DE": "de", "IT": "it", "BR": "br", "GLOBAL": "us"
            }
            language_mapping = {
                "es": "es", "en": "en", "fr": "fr", "de": "de", "it": "it", "pt": "pt"
            }

            country_code = country_mapping.get(target_country, "es")
            lang_code = language_mapping.get(target_language, "es")

            payload = {
                "q": reddit_query,
                "num": 5,
                "gl": country_code,
                "hl": lang_code
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, headers=headers, timeout=20) as response:
                    if response.status == 200:
                        data = await response.json()
                        organic_results = data.get('organic', [])
                        
                        reddit_insights = []
                        for result in organic_results:
                            insight = {
                                "title": result.get('title', ''),
                                "link": result.get('link', ''),
                                "snippet": result.get('snippet', ''),
                                "subreddit": self._extract_subreddit_from_url(result.get('link', '')),
                                "discussion_points": self._extract_discussion_points(result.get('snippet', '')),
                                "engagement_indicators": self._analyze_engagement_indicators(result.get('title', '')),
                                "post_type": self._classify_reddit_post_type(result.get('title', ''))
                            }
                            reddit_insights.append(insight)
                        
                        # Analyze overall Reddit sentiment
                        sentiment_analysis = self._analyze_reddit_sentiment(reddit_insights)
                        
                        logger.info(f"✅ Retrieved {len(reddit_insights)} Reddit insights for '{topic}'")
                        return {
                            "status": "success",
                            "total_results": len(reddit_insights),
                            "insights": reddit_insights,
                            "sentiment_analysis": sentiment_analysis,
                            "top_subreddits": self._get_top_subreddits(reddit_insights)
                        }
                    else:
                        return {"status": "error", "message": f"API returned status {response.status}"}
                        
        except Exception as e:
            logger.error(f"❌ Reddit insights failed: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    async def get_quora_insights(self, topic: str, target_country: str = "ES", target_language: str = "es") -> Dict[str, Any]:
        """
        Get Quora insights using Serper API with geographic targeting.

        Args:
            topic: Topic to search for on Quora
            target_country: Target country code
            target_language: Target language code

        Returns:
            Quora questions and answers insights
        """
        try:
            if not self.serper_api_key:
                return {"status": "unavailable", "message": "Serper API not configured"}
            
            # Search Quora specifically
            quora_query = f"site:quora.com {topic}"
            
            url = "https://google.serper.dev/search"
            headers = {
                "X-API-KEY": self.serper_api_key,
                "Content-Type": "application/json"
            }
            
            # Use same country and language mapping
            country_mapping = {
                "ES": "es", "MX": "mx", "AR": "ar", "CO": "co", "CL": "cl",
                "PE": "pe", "VE": "ve", "US": "us", "GB": "uk", "FR": "fr",
                "DE": "de", "IT": "it", "BR": "br", "GLOBAL": "us"
            }
            language_mapping = {
                "es": "es", "en": "en", "fr": "fr", "de": "de", "it": "it", "pt": "pt"
            }

            country_code = country_mapping.get(target_country, "es")
            lang_code = language_mapping.get(target_language, "es")

            payload = {
                "q": quora_query,
                "num": 5,
                "gl": country_code,
                "hl": lang_code
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, headers=headers, timeout=20) as response:
                    if response.status == 200:
                        data = await response.json()
                        organic_results = data.get('organic', [])
                        
                        quora_insights = []
                        for result in organic_results:
                            insight = {
                                "question": result.get('title', ''),
                                "link": result.get('link', ''),
                                "answer_preview": result.get('snippet', ''),
                                "question_type": self._classify_question_type(result.get('title', '')),
                                "complexity_level": self._assess_question_complexity(result.get('title', '')),
                                "answer_quality_indicators": self._analyze_answer_quality(result.get('snippet', ''))
                            }
                            quora_insights.append(insight)
                        
                        # Analyze question patterns
                        question_analysis = self._analyze_question_patterns(quora_insights)
                        
                        logger.info(f"✅ Retrieved {len(quora_insights)} Quora insights for '{topic}'")
                        return {
                            "status": "success",
                            "total_results": len(quora_insights),
                            "insights": quora_insights,
                            "question_analysis": question_analysis
                        }
                    else:
                        return {"status": "error", "message": f"API returned status {response.status}"}
                        
        except Exception as e:
            logger.error(f"❌ Quora insights failed: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    def _extract_subreddit_from_url(self, url: str) -> str:
        """Extract subreddit name from Reddit URL."""
        try:
            match = re.search(r'reddit\.com/r/([^/]+)', url)
            return match.group(1) if match else "unknown"
        except:
            return "unknown"
    
    def _extract_discussion_points(self, snippet: str) -> List[str]:
        """Extract key discussion points from Reddit snippet."""
        try:
            # Simple extraction of sentences that might be discussion points
            sentences = snippet.split('.')
            points = []
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) > 20 and any(word in sentence.lower() for word in ['porque', 'como', 'que', 'cuando', 'donde']):
                    points.append(sentence)
            return points[:3]  # Return top 3 points
        except:
            return []
    
    def _analyze_engagement_indicators(self, title: str) -> Dict[str, Any]:
        """Analyze engagement indicators from Reddit post title."""
        title_lower = title.lower()
        
        indicators = {
            "has_question": '?' in title,
            "has_exclamation": '!' in title,
            "has_caps": any(word.isupper() for word in title.split() if len(word) > 2),
            "has_numbers": bool(re.search(r'\d+', title)),
            "emotional_words": sum(1 for word in ['amazing', 'terrible', 'love', 'hate', 'best', 'worst'] if word in title_lower),
            "engagement_score": 0
        }
        
        # Calculate engagement score
        score = 0
        if indicators["has_question"]: score += 2
        if indicators["has_exclamation"]: score += 1
        if indicators["has_caps"]: score += 1
        if indicators["has_numbers"]: score += 1
        score += indicators["emotional_words"]
        
        indicators["engagement_score"] = min(score, 10)  # Cap at 10
        
        return indicators
    
    def _classify_reddit_post_type(self, title: str) -> str:
        """Classify the type of Reddit post."""
        title_lower = title.lower()
        
        if title_lower.startswith(('eli5', 'explain like')):
            return "explanation_request"
        elif '?' in title:
            return "question"
        elif any(word in title_lower for word in ['review', 'opinion', 'thoughts']):
            return "review_opinion"
        elif any(word in title_lower for word in ['tip', 'advice', 'help']):
            return "advice_seeking"
        elif any(word in title_lower for word in ['psa', 'warning', 'beware']):
            return "warning_psa"
        else:
            return "discussion"
    
    def _classify_question_type(self, question: str) -> str:
        """Classify the type of question based on its content."""
        question_lower = question.lower()
        
        if any(word in question_lower for word in ['qué es', 'what is', 'define']):
            return "definition"
        elif any(word in question_lower for word in ['cómo', 'how to', 'como']):
            return "how_to"
        elif any(word in question_lower for word in ['por qué', 'why', 'porque']):
            return "explanation"
        elif any(word in question_lower for word in ['cuál', 'which', 'mejor']):
            return "comparison"
        elif any(word in question_lower for word in ['cuándo', 'when', 'cuando']):
            return "timing"
        elif any(word in question_lower for word in ['dónde', 'where', 'donde']):
            return "location"
        else:
            return "general"
    
    def _assess_question_complexity(self, question: str) -> str:
        """Assess the complexity level of a question."""
        word_count = len(question.split())
        technical_terms = sum(1 for word in question.split() if len(word) > 8)
        
        if word_count > 15 or technical_terms > 2:
            return "complex"
        elif word_count > 8 or technical_terms > 0:
            return "medium"
        else:
            return "simple"
    
    def _analyze_answer_quality(self, answer_preview: str) -> Dict[str, Any]:
        """Analyze quality indicators of Quora answer preview."""
        return {
            "length": len(answer_preview),
            "has_examples": any(word in answer_preview.lower() for word in ['example', 'ejemplo', 'for instance']),
            "has_statistics": bool(re.search(r'\d+%|\d+\.\d+%', answer_preview)),
            "has_sources": any(word in answer_preview.lower() for word in ['according', 'study', 'research', 'según']),
            "professional_tone": any(word in answer_preview.lower() for word in ['experience', 'professional', 'expert'])
        }
    
    def _analyze_reddit_sentiment(self, insights: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze overall sentiment from Reddit insights."""
        if not insights:
            return {"sentiment": "neutral", "confidence": 0.0}
        
        positive_indicators = 0
        negative_indicators = 0
        total_posts = len(insights)
        
        for insight in insights:
            title = insight.get('title', '').lower()
            snippet = insight.get('snippet', '').lower()
            combined_text = f"{title} {snippet}"
            
            # Simple sentiment analysis
            positive_words = ['good', 'great', 'amazing', 'love', 'best', 'excellent', 'bueno', 'genial']
            negative_words = ['bad', 'terrible', 'hate', 'worst', 'awful', 'horrible', 'malo', 'terrible']
            
            pos_count = sum(1 for word in positive_words if word in combined_text)
            neg_count = sum(1 for word in negative_words if word in combined_text)
            
            if pos_count > neg_count:
                positive_indicators += 1
            elif neg_count > pos_count:
                negative_indicators += 1
        
        if positive_indicators > negative_indicators:
            sentiment = "positive"
            confidence = positive_indicators / total_posts
        elif negative_indicators > positive_indicators:
            sentiment = "negative"
            confidence = negative_indicators / total_posts
        else:
            sentiment = "neutral"
            confidence = 0.5
        
        return {
            "sentiment": sentiment,
            "confidence": confidence,
            "positive_posts": positive_indicators,
            "negative_posts": negative_indicators,
            "neutral_posts": total_posts - positive_indicators - negative_indicators
        }
    
    def _analyze_question_patterns(self, insights: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze patterns in Quora questions."""
        if not insights:
            return {}
        
        question_types = {}
        complexity_levels = {}
        
        for insight in insights:
            q_type = insight.get('question_type', 'general')
            complexity = insight.get('complexity_level', 'medium')
            
            question_types[q_type] = question_types.get(q_type, 0) + 1
            complexity_levels[complexity] = complexity_levels.get(complexity, 0) + 1
        
        return {
            "most_common_question_type": max(question_types, key=question_types.get) if question_types else "general",
            "question_type_distribution": question_types,
            "complexity_distribution": complexity_levels,
            "average_complexity": max(complexity_levels, key=complexity_levels.get) if complexity_levels else "medium"
        }
    
    def _get_top_subreddits(self, insights: List[Dict[str, Any]]) -> List[str]:
        """Get top subreddits from Reddit insights."""
        subreddit_counts = {}
        
        for insight in insights:
            subreddit = insight.get('subreddit', 'unknown')
            if subreddit != 'unknown':
                subreddit_counts[subreddit] = subreddit_counts.get(subreddit, 0) + 1
        
        # Sort by count and return top subreddits
        sorted_subreddits = sorted(subreddit_counts.items(), key=lambda x: x[1], reverse=True)
        return [subreddit for subreddit, count in sorted_subreddits[:5]]

    async def get_news_insights(self, topic: str, target_country: str = "ES", target_language: str = "es") -> Dict[str, Any]:
        """
        Get news insights using Serper News API.

        Args:
            topic: Topic to search for in news
            target_country: Target country code
            target_language: Target language code

        Returns:
            Recent news articles and insights
        """
        try:
            if not self.serper_api_key:
                return {"status": "unavailable", "message": "Serper API not configured"}

            url = "https://google.serper.dev/news"
            headers = {
                "X-API-KEY": self.serper_api_key,
                "Content-Type": "application/json"
            }

            # Country and language mapping
            country_mapping = {
                "ES": "es", "MX": "mx", "AR": "ar", "CO": "co", "CL": "cl",
                "PE": "pe", "VE": "ve", "US": "us", "GB": "uk", "FR": "fr",
                "DE": "de", "IT": "it", "BR": "br", "GLOBAL": "us"
            }
            language_mapping = {
                "es": "es", "en": "en", "fr": "fr", "de": "de", "it": "it", "pt": "pt"
            }

            country_code = country_mapping.get(target_country, "es")
            lang_code = language_mapping.get(target_language, "es")

            payload = {
                "q": topic,
                "num": 5,
                "gl": country_code,
                "hl": lang_code,
                "tbs": "qdr:m"  # Last month
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, headers=headers, timeout=30) as response:
                    if response.status == 200:
                        data = await response.json()
                        news_results = data.get('news', [])

                        # Process news results
                        processed_news = []
                        trending_topics = []

                        for article in news_results:
                            processed_article = {
                                "title": article.get('title', ''),
                                "snippet": article.get('snippet', ''),
                                "link": article.get('link', ''),
                                "source": article.get('source', ''),
                                "date": article.get('date', ''),
                                "imageUrl": article.get('imageUrl', ''),
                                "position": article.get('position', 0)
                            }
                            processed_news.append(processed_article)

                            # Extract trending topics from titles
                            title_words = article.get('title', '').lower().split()
                            for word in title_words:
                                if len(word) > 4 and word not in ['para', 'como', 'esta', 'desde', 'hasta', 'sobre']:
                                    trending_topics.append(word)

                        # Count trending topics
                        topic_counts = {}
                        for topic_word in trending_topics:
                            topic_counts[topic_word] = topic_counts.get(topic_word, 0) + 1

                        # Get top trending topics
                        top_trending = sorted(topic_counts.items(), key=lambda x: x[1], reverse=True)[:5]

                        logger.info(f"✅ Retrieved {len(processed_news)} news articles for '{topic}'")
                        return {
                            "status": "success",
                            "total_articles": len(processed_news),
                            "articles": processed_news,
                            "trending_topics": [topic for topic, count in top_trending],
                            "analysis": {
                                "recent_coverage": len(processed_news) > 0,
                                "media_interest": "high" if len(processed_news) >= 3 else "medium" if len(processed_news) >= 1 else "low",
                                "top_sources": list(set([article.get('source', '') for article in processed_news[:3]]))
                            }
                        }
                    else:
                        logger.error(f"❌ News API returned status {response.status}")
                        return {"status": "error", "message": f"News API error: {response.status}"}

        except Exception as e:
            logger.error(f"❌ News insights extraction failed: {str(e)}")
            return {"status": "error", "message": f"Failed to extract news insights: {str(e)}"}

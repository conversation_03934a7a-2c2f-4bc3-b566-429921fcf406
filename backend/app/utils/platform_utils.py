"""
Platform-specific utilities for social media optimization.
"""

from typing import Dict, <PERSON>, <PERSON><PERSON>


def get_platform_dimensions(platform: str, content_type: str = "instagram_posts") -> Dict[str, int]:
    """
    Get platform-specific image dimensions.
    
    Args:
        platform: Target platform (Instagram, LinkedIn, Facebook, X)
        content_type: Type of content being generated
        
    Returns:
        Dictionary with width and height dimensions
    """
    # Platform-specific dimensions optimized for each social media platform
    dimensions = {
        "Instagram": {
            "width": 1080,
            "height": 1080,  # Square format for optimal Instagram display
            "aspect_ratio": "1:1"
        },
        "LinkedIn": {
            "width": 1200,
            "height": 627,   # LinkedIn recommended image size
            "aspect_ratio": "1.91:1"
        },
        "Facebook": {
            "width": 1200,
            "height": 630,   # Facebook link preview size
            "aspect_ratio": "1.9:1"
        },
        "X": {
            "width": 1200,
            "height": 675,   # Twitter/X recommended size
            "aspect_ratio": "16:9"
        }
    }
    
    return dimensions.get(platform, dimensions["Instagram"])


def get_platform_character_limits(platform: str) -> Dict[str, int]:
    """
    Get platform-specific character limits for optimal engagement.
    
    Args:
        platform: Target platform
        
    Returns:
        Dictionary with optimal and maximum character limits
    """
    limits = {
        "Instagram": {
            "optimal": 125,    # Optimal for engagement (before "more" cutoff)
            "maximum": 2200,   # Technical maximum
            "hashtag_limit": 30,
            "hashtag_optimal": 5
        },
        "LinkedIn": {
            "optimal": 150,    # Optimal for professional engagement
            "maximum": 3000,   # Technical maximum
            "hashtag_limit": 3,
            "hashtag_optimal": 3
        },
        "Facebook": {
            "optimal": 80,     # Optimal for engagement (shorter is better)
            "maximum": 63206,  # Technical maximum
            "hashtag_limit": 30,
            "hashtag_optimal": 2
        },
        "X": {
            "optimal": 100,    # Optimal for engagement
            "maximum": 280,    # Technical maximum
            "hashtag_limit": 2,
            "hashtag_optimal": 2
        }
    }
    
    return limits.get(platform, limits["Instagram"])


def optimize_hashtags_for_platform(hashtags: list, platform: str) -> list:
    """
    Optimize hashtags for specific platform requirements.
    
    Args:
        hashtags: List of hashtags
        platform: Target platform
        
    Returns:
        Optimized hashtag list for the platform
    """
    limits = get_platform_character_limits(platform)
    optimal_count = limits["hashtag_optimal"]
    
    # Return optimal number of hashtags for the platform
    return hashtags[:optimal_count]


def get_platform_posting_best_practices(platform: str) -> Dict[str, Any]:
    """
    Get platform-specific best practices for content optimization.
    
    Args:
        platform: Target platform
        
    Returns:
        Dictionary with best practices
    """
    practices = {
        "Instagram": {
            "optimal_post_length": 125,
            "image_format": "square",
            "hashtag_strategy": "mix_popular_and_niche",
            "engagement_features": ["stories", "reels", "carousel"],
            "best_posting_times": ["11am-1pm", "7pm-9pm"],
            "content_style": "visual_first"
        },
        "LinkedIn": {
            "optimal_post_length": 150,
            "image_format": "horizontal",
            "hashtag_strategy": "professional_industry_specific",
            "engagement_features": ["articles", "polls", "documents"],
            "best_posting_times": ["8am-10am", "12pm-2pm", "5pm-6pm"],
            "content_style": "professional_insights"
        },
        "Facebook": {
            "optimal_post_length": 80,
            "image_format": "horizontal",
            "hashtag_strategy": "minimal_relevant",
            "engagement_features": ["events", "groups", "live"],
            "best_posting_times": ["1pm-3pm", "3pm-4pm"],
            "content_style": "community_focused"
        },
        "X": {
            "optimal_post_length": 100,
            "image_format": "horizontal",
            "hashtag_strategy": "trending_relevant",
            "engagement_features": ["threads", "spaces", "polls"],
            "best_posting_times": ["9am-10am", "12pm-1pm", "5pm-6pm"],
            "content_style": "concise_timely"
        }
    }
    
    return practices.get(platform, practices["Instagram"])


def format_content_for_platform(content: str, platform: str) -> str:
    """
    Format content according to platform-specific conventions.
    
    Args:
        content: Raw content text
        platform: Target platform
        
    Returns:
        Platform-optimized content
    """
    if platform == "LinkedIn":
        # LinkedIn prefers professional formatting with line breaks
        return content.replace(". ", ".\n\n")
    
    elif platform == "X":
        # Twitter/X prefers concise, single-paragraph format
        return content.replace("\n\n", " ").strip()
    
    elif platform == "Instagram":
        # Instagram allows more creative formatting
        return content
    
    elif platform == "Facebook":
        # Facebook works well with paragraph breaks
        return content
    
    return content


def get_cta_suggestions_for_platform(platform: str, industry: str = "general") -> list:
    """
    Get platform-specific call-to-action suggestions.
    
    Args:
        platform: Target platform
        industry: Business industry for context
        
    Returns:
        List of CTA suggestions optimized for the platform
    """
    cta_suggestions = {
        "Instagram": [
            "¡Desliza para ver más! 👉",
            "¡Guarda este post! 📌",
            "¡Comparte en tus stories! 📱",
            "¡Síguenos para más tips! ✨",
            "¡Comenta tu experiencia! 💬"
        ],
        "LinkedIn": [
            "¿Qué opinas? Comparte tu experiencia en los comentarios.",
            "¡Conecta conmigo para más insights profesionales!",
            "¿Te ha resultado útil? ¡Dale like y comparte!",
            "¡Sígueme para más contenido profesional!",
            "¿Quieres saber más? ¡Envíame un mensaje!"
        ],
        "Facebook": [
            "¡Comparte con tus amigos! 👥",
            "¿Qué piensas? ¡Déjanos tu comentario! 💭",
            "¡Dale like si te gustó! 👍",
            "¡Únete a nuestra comunidad! 🤝",
            "¡Etiqueta a alguien que necesite ver esto! 🏷️"
        ],
        "X": [
            "¡Retweet si estás de acuerdo! 🔄",
            "¿Tu experiencia? ¡Cuéntanos! 💬",
            "¡Sígueme para más tips! ✨",
            "¡Hilo completo en comentarios! 🧵",
            "¿Qué opinas? ¡Responde! 💭"
        ]
    }
    
    return cta_suggestions.get(platform, cta_suggestions["Instagram"])

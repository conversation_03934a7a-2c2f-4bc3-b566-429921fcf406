#!/usr/bin/env python3
"""
Test script para generar POSTS COMPLETOS (imagen + texto) con Creative Genius.
"""

import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.post_generation_service import PostGenerationService


async def test_complete_posts_generation():
    """Test que genera POSTS COMPLETOS con Creative Genius."""
    print("🚀 Probando Creative Genius - POSTS COMPLETOS (Imagen + Texto)...")
    
    # Initialize Post Generation Service
    post_service = PostGenerationService()
    
    # Test cases para diferentes productos
    test_cases = [
        {
            "brand_info": {
                "businessName": "Wouf Suplementos",
                "industry": "suplementos para mascotas",
                "target_audience": "dueños de perros",
                "brandColor": "#3018ef",
                "voice": "amigable y experto"
            },
            "design_config": {
                "template": "Educativo",
                "platform": "Instagram",
                "userTopic": "suplementos para perros que mejoran la salud"
            },
            "expected_keywords": ["perro", "suplemento", "salud", "wouf", "mascota", "energía"]
        },
        {
            "brand_info": {
                "businessName": "FitMax Gym",
                "industry": "fitness",
                "target_audience": "personas que quieren ponerse en forma",
                "brandColor": "#dd3a5a",
                "voice": "motivacional y directo"
            },
            "design_config": {
                "template": "Promocional",
                "platform": "Instagram",
                "userTopic": "transformación corporal en el gym"
            },
            "expected_keywords": ["gym", "fitness", "entrenamiento", "peso", "músculo", "fitmax"]
        }
    ]
    
    print("\n🎯 Generando POSTS COMPLETOS...")
    
    for i, test_case in enumerate(test_cases):
        brand_name = test_case["brand_info"]["businessName"]
        template = test_case["design_config"]["template"]
        
        print(f"\n🏢 Test {i+1}: {brand_name} - {template}")
        print(f"   📝 Topic: {test_case['design_config']['userTopic']}")
        
        try:
            # Generar posts completos
            response = await post_service.generate_posts_batch(
                brand_info=test_case["brand_info"],
                design_config=test_case["design_config"],
                generation_config={"postCount": 2}  # Solo 2 para test rápido
            )
            
            if response.success:
                print(f"   ✅ Generación EXITOSA: {response.total_generated} posts")
                
                for j, post in enumerate(response.posts):
                    print(f"\n   🎨 POST {j+1}:")
                    
                    # Evaluar IMAGEN
                    if post.image_url:
                        print(f"      🖼️ IMAGEN: ✅ Generada - {post.image_url[:50]}...")
                        
                        # Verificar metadatos de imagen
                        if hasattr(post, 'metadata'):
                            hook = post.metadata.get('visual_hook', 'N/A')
                            viral_score = post.metadata.get('viral_score', 'N/A')
                            creative_concept = post.metadata.get('creative_concept', 'N/A')
                            
                            print(f"         Hook en imagen: '{hook}'")
                            print(f"         Concepto creativo: {creative_concept[:60]}...")
                            print(f"         Viral score: {viral_score}")
                    else:
                        print(f"      🖼️ IMAGEN: ❌ NO generada")
                    
                    # Evaluar TEXTO
                    text = post.text.lower()
                    expected_keywords = test_case["expected_keywords"]
                    
                    text_relevant = any(keyword in text for keyword in expected_keywords)
                    
                    print(f"      📝 TEXTO: {post.text[:100]}...")
                    
                    if text_relevant:
                        print(f"      ✅ TEXTO RELEVANTE: Menciona el producto")
                        matching_keywords = [k for k in expected_keywords if k in text]
                        print(f"         Keywords encontradas: {matching_keywords}")
                    else:
                        print(f"      ❌ TEXTO IRRELEVANTE: No menciona el producto")
                        print(f"         Esperaba: {expected_keywords}")
                    
                    # Evaluar POST COMPLETO
                    has_image = post.image_url is not None
                    has_relevant_text = text_relevant
                    
                    if has_image and has_relevant_text:
                        print(f"      🎯 POST COMPLETO: ✅ CHINGÓN (imagen + texto relevante)")
                    elif has_image:
                        print(f"      🎯 POST COMPLETO: ⚠️ REGULAR (imagen OK, texto irrelevante)")
                    elif has_relevant_text:
                        print(f"      🎯 POST COMPLETO: ⚠️ REGULAR (texto OK, sin imagen)")
                    else:
                        print(f"      🎯 POST COMPLETO: ❌ MALO (sin imagen, texto irrelevante)")
                        
            else:
                print(f"   ❌ Error en generación: {response.error}")
                
        except Exception as e:
            print(f"   ❌ Excepción: {e}")
    
    print("\n🎯 Test específico: Wouf Suplementos - 3 posts educativos")
    
    brand_info = {
        "businessName": "Wouf Suplementos",
        "industry": "suplementos para mascotas",
        "target_audience": "dueños de perros",
        "brandColor": "#3018ef"
    }
    
    design_config = {
        "template": "Educativo",
        "platform": "Instagram",
        "userTopic": "mi marca es de suplementos para perro, se llama wouf y vendo muchos suplementos"
    }
    
    try:
        response = await post_service.generate_posts_batch(
            brand_info=brand_info,
            design_config=design_config,
            generation_config={"postCount": 3}
        )
        
        if response.success:
            print(f"\n✅ Generados {response.total_generated} posts para Wouf")
            
            posts_with_images = 0
            posts_with_relevant_text = 0
            complete_posts = 0
            
            for i, post in enumerate(response.posts):
                print(f"\n🎨 POST WOUF {i+1}:")
                print(f"   📝 Texto: {post.text}")
                print(f"   🖼️ Imagen: {'✅ SÍ' if post.image_url else '❌ NO'}")
                
                if post.image_url:
                    posts_with_images += 1
                    print(f"   🔗 URL: {post.image_url}")
                
                # Verificar relevancia del texto
                text_lower = post.text.lower()
                wouf_keywords = ["wouf", "perro", "suplemento", "mascota", "salud", "energía"]
                relevant = any(keyword in text_lower for keyword in wouf_keywords)
                
                if relevant:
                    posts_with_relevant_text += 1
                    print(f"   ✅ Texto relevante a Wouf")
                else:
                    print(f"   ❌ Texto NO relevante a Wouf")
                
                if post.image_url and relevant:
                    complete_posts += 1
                    print(f"   🎯 POST COMPLETO: ✅ CHINGÓN")
                else:
                    print(f"   🎯 POST COMPLETO: ❌ INCOMPLETO")
            
            print(f"\n📊 RESUMEN WOUF:")
            print(f"   🖼️ Posts con imagen: {posts_with_images}/3")
            print(f"   📝 Posts con texto relevante: {posts_with_relevant_text}/3")
            print(f"   🎯 Posts completos chingones: {complete_posts}/3")
            
            if complete_posts >= 2:
                print(f"   ✅ EXCELENTE: Creative Genius genera posts chingones")
            elif complete_posts >= 1:
                print(f"   ⚠️ REGULAR: Algunos posts están bien")
            else:
                print(f"   ❌ MALO: Ningún post está completo")
                
        else:
            print(f"❌ Error: {response.error}")
            
    except Exception as e:
        print(f"❌ Excepción: {e}")
    
    print("\n🚀 CONCLUSIÓN:")
    print("✅ Este test evalúa POSTS COMPLETOS (imagen + texto)")
    print("✅ Verifica que las imágenes se generen con Ideogram")
    print("✅ Verifica que el texto sea relevante al producto")
    print("✅ Evalúa el resultado final que ve el usuario")


if __name__ == "__main__":
    asyncio.run(test_complete_posts_generation())

#!/usr/bin/env python3
"""
Test script para verificar que el Creative Genius genera contenido SOBRE EL PRODUCTO.
"""

import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.creative_genius_service import CreativeGeniusService


async def test_product_focused_content():
    """Test que el Creative Genius genera contenido SOBRE EL PRODUCTO."""
    print("🎯 Probando Creative Genius - CONTENIDO SOBRE EL PRODUCTO...")
    
    # Initialize Creative Genius
    creative_genius = CreativeGeniusService()
    
    # Test cases específicos por producto
    test_cases = [
        {
            "businessName": "Wouf Suplementos",
            "industry": "suplementos para mascotas",
            "target_audience": "dueños de perros",
            "content_type": "educational",
            "expected_keywords": ["perro", "suplemento", "salud", "wouf", "mascota", "energía"]
        },
        {
            "businessName": "FitMax Gym", 
            "industry": "fitness",
            "target_audience": "personas que quieren ponerse en forma",
            "content_type": "promotional",
            "expected_keywords": ["gym", "fitness", "entrenamiento", "peso", "músculo", "fitmax"]
        },
        {
            "businessName": "TechStart Solutions",
            "industry": "tecnología",
            "target_audience": "emprendedores",
            "content_type": "inspirational",
            "expected_keywords": ["negocio", "empresa", "ventas", "crecimiento", "techstart", "solución"]
        }
    ]
    
    print("\n🔍 Verificando que genere contenido SOBRE EL PRODUCTO...")
    
    for i, context in enumerate(test_cases):
        print(f"\n🎯 Test {i+1}: {context['businessName']} - {context['content_type']}")
        
        try:
            breakthrough = await creative_genius.create_breakthrough_content(
                user_context=context,
                content_type=context['content_type']
            )
            
            hook = breakthrough.hook.lower()
            visual_concept = breakthrough.visual_concept.lower()
            content_angle = breakthrough.content_angle.lower()
            
            # Verificar que contenga palabras relacionadas al producto
            expected_keywords = context['expected_keywords']
            business_name = context['businessName'].lower()
            
            hook_relevant = any(keyword in hook for keyword in expected_keywords) or business_name.split()[0] in hook
            visual_relevant = any(keyword in visual_concept for keyword in expected_keywords)
            
            print(f"   🎯 Hook: '{breakthrough.hook}'")
            print(f"   🎨 Visual: {breakthrough.visual_concept[:100]}...")
            print(f"   🚀 Ángulo: {breakthrough.content_angle[:80]}...")
            print(f"   📊 Viral score: {breakthrough.viral_score}/10")
            
            # Validaciones de relevancia
            if hook_relevant:
                print(f"   ✅ HOOK RELEVANTE: Menciona el producto/industria")
            else:
                print(f"   ❌ HOOK IRRELEVANTE: No menciona el producto")
                print(f"      Esperaba: {expected_keywords}")
                print(f"      Obtuvo: {hook}")
            
            if visual_relevant:
                print(f"   ✅ VISUAL RELEVANTE: Relacionado al producto")
            else:
                print(f"   ❌ VISUAL IRRELEVANTE: No relacionado al producto")
            
            # Verificar que NO sea filosofía abstracta
            abstract_words = ["existencial", "universo", "camino", "futuro", "vida alternativa", "cápsula futurista"]
            is_abstract = any(word in hook or word in visual_concept for word in abstract_words)
            
            if is_abstract:
                print(f"   ❌ PROBLEMA: Contiene filosofía abstracta")
            else:
                print(f"   ✅ CORRECTO: No es filosofía abstracta")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n📊 Test de consistencia: Generando 3 conceptos para Wouf Suplementos...")
    
    context = {
        "businessName": "Wouf Suplementos",
        "industry": "suplementos para mascotas", 
        "target_audience": "dueños de perros"
    }
    
    relevant_concepts = 0
    abstract_concepts = 0
    
    for i in range(3):
        try:
            breakthrough = await creative_genius.create_breakthrough_content(
                user_context=context,
                content_type="educational"
            )
            
            hook = breakthrough.hook.lower()
            visual_concept = breakthrough.visual_concept.lower()
            
            # Verificar relevancia
            relevant_keywords = ["perro", "suplemento", "salud", "wouf", "mascota", "energía", "veterinario"]
            is_relevant = any(keyword in hook or keyword in visual_concept for keyword in relevant_keywords)
            
            # Verificar si es abstracto
            abstract_keywords = ["cápsula futurista", "universo", "camino", "vida alternativa", "existencial"]
            is_abstract = any(keyword in hook or keyword in visual_concept for keyword in abstract_keywords)
            
            if is_relevant:
                relevant_concepts += 1
                print(f"   ✅ Concepto {i+1}: RELEVANTE - {breakthrough.hook}")
            else:
                print(f"   ❌ Concepto {i+1}: IRRELEVANTE - {breakthrough.hook}")
            
            if is_abstract:
                abstract_concepts += 1
                print(f"      ⚠️ Contiene elementos abstractos")
                
        except Exception as e:
            print(f"   Error en concepto {i+1}: {e}")
    
    print(f"\n📊 RESULTADOS DE CONSISTENCIA:")
    print(f"   🎯 Conceptos RELEVANTES al producto: {relevant_concepts}/3")
    print(f"   🌙 Conceptos ABSTRACTOS: {abstract_concepts}/3")
    
    if relevant_concepts >= 2:
        print(f"   ✅ BUENO: Mayoría de conceptos son relevantes")
    else:
        print(f"   ❌ PROBLEMA: Muchos conceptos irrelevantes")
    
    if abstract_concepts <= 1:
        print(f"   ✅ BUENO: Pocos conceptos abstractos")
    else:
        print(f"   ⚠️ ADVERTENCIA: Demasiados conceptos abstractos")
    
    print("\n🎯 EJEMPLOS DE LO QUE DEBERÍA GENERAR:")
    print("\n✅ CORRECTO - Wouf Suplementos:")
    print("   Hook: 'Mi perro cambió completamente con Wouf'")
    print("   Visual: 'Split screen: perro cansado vs energético después de suplementos'")
    print("   Ángulo: 'Beneficios reales de los suplementos Wouf'")
    
    print("\n✅ CORRECTO - FitMax Gym:")
    print("   Hook: 'Perdí 15 kilos en FitMax'")
    print("   Visual: 'Antes/después: transformación corporal en el gym'")
    print("   Ángulo: 'Resultados reales de entrenar en FitMax'")
    
    print("\n❌ INCORRECTO - Filosofía abstracta:")
    print("   Hook: '¿Y si hubieras elegido otro camino?'")
    print("   Visual: 'Cápsula futurista en el espacio'")
    print("   Ángulo: 'Simulación de vida alternativa'")
    
    print("\n🚀 CONCLUSIÓN:")
    if relevant_concepts >= 2 and abstract_concepts <= 1:
        print("✅ Creative Genius ARREGLADO - Genera contenido sobre el producto")
    else:
        print("⚠️ Creative Genius necesita más ajustes para enfocarse en el producto")


if __name__ == "__main__":
    asyncio.run(test_product_focused_content())

#!/usr/bin/env python3
"""
Test script para verificar las mejoras de Emma en anuncios.
"""

import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.ad_creator_agent_service import AdCreatorAgentService


async def test_emma_improvements():
    """Test the improved Emma ad creator agent."""
    print("🧪 Probando las mejoras de Emma para anuncios...")
    
    # Initialize the service
    emma = AdCreatorAgentService()
    
    # Test 1: Basic chat with context awareness
    print("\n📝 Test 1: Chat básico con contexto")
    context = {
        "platform": "facebook",
        "currentPrompt": "Vendo zapatos deportivos de alta calidad",
        "headline": "",
        "punchline": "",
        "cta": "",
        "sessionId": "test_session_1"
    }
    
    response = await emma.chat("Hola Emma, ayúdame a crear un anuncio", context)
    print(f"Emma: {response['response']}")
    print(f"Sugerencias: {response['suggestions']}")
    
    # Test 2: Generate specific headline
    print("\n🎯 Test 2: Generar headline específico")
    context["task"] = "generate_headline"
    response = await emma.chat("Genera un headline atractivo", context)
    print(f"Emma: {response['response']}")
    print(f"Campo sugerido: {response.get('field_suggestions', {})}")
    
    # Test 3: Generate headline using specific method
    print("\n⚡ Test 3: Generar headline con método específico")
    headline = await emma.generate_specific_field("headline", {
        "platform": "facebook",
        "productDescription": "Zapatos deportivos de alta calidad para corredores profesionales"
    })
    print(f"Headline generado: '{headline}' (longitud: {len(headline)} caracteres)")
    
    # Test 4: Generate punchline
    print("\n💫 Test 4: Generar punchline")
    punchline = await emma.generate_specific_field("punchline", {
        "platform": "facebook",
        "productDescription": "Zapatos deportivos de alta calidad para corredores profesionales"
    })
    print(f"Punchline generado: '{punchline}' (longitud: {len(punchline)} caracteres)")
    
    # Test 5: Generate CTA
    print("\n🎯 Test 5: Generar CTA")
    cta = await emma.generate_specific_field("cta", {
        "platform": "facebook",
        "productDescription": "Zapatos deportivos de alta calidad para corredores profesionales"
    })
    print(f"CTA generado: '{cta}' (longitud: {len(cta)} caracteres)")
    
    # Test 6: Context awareness with filled fields
    print("\n🧠 Test 6: Conciencia de contexto con campos llenos")
    context_filled = {
        "platform": "facebook",
        "currentPrompt": "Zapatos deportivos de alta calidad",
        "headline": headline,
        "punchline": punchline,
        "cta": cta,
        "sessionId": "test_session_2"
    }
    
    response = await emma.chat("¿Qué más puedo mejorar?", context_filled)
    print(f"Emma: {response['response']}")
    print(f"Sugerencias contextuales: {response['suggestions']}")
    
    # Test 7: Memory test
    print("\n🧠 Test 7: Prueba de memoria")
    response1 = await emma.chat("Quiero crear un anuncio para Instagram", {
        "platform": "instagram",
        "sessionId": "memory_test"
    })
    print(f"Emma (1): {response1['response']}")
    
    response2 = await emma.chat("¿Recuerdas para qué plataforma era?", {
        "platform": "instagram",
        "sessionId": "memory_test"
    })
    print(f"Emma (2): {response2['response']}")
    
    print("\n✅ Pruebas completadas!")


if __name__ == "__main__":
    asyncio.run(test_emma_improvements())

#!/usr/bin/env python3
"""
Test script específico para probar Emma con suplementos.
"""

import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.ad_creator_agent_service import AdCreatorAgentService


async def test_emma_supplements():
    """Test Emma specifically with supplements."""
    print("🧪 Probando Emma mejorada para SUPLEMENTOS...")
    
    # Initialize the service
    emma = AdCreatorAgentService()
    
    # Test 1: Chat con marca de suplementos
    print("\n💊 Test 1: Chat con marca de suplementos")
    context = {
        "platform": "facebook",
        "currentPrompt": "Vendo proteína whey premium para atletas y personas que van al gym",
        "headline": "",
        "punchline": "",
        "cta": "",
        "sessionId": "supplements_test_1"
    }
    
    response = await emma.chat("Ayúdame a crear un anuncio", context)
    print(f"Emma: {response['response']}")
    print(f"Sugerencias: {response['suggestions']}")
    
    # Test 2: Generar headline específico para suplementos
    print("\n🎯 Test 2: Generar headline para proteína")
    headline = await emma.generate_specific_field("headline", {
        "platform": "facebook",
        "productDescription": "Proteína whey premium 100% pura, ideal para ganar masa muscular y recuperación post-workout"
    })
    print(f"Headline: '{headline}' (longitud: {len(headline)} caracteres)")
    
    # Test 3: Generar punchline para suplementos
    print("\n💫 Test 3: Generar punchline para pre-workout")
    punchline = await emma.generate_specific_field("punchline", {
        "platform": "instagram",
        "productDescription": "Pre-workout con cafeína y creatina para máxima energía en el gym"
    })
    print(f"Punchline: '{punchline}' (longitud: {len(punchline)} caracteres)")
    
    # Test 4: Generar CTA para suplementos
    print("\n🎯 Test 4: Generar CTA para quemador de grasa")
    cta = await emma.generate_specific_field("cta", {
        "platform": "facebook",
        "productDescription": "Quemador de grasa natural para definición muscular"
    })
    print(f"CTA: '{cta}' (longitud: {len(cta)} caracteres)")
    
    # Test 5: Chat específico pidiendo headline
    print("\n🗣️ Test 5: Pedir headline específico")
    response = await emma.chat("Genera un headline para mi proteína", {
        "platform": "instagram",
        "currentPrompt": "Proteína whey isolate sabor chocolate",
        "sessionId": "supplements_test_2"
    })
    print(f"Emma: {response['response']}")
    print(f"Campo sugerido: {response.get('field_suggestions', {})}")
    
    # Test 6: Chat pidiendo ayuda general
    print("\n🆘 Test 6: Pedir ayuda general")
    response = await emma.chat("Ayuda, genera todo lo que falta", {
        "platform": "facebook",
        "currentPrompt": "Creatina monohidrato para fuerza y potencia",
        "headline": "",
        "punchline": "",
        "cta": "",
        "sessionId": "supplements_test_3"
    })
    print(f"Emma: {response['response']}")
    print(f"Sugerencias: {response['suggestions']}")
    
    print("\n✅ Pruebas de suplementos completadas!")


if __name__ == "__main__":
    asyncio.run(test_emma_supplements())

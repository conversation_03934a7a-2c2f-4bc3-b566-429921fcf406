#!/usr/bin/env python3
"""
Test script for the complete image generation + download flow
"""

import requests
import json

def test_full_flow():
    """Test image generation followed by download"""
    
    print("🧪 Testing Complete Image Generation + Download Flow")
    print("=" * 60)
    
    # Step 1: Generate a new image
    print("Step 1: Generating new image...")
    
    generate_url = "http://localhost:8000/api/image-generator/generate"
    data = {
        "prompt": "a simple red circle on white background",
        "resolution": "1024x1024",
        "rendering_speed": "TURBO",  # Use fastest for testing
        "magic_prompt": "OFF",  # Disable for faster generation
        "num_images": "1",
        "style_type": "GENERAL"
    }
    
    try:
        response = requests.post(generate_url, data=data, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success") and result.get("image_url"):
                image_url = result["image_url"]
                print(f"✅ Image generated successfully!")
                print(f"Image URL: {image_url}")
                
                # Step 2: Test download
                print("\nStep 2: Testing download...")
                download_url = f"http://localhost:8000/api/image-generator/download-image?url={requests.utils.quote(image_url)}"
                
                download_response = requests.get(download_url, timeout=30)
                
                if download_response.status_code == 200:
                    print("✅ Download successful!")
                    print(f"Content-Type: {download_response.headers.get('content-type')}")
                    print(f"Content-Length: {download_response.headers.get('content-length')}")
                    print(f"Content-Disposition: {download_response.headers.get('content-disposition')}")
                    
                    # Save test file
                    with open("test_full_flow.png", "wb") as f:
                        f.write(download_response.content)
                    print("📁 Test file saved as test_full_flow.png")
                    
                    print("\n🎉 Complete flow test PASSED!")
                    return True
                else:
                    print(f"❌ Download failed: {download_response.status_code}")
                    print(f"Response: {download_response.text}")
                    return False
            else:
                print(f"❌ Image generation failed: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ Generation request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

if __name__ == "__main__":
    test_full_flow()

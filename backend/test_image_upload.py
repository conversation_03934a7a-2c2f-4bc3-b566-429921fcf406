#!/usr/bin/env python3
"""
Test script to verify image hosting services are working.
"""

import asyncio
import sys
import os
from PIL import Image
import io

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from app.api.endpoints.luma_labs import upload_image_to_temp_host

async def test_image_upload():
    """Test the image upload functionality."""
    print("🧪 Testing image upload services...")
    
    try:
        # Create a simple test image
        print("📷 Creating test image...")
        image = Image.new('RGB', (400, 400), color='red')
        
        # Convert to bytes
        buffer = io.BytesIO()
        image.save(buffer, format='JPEG', quality=90)
        image_bytes = buffer.getvalue()
        
        print(f"📁 Test image size: {len(image_bytes)} bytes")
        
        # Test upload
        print("🚀 Attempting upload...")
        url = await upload_image_to_temp_host(image_bytes)
        
        print(f"✅ Upload successful!")
        print(f"🌐 URL: {url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Upload failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_image_upload())
    if success:
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print("\n💥 Tests failed!")
        sys.exit(1)

#!/usr/bin/env python3
"""
Test script to verify Supabase connection and database operations
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.supabase import supabase_service

async def test_supabase_connection():
    """Test basic Supabase connection and operations"""
    print("🔍 Testing Supabase connection...")
    
    try:
        # Test 1: Check if we can connect to Supabase
        print("✅ Supabase client initialized successfully")
        
        # Test 2: Try to fetch existing analyses (should work even if empty)
        print("\n📊 Testing database query...")
        analyses = await supabase_service.get_user_analyses("test-user-id", limit=5)
        print(f"✅ Successfully queried database. Found {len(analyses)} analyses for test user.")
        
        # Test 3: Test saving a mock analysis
        print("\n💾 Testing analysis save...")
        mock_analysis_data = {
            "original_filename": "test-design.jpg",
            "file_size": 1024,
            "file_type": "image/jpeg",
            "score": 85,
            "complexity": {
                "color": 7,
                "layout": 8,
                "typography": 9,
                "hierarchy": 8,
                "composition": 7,
                "contrast": 9
            },
            "areas": [
                {
                    "name": "Jerarquía Visual",
                    "score": 8,
                    "description": "Buen uso de jerarquía visual",
                    "recommendations": ["Mantener consistencia"]
                }
            ],
            "recommendations": ["Excelente trabajo en general"],
            "analysis_summary": "Análisis de prueba",
            "gemini_analysis": "Análisis detallado de Gemini",
            "agent_message": "¡Buen trabajo!",
            "visuai_insights": {"test": "data"},
            "analysis_duration_ms": 2500
        }
        
        saved_analysis = await supabase_service.save_design_analysis(
            user_id="test-user-id",
            analysis_data=mock_analysis_data
        )
        
        if saved_analysis:
            analysis_id = saved_analysis["id"]
            print(f"✅ Successfully saved test analysis with ID: {analysis_id}")
            
            # Test 4: Test retrieving the saved analysis
            print("\n🔍 Testing analysis retrieval...")
            retrieved_analysis = await supabase_service.get_analysis_by_id(
                analysis_id=analysis_id,
                user_id="test-user-id"
            )
            
            if retrieved_analysis:
                print(f"✅ Successfully retrieved analysis: {retrieved_analysis['original_filename']}")
                
                # Test 5: Test updating favorite status
                print("\n⭐ Testing favorite toggle...")
                success = await supabase_service.update_analysis_favorite(
                    analysis_id=analysis_id,
                    user_id="test-user-id",
                    is_favorite=True
                )
                
                if success:
                    print("✅ Successfully updated favorite status")
                else:
                    print("❌ Failed to update favorite status")
                
                # Test 6: Test deletion (cleanup)
                print("\n🗑️ Testing analysis deletion (cleanup)...")
                delete_success = await supabase_service.delete_analysis(
                    analysis_id=analysis_id,
                    user_id="test-user-id"
                )
                
                if delete_success:
                    print("✅ Successfully deleted test analysis")
                else:
                    print("❌ Failed to delete test analysis")
                    
            else:
                print("❌ Failed to retrieve saved analysis")
        else:
            print("❌ Failed to save test analysis")
            
        print("\n🎉 Supabase integration test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error during Supabase test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Supabase Integration Test for Emma Studio")
    print("=" * 50)
    
    # Run the async test
    success = asyncio.run(test_supabase_connection())
    
    if success:
        print("\n✅ All tests passed! Supabase integration is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the configuration.")
        sys.exit(1)

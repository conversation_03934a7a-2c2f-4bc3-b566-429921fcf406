"use client"
import { motion } from "framer-motion";
import { useLocation } from "wouter";
import { <PERSON><PERSON><PERSON>, TrendingUp } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import EmmaProfile from "@/assets/emma-profile.png";

export function EmmaSuggestions() {
  const [, navigate] = useLocation();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 2.0 }}
    >
      <Card className="border-white/20 bg-white/90 backdrop-blur-md shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden">
        <CardContent className="p-8 relative">
          {/* Gradient background */}
          <div className="absolute top-0 left-0 right-0 h-24 bg-gradient-to-r from-[#3018ef]/10 via-[#8b5cf6]/10 to-[#dd3a5a]/10 opacity-50" />

          <div className="flex items-start gap-6 relative z-10">
            <motion.div
              whileHover={{ scale: 1.05, rotate: 5 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              <Avatar className="h-16 w-16 border-2 border-white/30 shadow-xl backdrop-blur-md">
                <AvatarImage src={EmmaProfile} alt="Emma AI" />
                <AvatarFallback className="bg-gradient-to-br from-[#3018ef]/20 to-[#dd3a5a]/20 backdrop-blur-md text-gray-700 text-xl font-bold">
                  E
                </AvatarFallback>
              </Avatar>
            </motion.div>
            <div className="flex-1">
              <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-3 flex items-center">
                <Sparkles className="w-6 h-6 text-[#3018ef] mr-2" />
                Sugerencias Inteligentes de Emma
              </h3>
              <div className="space-y-4">
                <motion.div
                  className="bg-white/80 backdrop-blur-md rounded-xl p-6 border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300"
                  whileHover={{ scale: 1.02, y: -2 }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                >
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                      <TrendingUp className="h-5 w-5 text-white" />
                    </div>
                    <span className="font-bold text-gray-900">Optimización Sugerida</span>
                  </div>
                  <p className="text-gray-700 mb-4 leading-relaxed">
                    Basándome en tus proyectos recientes, te recomiendo crear un buyer persona
                    más detallado para mejorar la segmentación de tus campañas.
                  </p>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      onClick={() => navigate("/dashboard/herramientas/buyer-persona-generator")}
                      size="sm"
                      className="bg-gradient-to-r from-[#3018ef] to-[#4f46e5] hover:from-[#1e1b8b] hover:to-[#3730a3] text-white font-semibold px-4 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      Crear Buyer Persona
                    </Button>
                  </motion.div>
                </motion.div>

                <motion.div
                  className="bg-white/80 backdrop-blur-md rounded-xl p-6 border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300"
                  whileHover={{ scale: 1.02, y: -2 }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                >
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                      <Sparkles className="h-5 w-5 text-white" />
                    </div>
                    <span className="font-bold text-gray-900">Tendencia Detectada</span>
                  </div>
                  <p className="text-gray-700 mb-4 leading-relaxed">
                    He notado un aumento del 40% en engagement con contenido visual.
                    ¿Quieres que genere más imágenes para tus próximas campañas?
                  </p>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      onClick={() => navigate("/image-generator")}
                      size="sm"
                      variant="outline"
                      className="border-2 border-[#dd3a5a]/30 text-[#dd3a5a] hover:bg-[#dd3a5a]/5 font-semibold px-4 py-2 rounded-lg backdrop-blur-md transition-all duration-300"
                    >
                      Generar Imágenes
                    </Button>
                  </motion.div>
                </motion.div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

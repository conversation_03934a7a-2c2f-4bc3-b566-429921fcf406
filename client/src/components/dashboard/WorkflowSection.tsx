"use client"
import { motion } from "framer-motion";
import { useLocation } from "wouter";
import { ChevronRight } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

// Emma brand colors for consistent styling
const EMMA_COLORS = {
  primary: "#3018ef",
  primaryLight: "#6366f1",
  secondary: "#dd3a5a",
  secondaryLight: "#f472b6",
  purple: "#8b5cf6",
  purpleLight: "#a78bfa",
  green: "#10b981",
  greenLight: "#34d399",
  orange: "#f59e0b",
  orangeLight: "#fbbf24",
  cyan: "#06b6d4",
  cyanLight: "#22d3ee",
};

function getEmmaColor(colorType: string, intensity: "base" | "light" | "dark" = "base"): string {
  const colorMap: Record<string, Record<string, string>> = {
    primary: {
      base: EMMA_COLORS.primary,
      light: EMMA_COLORS.primaryLight,
      dark: "#1e1b8b",
    },
    secondary: {
      base: EMMA_COLORS.secondary,
      light: EMMA_COLORS.secondaryLight,
      dark: "#be185d",
    },
    purple: {
      base: EMMA_COLORS.purple,
      light: EMMA_COLORS.purpleLight,
      dark: "#7c3aed",
    },
    green: {
      base: EMMA_COLORS.green,
      light: EMMA_COLORS.greenLight,
      dark: "#059669",
    },
    orange: {
      base: EMMA_COLORS.orange,
      light: EMMA_COLORS.orangeLight,
      dark: "#d97706",
    },
    cyan: {
      base: EMMA_COLORS.cyan,
      light: EMMA_COLORS.cyanLight,
      dark: "#0891b2",
    },
  };

  return colorMap[colorType]?.[intensity] || EMMA_COLORS.primary;
}

interface WorkflowTool {
  title: string;
  description: string;
  path: string;
  preview: string;
  colorType?: string;
}

interface WorkflowSectionData {
  title: string;
  description: string;
  tools: WorkflowTool[];
}

interface WorkflowSectionProps {
  workflowSections: WorkflowSectionData[];
}

export function WorkflowSection({ workflowSections }: WorkflowSectionProps) {
  const [, navigate] = useLocation();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 1.5 }}
    >
      <div className="mb-8">
        <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
          Flujo de Trabajo Completo
        </h2>
        <p className="text-gray-600 text-lg">Herramientas organizadas por proceso de marketing</p>
      </div>

      <div className="space-y-8">
        {workflowSections.map((section, sectionIndex) => (
          <motion.div
            key={sectionIndex}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.6 + sectionIndex * 0.2 }}
          >
            <Card className="border-white/20 bg-white/90 backdrop-blur-md shadow-xl hover:shadow-2xl transition-all duration-300">
              <CardContent className="p-8">
                <div className="mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2 flex items-center">
                    <span className="mr-3">{section.title}</span>
                  </h3>
                  <p className="text-gray-600 text-base">{section.description}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {section.tools.map((tool, toolIndex) => (
                    <motion.div
                      key={toolIndex}
                      whileHover={{
                        scale: 1.05,
                        y: -8,
                        transition: { type: "spring", stiffness: 400, damping: 25 }
                      }}
                      className="group"
                    >
                      <Card
                        className="cursor-pointer border-2 border-transparent hover:border-white/40 bg-white/95 backdrop-blur-md hover:bg-white hover:shadow-2xl transition-all duration-300 overflow-hidden rounded-2xl"
                        onClick={() => navigate(tool.path)}
                        style={{
                          background: `linear-gradient(135deg, ${getEmmaColor(tool.colorType || "primary", "base")}15, ${getEmmaColor(tool.colorType || "primary", "light")}10, white)`,
                        }}
                      >
                        <CardContent className="p-6 relative">
                          {/* Vibrant gradient header */}
                          <div
                            className="absolute top-0 left-0 right-0 h-20 rounded-t-2xl"
                            style={{
                              background: `linear-gradient(135deg, ${getEmmaColor(tool.colorType || "primary", "base")}, ${getEmmaColor(tool.colorType || "primary", "light")})`,
                            }}
                          >
                            {/* Glassmorphism overlay */}
                            <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-black/10 rounded-t-2xl"></div>

                            {/* Decorative elements */}
                            <div className="absolute right-0 top-0 w-16 h-16 bg-white/20 rounded-bl-2xl"></div>
                            <div className="absolute left-0 bottom-0 w-8 h-8 bg-white/30 rounded-tr-xl"></div>
                          </div>

                          {/* Icon container with vibrant styling */}
                          <motion.div
                            className="relative z-10 w-20 h-20 rounded-2xl flex items-center justify-center mb-6 mt-4 border-2 shadow-xl"
                            style={{
                              background: `linear-gradient(135deg, ${getEmmaColor(tool.colorType || "primary", "base")}, ${getEmmaColor(tool.colorType || "primary", "light")})`,
                              borderColor: `${getEmmaColor(tool.colorType || "primary", "base")}40`,
                            }}
                            whileHover={{ scale: 1.15, rotate: 10 }}
                            transition={{ type: "spring", stiffness: 400, damping: 25 }}
                          >
                            <span className="text-3xl filter drop-shadow-sm">{tool.preview}</span>
                          </motion.div>

                          <h4 className="font-bold text-gray-900 mb-3 text-lg group-hover:text-gray-700 transition-colors relative z-10">
                            {tool.title}
                          </h4>
                          <p className="text-gray-600 text-sm leading-relaxed relative z-10 mb-4">
                            {tool.description}
                          </p>

                          {/* Action indicator */}
                          <div className="flex items-center text-sm font-semibold relative z-10 group-hover:translate-x-2 transition-transform duration-300"
                            style={{ color: getEmmaColor(tool.colorType || "primary", "base") }}
                          >
                            Usar herramienta
                            <ChevronRight className="ml-1 w-4 h-4" />
                          </div>

                          {/* Enhanced hover effect */}
                          <div
                            className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"
                            style={{
                              background: `linear-gradient(135deg, ${getEmmaColor(tool.colorType || "primary", "base")}, ${getEmmaColor(tool.colorType || "primary", "light")})`,
                            }}
                          />
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
}

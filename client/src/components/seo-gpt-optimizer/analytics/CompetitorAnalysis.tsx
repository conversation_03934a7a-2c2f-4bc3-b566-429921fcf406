/**
 * SEO & GPT Optimizer™ - Competitor Analysis Component
 * Analysis comparing content performance against competitors
 */

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  Users, 
  TrendingUp, 
  Target, 
  Search,
  ExternalLink,
  Award,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Eye,
  Clock
} from 'lucide-react';

interface CompetitorData {
  domain: string;
  title: string;
  url: string;
  estimated_score: number;
  position: number;
  content_length: number;
  authority_signals: number;
  semantic_similarity: number;
  strengths: string[];
  weaknesses: string[];
  opportunities: string[];
}

interface CompetitorAnalysisProps {
  topic: string;
  userScore: number;
  competitors: CompetitorData[];
  onAnalyzeCompetitor?: (url: string) => void;
  className?: string;
}

const CompetitorAnalysis: React.FC<CompetitorAnalysisProps> = ({
  topic,
  userScore,
  competitors,
  onAnalyzeCompetitor,
  className = ''
}) => {
  const [selectedCompetitor, setSelectedCompetitor] = useState<CompetitorData | null>(null);
  const [sortBy, setSortBy] = useState<'score' | 'position' | 'length'>('score');

  // Sort competitors
  const sortedCompetitors = useMemo(() => {
    return [...competitors].sort((a, b) => {
      switch (sortBy) {
        case 'score':
          return b.estimated_score - a.estimated_score;
        case 'position':
          return a.position - b.position;
        case 'length':
          return b.content_length - a.content_length;
        default:
          return 0;
      }
    });
  }, [competitors, sortBy]);

  // Calculate user ranking
  const userRanking = useMemo(() => {
    const scoresAbove = competitors.filter(c => c.estimated_score > userScore).length;
    return scoresAbove + 1;
  }, [competitors, userScore]);

  // Calculate insights
  const insights = useMemo(() => {
    if (!competitors.length) return null;

    const avgScore = competitors.reduce((sum, c) => sum + c.estimated_score, 0) / competitors.length;
    const topCompetitor = competitors.reduce((top, current) => 
      current.estimated_score > top.estimated_score ? current : top
    );
    const avgLength = competitors.reduce((sum, c) => sum + c.content_length, 0) / competitors.length;
    
    const strongCompetitors = competitors.filter(c => c.estimated_score > userScore);
    const weakCompetitors = competitors.filter(c => c.estimated_score < userScore);

    return {
      avgScore,
      topCompetitor,
      avgLength,
      strongCompetitors: strongCompetitors.length,
      weakCompetitors: weakCompetitors.length,
      scoreGap: topCompetitor.estimated_score - userScore
    };
  }, [competitors, userScore]);

  const getScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 70) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score: number): string => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 70) return 'bg-blue-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  if (!competitors.length) {
    return (
      <div className={`bg-white rounded-2xl shadow-sm border border-gray-100 p-8 text-center ${className}`}>
        <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Sin análisis competitivo</h3>
        <p className="text-gray-600">
          Realiza una investigación para obtener datos de competidores y análisis comparativo.
        </p>
      </div>
    );
  }

  return (
    <motion.div
      className={`bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
              <Users className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Análisis Competitivo</h2>
              <p className="text-gray-600 text-sm">Comparación con competidores para "{topic}"</p>
            </div>
          </div>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="score">Puntuación</option>
            <option value="position">Posición</option>
            <option value="length">Longitud</option>
          </select>
        </div>

        {/* User Position */}
        <div className="flex items-center gap-4 p-4 bg-blue-50 rounded-xl">
          <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
            <Target className="w-6 h-6 text-white" />
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold text-blue-900">Tu Contenido</h3>
              <span className="px-2 py-1 bg-blue-200 text-blue-800 rounded-full text-xs font-medium">
                Posición #{userRanking}
              </span>
            </div>
            <div className="flex items-center gap-4 text-sm text-blue-700">
              <span>Puntuación: {userScore.toFixed(1)}</span>
              {insights && (
                <span>
                  {insights.scoreGap > 0 
                    ? `${insights.scoreGap.toFixed(1)} puntos por debajo del líder`
                    : 'Líder en puntuación'
                  }
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Insights Summary */}
      {insights && (
        <div className="p-6 border-b border-gray-100">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-xl">
              <div className="text-2xl font-bold text-green-600">{insights.weakCompetitors}</div>
              <div className="text-sm text-green-600">Competidores por debajo</div>
            </div>

            <div className="text-center p-4 bg-red-50 rounded-xl">
              <div className="text-2xl font-bold text-red-600">{insights.strongCompetitors}</div>
              <div className="text-sm text-red-600">Competidores por encima</div>
            </div>

            <div className="text-center p-4 bg-blue-50 rounded-xl">
              <div className="text-2xl font-bold text-blue-600">{insights.avgScore.toFixed(1)}</div>
              <div className="text-sm text-blue-600">Puntuación promedio</div>
            </div>
          </div>
        </div>
      )}

      {/* Competitors List */}
      <div className="max-h-96 overflow-y-auto">
        <div className="divide-y divide-gray-100">
          {sortedCompetitors.map((competitor, index) => (
            <motion.div
              key={competitor.url}
              className="p-6 hover:bg-gray-50 cursor-pointer transition-colors duration-200"
              onClick={() => setSelectedCompetitor(competitor)}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 flex-1">
                  {/* Position Badge */}
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                    competitor.position <= 3 ? 'bg-yellow-100 text-yellow-700' :
                    competitor.position <= 5 ? 'bg-blue-100 text-blue-700' :
                    'bg-gray-100 text-gray-700'
                  }`}>
                    {competitor.position}
                  </div>

                  {/* Competitor Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold text-gray-900 truncate">{competitor.title}</h3>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(competitor.url, '_blank');
                        }}
                        className="text-gray-400 hover:text-blue-600 transition-colors duration-200"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </button>
                    </div>
                    <p className="text-sm text-gray-600 truncate">{competitor.domain}</p>
                    <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                      <span>{competitor.content_length} palabras</span>
                      <span>Autoridad: {competitor.authority_signals}/100</span>
                      <span>Similitud: {competitor.semantic_similarity}/100</span>
                    </div>
                  </div>
                </div>

                {/* Score */}
                <div className="text-center">
                  <div className={`text-2xl font-bold ${getScoreColor(competitor.estimated_score)}`}>
                    {competitor.estimated_score.toFixed(1)}
                  </div>
                  <div className="text-xs text-gray-500">Puntuación estimada</div>
                </div>
              </div>

              {/* Expanded Details */}
              {selectedCompetitor?.url === competitor.url && (
                <motion.div
                  className="mt-4 pt-4 border-t border-gray-200"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                >
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Strengths */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        Fortalezas
                      </h4>
                      <div className="space-y-1">
                        {competitor.strengths.map((strength, i) => (
                          <div key={i} className="text-sm text-gray-700 flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                            {strength}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Weaknesses */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                        <AlertTriangle className="w-4 h-4 text-red-600" />
                        Debilidades
                      </h4>
                      <div className="space-y-1">
                        {competitor.weaknesses.map((weakness, i) => (
                          <div key={i} className="text-sm text-gray-700 flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                            {weakness}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Opportunities */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-blue-600" />
                        Oportunidades
                      </h4>
                      <div className="space-y-1">
                        {competitor.opportunities.map((opportunity, i) => (
                          <div key={i} className="text-sm text-gray-700 flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                            {opportunity}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 mt-4">
                    {onAnalyzeCompetitor && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onAnalyzeCompetitor(competitor.url);
                        }}
                        className="flex items-center gap-2 bg-gradient-to-r from-orange-600 to-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-orange-700 hover:to-red-700 transition-all duration-200"
                      >
                        <Search className="w-4 h-4" />
                        Analizar en Detalle
                      </button>
                    )}
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        window.open(competitor.url, '_blank');
                      }}
                      className="flex items-center gap-2 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 transition-all duration-200"
                    >
                      <Eye className="w-4 h-4" />
                      Ver Original
                    </button>
                  </div>
                </motion.div>
              )}
            </motion.div>
          ))}
        </div>
      </div>

      {/* Action Recommendations */}
      {insights && (
        <div className="p-6 border-t border-gray-100 bg-gray-50">
          <h3 className="font-semibold text-gray-900 mb-3">Recomendaciones</h3>
          <div className="space-y-2 text-sm text-gray-700">
            {insights.scoreGap > 10 && (
              <div className="flex items-start gap-2">
                <AlertTriangle className="w-4 h-4 text-orange-500 mt-0.5 flex-shrink-0" />
                <span>
                  Hay una brecha significativa de {insights.scoreGap.toFixed(1)} puntos con el líder. 
                  Enfócate en mejorar autoridad y similitud semántica.
                </span>
              </div>
            )}
            
            {userScore < insights.avgScore && (
              <div className="flex items-start gap-2">
                <TrendingUp className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                <span>
                  Tu puntuación está por debajo del promedio ({insights.avgScore.toFixed(1)}). 
                  Revisa las fortalezas de los competidores mejor posicionados.
                </span>
              </div>
            )}
            
            {userScore >= insights.avgScore && (
              <div className="flex items-start gap-2">
                <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span>
                  Tu contenido está por encima del promedio. Mantén la calidad y busca oportunidades 
                  de diferenciación para superar a los líderes.
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default CompetitorAnalysis;

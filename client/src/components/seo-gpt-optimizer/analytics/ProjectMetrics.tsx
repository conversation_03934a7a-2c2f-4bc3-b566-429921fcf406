/**
 * SEO & GPT Optimizer™ - Project Metrics Component
 * Comparative metrics and performance analysis across projects
 */

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Clock,
  FileText,
  Award,
  Filter,
  ArrowUpRight,
  ArrowDownRight,
  Minus
} from 'lucide-react';

import { SEOGPTProject, ProjectStatus, ContentType } from '../../../types/seo-gpt-optimizer';
import GPTRankMeter from '../shared/GPTRankMeter';

interface ProjectMetricsProps {
  projects: SEOGPTProject[];
  timeRange?: '7d' | '30d' | '90d' | 'all';
  onProjectClick?: (projectId: string) => void;
  className?: string;
}

const statusColors = {
  [ProjectStatus.DRAFT]: 'bg-gray-100 text-gray-700',
  [ProjectStatus.RESEARCHING]: 'bg-yellow-100 text-yellow-700',
  [ProjectStatus.WRITING]: 'bg-blue-100 text-blue-700',
  [ProjectStatus.OPTIMIZING]: 'bg-purple-100 text-purple-700',
  [ProjectStatus.COMPLETED]: 'bg-green-100 text-green-700',
  [ProjectStatus.PUBLISHED]: 'bg-emerald-100 text-emerald-700'
};

const contentTypeIcons = {
  [ContentType.ARTICLE]: FileText,
  [ContentType.GUIDE]: Target,
  [ContentType.TUTORIAL]: Clock,
  [ContentType.COMPARISON]: BarChart3,
  [ContentType.LIST]: Award,
  [ContentType.FAQ]: TrendingUp
};

const ProjectMetrics: React.FC<ProjectMetricsProps> = ({
  projects,
  timeRange = '30d',
  onProjectClick,
  className = ''
}) => {
  const [sortBy, setSortBy] = useState<'score' | 'improvement' | 'updated' | 'created'>('score');
  const [filterStatus, setFilterStatus] = useState<ProjectStatus | 'all'>('all');
  const [filterContentType, setFilterContentType] = useState<ContentType | 'all'>('all');

  // Filter and sort projects
  const filteredProjects = useMemo(() => {
    let filtered = projects.filter(project => {
      if (filterStatus !== 'all' && project.status !== filterStatus) return false;
      if (filterContentType !== 'all' && project.content_type !== filterContentType) return false;
      return true;
    });

    // Sort projects
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'score':
          return b.current_gpt_rank_score - a.current_gpt_rank_score;
        case 'improvement':
          const improvementA = a.current_gpt_rank_score - (a.best_gpt_rank_score || 0);
          const improvementB = b.current_gpt_rank_score - (b.best_gpt_rank_score || 0);
          return improvementB - improvementA;
        case 'updated':
          return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
        case 'created':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        default:
          return 0;
      }
    });

    return filtered;
  }, [projects, sortBy, filterStatus, filterContentType]);

  // Calculate overall statistics
  const stats = useMemo(() => {
    if (!filteredProjects.length) return null;

    const scores = filteredProjects.map(p => p.current_gpt_rank_score);
    const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const maxScore = Math.max(...scores);
    const minScore = Math.min(...scores);
    
    const completedProjects = filteredProjects.filter(p => p.status === ProjectStatus.COMPLETED).length;
    const activeProjects = filteredProjects.filter(p => 
      [ProjectStatus.RESEARCHING, ProjectStatus.WRITING, ProjectStatus.OPTIMIZING].includes(p.status)
    ).length;

    const totalWords = filteredProjects.reduce((sum, p) => sum + p.word_count, 0);
    const avgWords = totalWords / filteredProjects.length;

    return {
      totalProjects: filteredProjects.length,
      avgScore,
      maxScore,
      minScore,
      completedProjects,
      activeProjects,
      totalWords,
      avgWords
    };
  }, [filteredProjects]);

  const getScoreChange = (project: SEOGPTProject) => {
    const change = project.current_gpt_rank_score - (project.best_gpt_rank_score || project.current_gpt_rank_score);
    return change;
  };

  const getScoreChangeIcon = (change: number) => {
    if (change > 0) return <ArrowUpRight className="w-4 h-4 text-green-600" />;
    if (change < 0) return <ArrowDownRight className="w-4 h-4 text-red-600" />;
    return <Minus className="w-4 h-4 text-gray-400" />;
  };

  if (!projects.length) {
    return (
      <div className={`bg-white rounded-2xl shadow-sm border border-gray-100 p-8 text-center ${className}`}>
        <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Sin proyectos</h3>
        <p className="text-gray-600">
          Crea tu primer proyecto para ver métricas y análisis comparativos.
        </p>
      </div>
    );
  }

  return (
    <motion.div
      className={`bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Métricas de Proyectos</h2>
              <p className="text-gray-600 text-sm">Análisis comparativo y rendimiento</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Sort Dropdown */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="score">Puntuación</option>
              <option value="improvement">Mejora</option>
              <option value="updated">Actualizado</option>
              <option value="created">Creado</option>
            </select>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-3">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
            >
              <option value="all">Todos los estados</option>
              {Object.values(ProjectStatus).map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
          </div>

          <select
            value={filterContentType}
            onChange={(e) => setFilterContentType(e.target.value as any)}
            className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
          >
            <option value="all">Todos los tipos</option>
            {Object.values(ContentType).map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Statistics Overview */}
      {stats && (
        <div className="p-6 border-b border-gray-100">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-xl">
              <div className="text-2xl font-bold text-blue-600">{stats.avgScore.toFixed(1)}</div>
              <div className="text-sm text-blue-600">Puntuación Promedio</div>
            </div>

            <div className="text-center p-4 bg-green-50 rounded-xl">
              <div className="text-2xl font-bold text-green-600">{stats.completedProjects}</div>
              <div className="text-sm text-green-600">Completados</div>
            </div>

            <div className="text-center p-4 bg-yellow-50 rounded-xl">
              <div className="text-2xl font-bold text-yellow-600">{stats.activeProjects}</div>
              <div className="text-sm text-yellow-600">Activos</div>
            </div>

            <div className="text-center p-4 bg-purple-50 rounded-xl">
              <div className="text-2xl font-bold text-purple-600">{Math.round(stats.avgWords)}</div>
              <div className="text-sm text-purple-600">Palabras Promedio</div>
            </div>
          </div>
        </div>
      )}

      {/* Projects List */}
      <div className="max-h-96 overflow-y-auto">
        {filteredProjects.length === 0 ? (
          <div className="p-6 text-center">
            <p className="text-gray-600">No hay proyectos que coincidan con los filtros seleccionados.</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {filteredProjects.map((project, index) => {
              const ContentTypeIcon = contentTypeIcons[project.content_type];
              const scoreChange = getScoreChange(project);
              
              return (
                <motion.div
                  key={project.project_id}
                  className="p-6 hover:bg-gray-50 cursor-pointer transition-colors duration-200"
                  onClick={() => onProjectClick?.(project.project_id)}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 flex-1">
                      {/* Project Icon */}
                      <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center">
                        <ContentTypeIcon className="w-6 h-6 text-gray-600" />
                      </div>

                      {/* Project Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-semibold text-gray-900 truncate">{project.title}</h3>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColors[project.status]}`}>
                            {project.status}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 truncate">{project.topic}</p>
                        <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                          <span>{project.word_count} palabras</span>
                          <span>{project.content_type}</span>
                          <span>Actualizado {new Date(project.updated_at).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>

                    {/* Metrics */}
                    <div className="flex items-center gap-6">
                      {/* Score Change */}
                      <div className="text-center">
                        <div className="flex items-center gap-1 justify-center mb-1">
                          {getScoreChangeIcon(scoreChange)}
                          <span className={`text-sm font-medium ${
                            scoreChange > 0 ? 'text-green-600' : 
                            scoreChange < 0 ? 'text-red-600' : 'text-gray-500'
                          }`}>
                            {scoreChange !== 0 ? `${scoreChange > 0 ? '+' : ''}${scoreChange.toFixed(1)}` : '—'}
                          </span>
                        </div>
                        <div className="text-xs text-gray-500">Cambio</div>
                      </div>

                      {/* Current Score */}
                      <div className="text-center">
                        <GPTRankMeter
                          score={project.current_gpt_rank_score}
                          grade={project.current_gpt_rank_score >= 90 ? 'A+' : 
                                 project.current_gpt_rank_score >= 80 ? 'A' : 
                                 project.current_gpt_rank_score >= 70 ? 'B' : 'C'}
                          size="sm"
                          showDetails={false}
                          animated={false}
                        />
                      </div>

                      {/* Target Progress */}
                      <div className="text-center">
                        <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden mb-1">
                          <div 
                            className="h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-500"
                            style={{ 
                              width: `${Math.min((project.current_gpt_rank_score / project.target_gpt_rank_score) * 100, 100)}%` 
                            }}
                          />
                        </div>
                        <div className="text-xs text-gray-500">
                          {project.current_gpt_rank_score.toFixed(0)}/{project.target_gpt_rank_score}
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        )}
      </div>

      {/* Performance Summary */}
      {filteredProjects.length > 0 && (
        <div className="p-6 border-t border-gray-100 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-green-600" />
              <span className="text-gray-700">
                <strong>{filteredProjects.filter(p => getScoreChange(p) > 0).length}</strong> proyectos mejorando
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-blue-600" />
              <span className="text-gray-700">
                <strong>{filteredProjects.filter(p => p.current_gpt_rank_score >= p.target_gpt_rank_score).length}</strong> objetivos alcanzados
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <Award className="w-4 h-4 text-purple-600" />
              <span className="text-gray-700">
                <strong>{filteredProjects.filter(p => p.current_gpt_rank_score >= 80).length}</strong> con puntuación alta
              </span>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default ProjectMetrics;

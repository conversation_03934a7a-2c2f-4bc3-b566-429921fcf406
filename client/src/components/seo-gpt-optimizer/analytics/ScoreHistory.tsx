/**
 * SEO & GPT Optimizer™ - Score History Component
 * Charts showing GPT Rank Score evolution over time
 */

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  BarChart3, 
  LineChart,
  Filter,
  Download
} from 'lucide-react';

interface ScoreDataPoint {
  score: number;
  timestamp: string;
  version: number;
  trigger_event?: string;
}

interface ScoreHistoryProps {
  projectId?: string;
  data: ScoreDataPoint[];
  timeRange?: '7d' | '30d' | '90d' | 'all';
  onTimeRangeChange?: (range: '7d' | '30d' | '90d' | 'all') => void;
  className?: string;
}

const ScoreHistory: React.FC<ScoreHistoryProps> = ({
  projectId,
  data,
  timeRange = '30d',
  onTimeRangeChange,
  className = ''
}) => {
  const [chartType, setChartType] = useState<'line' | 'bar'>('line');
  const [showEvents, setShowEvents] = useState(true);

  // Process data for visualization
  const processedData = useMemo(() => {
    if (!data.length) return [];

    // Filter by time range
    const now = new Date();
    const cutoffDate = new Date();
    
    switch (timeRange) {
      case '7d':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        cutoffDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        cutoffDate.setDate(now.getDate() - 90);
        break;
      default:
        cutoffDate.setFullYear(2020); // Show all data
    }

    return data
      .filter(point => new Date(point.timestamp) >= cutoffDate)
      .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
  }, [data, timeRange]);

  // Calculate statistics
  const stats = useMemo(() => {
    if (!processedData.length) return null;

    const scores = processedData.map(d => d.score);
    const currentScore = scores[scores.length - 1];
    const previousScore = scores.length > 1 ? scores[scores.length - 2] : currentScore;
    const minScore = Math.min(...scores);
    const maxScore = Math.max(...scores);
    const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const trend = currentScore - previousScore;

    return {
      current: currentScore,
      previous: previousScore,
      min: minScore,
      max: maxScore,
      average: avgScore,
      trend,
      improvement: maxScore - minScore,
      totalVersions: processedData.length
    };
  }, [processedData]);

  const handleExport = () => {
    const csvContent = [
      'Timestamp,Score,Version,Event',
      ...processedData.map(point => 
        `${point.timestamp},${point.score},${point.version},${point.trigger_event || ''}`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `score-history-${projectId || 'all'}-${Date.now()}.csv`;
    link.click();
    URL.revokeObjectURL(url);
  };

  if (!data.length) {
    return (
      <div className={`bg-white rounded-2xl shadow-sm border border-gray-100 p-8 text-center ${className}`}>
        <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Sin datos de historial</h3>
        <p className="text-gray-600">
          Los datos del historial de puntuaciones aparecerán aquí una vez que comiences a analizar contenido.
        </p>
      </div>
    );
  }

  return (
    <motion.div
      className={`bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Evolución de Puntuación</h2>
              <p className="text-gray-600 text-sm">Historial de GPT Rank Score</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Chart Type Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setChartType('line')}
                className={`p-2 rounded-md transition-colors duration-200 ${
                  chartType === 'line' 
                    ? 'bg-white text-blue-600 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <LineChart className="w-4 h-4" />
              </button>
              <button
                onClick={() => setChartType('bar')}
                className={`p-2 rounded-md transition-colors duration-200 ${
                  chartType === 'bar' 
                    ? 'bg-white text-blue-600 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <BarChart3 className="w-4 h-4" />
              </button>
            </div>

            <button
              onClick={handleExport}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              title="Exportar datos"
            >
              <Download className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Time Range Selector */}
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-500" />
          <div className="flex bg-gray-100 rounded-lg p-1">
            {(['7d', '30d', '90d', 'all'] as const).map((range) => (
              <button
                key={range}
                onClick={() => onTimeRangeChange?.(range)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors duration-200 ${
                  timeRange === range
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {range === '7d' ? '7 días' :
                 range === '30d' ? '30 días' :
                 range === '90d' ? '90 días' : 'Todo'}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="p-6 border-b border-gray-100">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-xl">
              <div className="text-2xl font-bold text-blue-600">{stats.current.toFixed(1)}</div>
              <div className="text-sm text-blue-600">Actual</div>
              {stats.trend !== 0 && (
                <div className={`flex items-center justify-center gap-1 mt-1 ${
                  stats.trend > 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stats.trend > 0 ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
                  <span className="text-xs">{Math.abs(stats.trend).toFixed(1)}</span>
                </div>
              )}
            </div>

            <div className="text-center p-4 bg-green-50 rounded-xl">
              <div className="text-2xl font-bold text-green-600">{stats.max.toFixed(1)}</div>
              <div className="text-sm text-green-600">Máximo</div>
            </div>

            <div className="text-center p-4 bg-yellow-50 rounded-xl">
              <div className="text-2xl font-bold text-yellow-600">{stats.average.toFixed(1)}</div>
              <div className="text-sm text-yellow-600">Promedio</div>
            </div>

            <div className="text-center p-4 bg-purple-50 rounded-xl">
              <div className="text-2xl font-bold text-purple-600">{stats.totalVersions}</div>
              <div className="text-sm text-purple-600">Versiones</div>
            </div>
          </div>
        </div>
      )}

      {/* Chart Area */}
      <div className="p-6">
        <div className="relative h-64 bg-gray-50 rounded-xl overflow-hidden">
          {/* Simple SVG Chart */}
          <svg className="w-full h-full" viewBox="0 0 800 200">
            {/* Grid lines */}
            {[0, 25, 50, 75, 100].map((value) => (
              <g key={value}>
                <line
                  x1="0"
                  y1={200 - (value * 2)}
                  x2="800"
                  y2={200 - (value * 2)}
                  stroke="#e5e7eb"
                  strokeWidth="1"
                />
                <text
                  x="10"
                  y={200 - (value * 2) - 5}
                  fontSize="12"
                  fill="#6b7280"
                >
                  {value}
                </text>
              </g>
            ))}

            {/* Data visualization */}
            {processedData.length > 1 && (
              <>
                {chartType === 'line' ? (
                  // Line chart
                  <polyline
                    points={processedData.map((point, index) => {
                      const x = (index / (processedData.length - 1)) * 780 + 10;
                      const y = 200 - (point.score * 2);
                      return `${x},${y}`;
                    }).join(' ')}
                    fill="none"
                    stroke="url(#gradient)"
                    strokeWidth="3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                ) : (
                  // Bar chart
                  processedData.map((point, index) => {
                    const x = (index / processedData.length) * 780 + 10;
                    const width = 780 / processedData.length - 5;
                    const height = point.score * 2;
                    const y = 200 - height;
                    
                    return (
                      <rect
                        key={index}
                        x={x}
                        y={y}
                        width={width}
                        height={height}
                        fill="url(#gradient)"
                        rx="2"
                      />
                    );
                  })
                )}

                {/* Data points */}
                {processedData.map((point, index) => {
                  const x = chartType === 'line' 
                    ? (index / (processedData.length - 1)) * 780 + 10
                    : (index / processedData.length) * 780 + 10 + (780 / processedData.length - 5) / 2;
                  const y = 200 - (point.score * 2);
                  
                  return (
                    <g key={index}>
                      <circle
                        cx={x}
                        cy={y}
                        r="4"
                        fill="#3b82f6"
                        stroke="white"
                        strokeWidth="2"
                      />
                      {showEvents && point.trigger_event && (
                        <circle
                          cx={x}
                          cy={y - 15}
                          r="3"
                          fill="#f59e0b"
                        />
                      )}
                    </g>
                  );
                })}

                {/* Gradient definition */}
                <defs>
                  <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#3b82f6" />
                    <stop offset="100%" stopColor="#8b5cf6" />
                  </linearGradient>
                </defs>
              </>
            )}
          </svg>

          {/* Overlay for interactions */}
          <div className="absolute inset-0 pointer-events-none">
            {/* You could add tooltips or hover effects here */}
          </div>
        </div>

        {/* Legend */}
        <div className="flex items-center justify-center gap-6 mt-4 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span>GPT Rank Score</span>
          </div>
          {showEvents && (
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <span>Eventos</span>
            </div>
          )}
        </div>
      </div>

      {/* Recent Changes */}
      {processedData.length > 1 && (
        <div className="p-6 border-t border-gray-100 bg-gray-50">
          <h3 className="font-semibold text-gray-900 mb-3">Cambios Recientes</h3>
          <div className="space-y-2">
            {processedData.slice(-3).reverse().map((point, index) => (
              <div key={index} className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-700">
                    Versión {point.version}
                  </span>
                  {point.trigger_event && (
                    <span className="text-gray-500">• {point.trigger_event}</span>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium text-gray-900">{point.score.toFixed(1)}</span>
                  <span className="text-gray-500">
                    {new Date(point.timestamp).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default ScoreHistory;

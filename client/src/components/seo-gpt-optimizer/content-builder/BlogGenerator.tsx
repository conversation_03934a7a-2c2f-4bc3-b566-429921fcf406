/**
 * SEO & GPT Optimizer™ - Blog Generator Component
 * AI-powered blog generation with Creative Genius
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Sparkles, 
  FileText, 
  Image, 
  Clock, 
  Target,
  Wand2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

import { useBlogGenerator } from '../../../hooks/seo-gpt-optimizer/useBlogGenerator';

interface BlogGeneratorProps {
  projectId: string;
  onContentGenerated: (content: string) => void;
  className?: string;
}

const BlogGenerator: React.FC<BlogGeneratorProps> = ({
  projectId,
  onContentGenerated,
  className = ''
}) => {
  const [topic, setTopic] = useState('');
  const [contentType, setContentType] = useState('educational');
  const [includeImages, setIncludeImages] = useState(true);
  const [numImages, setNumImages] = useState(3);
  const [targetLength, setTargetLength] = useState('medium');

  const {
    generatedBlog,
    loading,
    error,
    hasGeneratedBlog,
    isGenerating,
    generateBlog,
    clearError,
    blogContent,
    blogImages,
    creativeConcept,
    contentStats
  } = useBlogGenerator();

  const handleGenerate = async () => {
    if (!topic.trim()) return;

    await generateBlog({
      project_id: projectId,
      topic: topic.trim(),
      content_type: contentType,
      include_images: includeImages,
      num_images: numImages,
      target_length: targetLength
    });
  };

  const handleUseContent = () => {
    if (blogContent) {
      onContentGenerated(blogContent);
    }
  };

  return (
    <div className={`bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-purple-50 to-blue-50">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-gray-900">Generador de Blogs IA</h3>
            <p className="text-sm text-gray-600">Powered by Creative Genius</p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {!hasGeneratedBlog ? (
          /* Generation Form */
          <div className="space-y-6">
            {/* Topic Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tema del Blog
              </label>
              <input
                type="text"
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
                placeholder="Ej: Beneficios del magnesio para la salud"
                className="w-full px-4 py-3 border border-gray-300 rounded-xl bg-white text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
              />
            </div>

            {/* Content Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tipo de Contenido
              </label>
              <select
                value={contentType}
                onChange={(e) => setContentType(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl bg-white text-gray-900 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
              >
                <option value="educational">Educativo</option>
                <option value="motivational">Motivacional</option>
                <option value="balanced">Balanceado</option>
              </select>
            </div>

            {/* Target Length */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Longitud del Blog
              </label>
              <select
                value={targetLength}
                onChange={(e) => setTargetLength(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl bg-white text-gray-900 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
              >
                <option value="short">Corto (800-1200 palabras)</option>
                <option value="medium">Medio (1500-2500 palabras)</option>
                <option value="long">Largo (3000-5000 palabras)</option>
              </select>
            </div>

            {/* Images Options */}
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="includeImages"
                  checked={includeImages}
                  onChange={(e) => setIncludeImages(e.target.checked)}
                  className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                />
                <label htmlFor="includeImages" className="text-sm font-medium text-gray-700">
                  Incluir imágenes generadas con IA
                </label>
              </div>

              {includeImages && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Número de imágenes
                  </label>
                  <select
                    value={numImages}
                    onChange={(e) => setNumImages(parseInt(e.target.value))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl bg-white text-gray-900 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                  >
                    <option value={1}>1 imagen</option>
                    <option value={2}>2 imágenes</option>
                    <option value={3}>3 imágenes</option>
                    <option value={4}>4 imágenes</option>
                  </select>
                </div>
              )}
            </div>

            {/* Error Display */}
            {error.hasError && (
              <motion.div
                className="p-4 bg-red-50 border border-red-200 rounded-xl"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-5 h-5 text-red-500" />
                  <p className="text-red-700 text-sm">{error.message}</p>
                </div>
              </motion.div>
            )}

            {/* Generate Button */}
            <motion.button
              onClick={handleGenerate}
              disabled={!topic.trim() || isGenerating}
              className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-4 rounded-xl font-medium hover:from-purple-700 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center gap-3"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Wand2 className="w-5 h-5" />
              {isGenerating ? 'Generando Blog...' : 'Generar Blog con IA'}
            </motion.button>
          </div>
        ) : (
          /* Generated Content Preview */
          <div className="space-y-6">
            {/* Success Message */}
            <motion.div
              className="p-4 bg-green-50 border border-green-200 rounded-xl"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
            >
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <p className="text-green-700 font-medium">¡Blog generado exitosamente!</p>
              </div>
            </motion.div>

            {/* Content Stats */}
            {contentStats && (
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-3 bg-gray-50 rounded-xl">
                  <FileText className="w-5 h-5 text-gray-600 mx-auto mb-1" />
                  <p className="text-sm font-medium text-gray-900">{contentStats.word_count}</p>
                  <p className="text-xs text-gray-600">Palabras</p>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-xl">
                  <Clock className="w-5 h-5 text-gray-600 mx-auto mb-1" />
                  <p className="text-sm font-medium text-gray-900">{contentStats.estimated_reading_time}</p>
                  <p className="text-xs text-gray-600">Lectura</p>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-xl">
                  <Image className="w-5 h-5 text-gray-600 mx-auto mb-1" />
                  <p className="text-sm font-medium text-gray-900">{blogImages.length}</p>
                  <p className="text-xs text-gray-600">Imágenes</p>
                </div>
              </div>
            )}

            {/* Creative Concept */}
            {creativeConcept && (
              <div className="p-4 bg-purple-50 border border-purple-200 rounded-xl">
                <h4 className="font-medium text-purple-900 mb-2">Concepto Creativo</h4>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Hook:</span> {creativeConcept.hook}</p>
                  <p><span className="font-medium">Ángulo:</span> {creativeConcept.content_angle}</p>
                  <p><span className="font-medium">Score Viral:</span> {creativeConcept.viral_score}/10</p>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3">
              <motion.button
                onClick={handleUseContent}
                className="flex-1 bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-3 rounded-xl font-medium hover:from-green-700 hover:to-green-800 transition-all duration-200 flex items-center justify-center gap-2"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <CheckCircle className="w-4 h-4" />
                Usar Este Contenido
              </motion.button>
              
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-all duration-200"
              >
                Generar Nuevo
              </button>
            </div>
          </div>
        )}

        {/* Loading State */}
        <AnimatePresence>
          {isGenerating && (
            <motion.div
              className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <motion.div
                className="bg-white rounded-2xl p-8 shadow-xl max-w-md w-full mx-4"
                initial={{ scale: 0.9, y: 20 }}
                animate={{ scale: 1, y: 0 }}
                exit={{ scale: 0.9, y: 20 }}
              >
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Sparkles className="w-8 h-8 text-white animate-pulse" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">Generando Blog</h3>
                  <p className="text-gray-600 mb-4">{loading.message}</p>
                  
                  {/* Progress Bar */}
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                    <motion.div
                      className="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${loading.progress}%` }}
                      transition={{ duration: 0.5 }}
                    />
                  </div>
                  
                  <p className="text-sm text-gray-500">{loading.progress}% completado</p>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default BlogGenerator;

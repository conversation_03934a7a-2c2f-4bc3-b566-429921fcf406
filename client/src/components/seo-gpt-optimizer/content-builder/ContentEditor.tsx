/**
 * SEO & GPT Optimizer™ - Content Editor Component
 * Main content editor with real-time GPT Rank analysis
 */

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import {
  Type,
  Save,
  Download,
  Eye,
  EyeOff,
  Maximize2,
  Minimize2,
  FileText,
  Clock,
  Target,
  Sparkles,
  BarChart3
} from 'lucide-react';

import { useGPTRank } from '../../../hooks/seo-gpt-optimizer/useGPTRank';
import { ContentType } from '../../../types/seo-gpt-optimizer';
import LoadingSpinner from '../shared/LoadingSpinner';
import BlogGenerator from './BlogGenerator';
import RichTextEditor from './RichTextEditor';
import ImageGenerator from './ImageGenerator';

interface ContentEditorProps {
  projectId?: string;
  initialContent?: string;
  initialTopic?: string;
  contentType?: ContentType;
  onSave?: (content: string) => void;
  onContentChange?: (content: string, analysis: any) => void;
  className?: string;
}

const ContentEditor: React.FC<ContentEditorProps> = ({
  projectId,
  initialContent = '',
  initialTopic = '',
  contentType = ContentType.ARTICLE,
  onSave,
  onContentChange,
  className = ''
}) => {
  const [content, setContent] = useState(initialContent);
  const [topic, setTopic] = useState(initialTopic);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [showBlogGenerator, setShowBlogGenerator] = useState(false);
  const [showImageGenerator, setShowImageGenerator] = useState(false);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const {
    analysis,
    loading,
    error,
    analyzeContentRealtime,
    clearError,
    hasAnalysis,
    gptRankScore,
    scoreGrade,
    needsImprovement
  } = useGPTRank();

  // Update word and character count
  useEffect(() => {
    const words = content.trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
    setCharCount(content.length);
  }, [content]);

  // Manual analysis trigger
  const handleAnalyzeContent = useCallback(() => {
    if (content.trim().length > 20) {
      const analysisTarget = topic.trim() || 'contenido general';
      analyzeContentRealtime(content, analysisTarget);
    }
  }, [content, topic, analyzeContentRealtime]);

  // Notify parent of content changes (no auto-analysis)
  useEffect(() => {
    if (onContentChange) {
      onContentChange(content, analysis);
    }
  }, [content, analysis, onContentChange]);

  const handleContentChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setContent(e.target.value);
    clearError();
  }, [clearError]);

  const handleTopicChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setTopic(e.target.value);
    clearError();
  }, [clearError]);

  const handleSave = useCallback(() => {
    if (onSave) {
      onSave(content);
      setLastSaved(new Date());

      // Manual analysis after saving (removed auto-analysis)
      // User can click "Analizar" button if they want analysis
    }
  }, [content, onSave, topic, analyzeContentRealtime]);

  const handleExport = useCallback(() => {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${topic || 'content'}-${Date.now()}.txt`;
    link.click();
    URL.revokeObjectURL(url);
  }, [content, topic]);

  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen);
    if (!isFullscreen && containerRef.current) {
      containerRef.current.requestFullscreen?.();
    } else if (document.fullscreenElement) {
      document.exitFullscreen?.();
    }
  }, [isFullscreen]);

  const togglePreview = useCallback(() => {
    setShowPreview(!showPreview);
  }, [showPreview]);

  const handleGeneratedContent = useCallback((generatedContent: string) => {
    setContent(generatedContent);
    setShowBlogGenerator(false);
    clearError();

    // Manual analysis for generated content (removed auto-analysis)
    // User can click "Analizar" button if they want analysis
  }, [clearError, topic, analyzeContentRealtime]);

  // Handle image generation
  const handleImageGenerated = useCallback((imageUrl: string, prompt: string) => {
    const imageMarkdown = `\n\n![${prompt}](${imageUrl})\n\n`;
    setContent(prev => prev + imageMarkdown);
    setShowImageGenerator(false);
  }, []);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [content]);

  const getScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 70) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <motion.div
      ref={containerRef}
      className={`bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden ${
        isFullscreen ? 'fixed inset-0 z-50 rounded-none' : ''
      } ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-100 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Type className="w-4 h-4 text-white" />
              </div>
              <h2 className="text-lg font-bold text-gray-900">Content Builder</h2>
            </div>

            {/* Real-time Score Display */}
            {hasAnalysis && (
              <motion.div
                className="flex items-center gap-3 px-4 py-2 bg-white rounded-xl border border-gray-200"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", duration: 0.5 }}
              >
                <div className="flex items-center gap-2">
                  <Target className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">GPT Rank:</span>
                  <span className={`text-lg font-bold ${getScoreColor(gptRankScore)}`}>
                    {gptRankScore.toFixed(1)}
                  </span>
                  <span className="text-sm text-gray-500">({scoreGrade})</span>
                </div>
                
                {loading.isLoading && (
                  <div className="w-4 h-4 border-2 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
                )}
              </motion.div>
            )}
          </div>

          <div className="flex items-center gap-2">
            {/* Stats */}
            <div className="flex items-center gap-4 text-sm text-gray-600 mr-4">
              <div className="flex items-center gap-1">
                <FileText className="w-4 h-4" />
                <span>{wordCount} palabras</span>
              </div>
              <div className="flex items-center gap-1">
                <Type className="w-4 h-4" />
                <span>{charCount} caracteres</span>
              </div>
              {content.trim().length >= 20 && (
                <div className="flex items-center gap-1 text-green-600">
                  <BarChart3 className="w-4 h-4" />
                  <span>Listo para analizar</span>
                </div>
              )}
              {lastSaved && (
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>Guardado {lastSaved.toLocaleTimeString()}</span>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <button
              onClick={() => setShowBlogGenerator(!showBlogGenerator)}
              className="p-2 text-purple-600 hover:text-purple-700 hover:bg-purple-50 rounded-lg transition-colors duration-200"
              title="Generar blog con IA"
            >
              <Sparkles className="w-4 h-4" />
            </button>

            <button
              onClick={handleAnalyzeContent}
              disabled={content.trim().length < 20 || loading.isLoading}
              className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Analizar contenido con Emma AI"
            >
              <BarChart3 className="w-4 h-4" />
              {loading.isLoading ? 'Analizando...' : 'Analizar'}
            </button>

            <button
              onClick={togglePreview}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              title={showPreview ? "Ocultar vista previa" : "Mostrar vista previa"}
            >
              {showPreview ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>

            <button
              onClick={toggleFullscreen}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              title={isFullscreen ? "Salir de pantalla completa" : "Pantalla completa"}
            >
              {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </button>

            <button
              onClick={handleExport}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              title="Exportar contenido"
            >
              <Download className="w-4 h-4" />
            </button>

            {onSave && (
              <button
                onClick={handleSave}
                className="flex items-center gap-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:from-purple-700 hover:to-blue-700 transition-all duration-200"
              >
                <Save className="w-4 h-4" />
                Guardar
              </button>
            )}
          </div>
        </div>

        {/* Topic Input */}
        <div className="mt-4">
          <div className="relative">
            <input
              type="text"
              value={topic}
              onChange={handleTopicChange}
              placeholder="Tema principal del contenido (ej: beneficios del magnesio para la salud)"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
            />
            {topic.trim().length === 0 && content.trim().length > 20 && (
              <div className="absolute right-3 top-2 text-orange-500">
                <span className="text-xs">⚠️ Tema requerido para análisis</span>
              </div>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error.hasError && (
          <motion.div
            className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <p className="text-red-700 text-sm">{error.message}</p>
          </motion.div>
        )}
      </div>

      {/* Blog Generator */}
      {showBlogGenerator && (
        <div className="p-6 border-b border-gray-100">
          <BlogGenerator
            projectId={projectId || 'demo'}
            onContentGenerated={handleGeneratedContent}
          />
        </div>
      )}

      {/* Rich Text Editor */}
      <div className="flex-1">
        <RichTextEditor
          content={content}
          onChange={handleContentChange}
          onSave={handleSave}
          placeholder="Comienza a escribir tu blog...

Consejos para un mejor Emma AI Rank:
• Usa encabezados (H1, H2, H3) para estructurar
• Incluye imágenes relevantes
• Escribe párrafos claros y concisos
• Usa negritas para destacar puntos importantes
• Mantén un tono profesional y autoritativo"
          className="h-full"
        />

        {/* Loading Overlay */}
        {loading.isLoading && (
          <motion.div
            className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-3 border border-gray-200 z-10"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
              <span className="text-sm text-gray-600">Analizando con Emma AI...</span>
            </div>
          </motion.div>
        )}
      </div>

      {/* Image Generator Modal */}
      {showImageGenerator && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <ImageGenerator
              onImageGenerated={handleImageGenerated}
              onClose={() => setShowImageGenerator(false)}
            />
          </div>
        </div>
      )}

      {/* Footer with Quick Stats */}
      <div className="p-4 border-t border-gray-100 bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center gap-6">
            <span>Líneas: {content.split('\n').length}</span>
            <span>Párrafos: {content.split('\n\n').filter(p => p.trim()).length}</span>
            <span>Tiempo de lectura: ~{Math.ceil(wordCount / 200)} min</span>
          </div>
          
          {hasAnalysis && (
            <div className="flex items-center gap-4">
              <span>Confianza: {analysis?.confidence_level || 'N/A'}</span>
              {needsImprovement && (
                <span className="text-orange-600 font-medium">
                  ⚠️ Necesita mejoras
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default ContentEditor;

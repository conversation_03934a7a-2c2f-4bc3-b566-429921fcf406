/**
 * SEO & GPT Optimizer™ - GPT Rank Panel Component
 * Side panel showing detailed GPT Rank metrics and breakdown
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Target, 
  TrendingUp, 
  Brain, 
  FileText, 
  Eye, 
  CheckCircle,
  AlertCircle,
  Info,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

import { GPTRankAnalysis, ComponentScores } from '../../../types/seo-gpt-optimizer';
import GPTRankMeter from '../shared/GPTRankMeter';
import LoadingSpinner from '../shared/LoadingSpinner';

interface GPTRankPanelProps {
  analysis: GPTRankAnalysis | null;
  loading: boolean;
  error?: string;
  className?: string;
}

const componentInfo = {
  semantic_similarity: {
    name: 'Similitud Semántica',
    description: 'Qué tan similar es tu contenido a fuentes autoritativas como Wikipedia',
    icon: Brain,
    tips: [
      'Usa terminología técnica y precisa',
      'Estructura como artículos académicos',
      'Incluye definiciones claras'
    ]
  },
  logical_coherence: {
    name: 'Coherencia Lógica',
    description: 'Flujo lógico y estructura del contenido',
    icon: FileText,
    tips: [
      'Usa conectores entre párrafos',
      'Mantén una progresión clara de ideas',
      'Estructura: introducción → desarrollo → conclusión'
    ]
  },
  authority_signals: {
    name: 'Señales de Autoridad',
    description: 'Indicadores de expertise y credibilidad',
    icon: CheckCircle,
    tips: [
      'Incluye datos y estadísticas',
      'Menciona estudios y fuentes',
      'Usa lenguaje autoritativo'
    ]
  },
  citability_score: {
    name: 'Citabilidad',
    description: 'Qué tan fácil es citar tu contenido',
    icon: Eye,
    tips: [
      'Crea declaraciones claras y concisas',
      'Incluye definiciones específicas',
      'Evita lenguaje ambiguo'
    ]
  },
  clarity_score: {
    name: 'Claridad',
    description: 'Legibilidad y comprensión del contenido',
    icon: Info,
    tips: [
      'Usa oraciones de 15-25 palabras',
      'Párrafos de 3-5 oraciones',
      'Evita jerga innecesaria'
    ]
  },
  completeness_score: {
    name: 'Completitud',
    description: 'Qué tan completo es el contenido sobre el tema',
    icon: TrendingUp,
    tips: [
      'Responde qué, cómo, por qué, cuándo',
      'Incluye ejemplos prácticos',
      'Cubre casos de uso específicos'
    ]
  }
};

const GPTRankPanel: React.FC<GPTRankPanelProps> = ({
  analysis,
  loading,
  error,
  className = ''
}) => {
  const [expandedComponent, setExpandedComponent] = useState<string | null>(null);

  const toggleComponent = (component: string) => {
    setExpandedComponent(expandedComponent === component ? null : component);
  };

  const getScoreColor = (score: number): string => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 70) return 'bg-blue-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getScoreTextColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 70) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-2xl shadow-sm border border-gray-100 p-6 ${className}`}>
        <div className="text-center">
          <LoadingSpinner size="md" message="Analizando contenido..." />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-2xl shadow-sm border border-gray-100 p-6 ${className}`}>
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="font-semibold text-gray-900 mb-2">Error en el análisis</h3>
          <p className="text-sm text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  if (!analysis) {
    return (
      <div className={`bg-white rounded-2xl shadow-sm border border-gray-100 p-6 ${className}`}>
        <div className="text-center">
          <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="font-semibold text-gray-900 mb-2">GPT Rank Analysis</h3>
          <p className="text-sm text-gray-600 mb-4">
            Comienza a escribir para ver el análisis en tiempo real
          </p>
          <div className="text-xs text-gray-500">
            Necesitas al menos 50 caracteres y un tema definido
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      className={`bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden ${className}`}
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
            <Target className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-gray-900">GPT Rank Analysis</h2>
            <p className="text-sm text-gray-600">Análisis en tiempo real</p>
          </div>
        </div>

        {/* Main Score */}
        <div className="text-center">
          <GPTRankMeter
            score={analysis.gpt_rank_score}
            grade={analysis.score_grade}
            size="md"
            showDetails={true}
            animated={true}
          />
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4 mt-6">
          <div className="text-center p-3 bg-gray-50 rounded-xl">
            <div className="text-lg font-bold text-gray-900">
              {analysis.content_stats.word_count}
            </div>
            <div className="text-xs text-gray-600">Palabras</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-xl">
            <div className="text-lg font-bold text-gray-900">
              {analysis.content_stats.paragraph_count}
            </div>
            <div className="text-xs text-gray-600">Párrafos</div>
          </div>
        </div>
      </div>

      {/* Component Breakdown */}
      <div className="p-6">
        <h3 className="font-semibold text-gray-900 mb-4">Desglose por Componentes</h3>
        
        <div className="space-y-3">
          {Object.entries(analysis.component_scores).map(([component, score]) => {
            const info = componentInfo[component as keyof ComponentScores];
            const IconComponent = info.icon;
            const isExpanded = expandedComponent === component;
            
            return (
              <div key={component} className="border border-gray-200 rounded-xl overflow-hidden">
                <button
                  onClick={() => toggleComponent(component)}
                  className="w-full p-4 text-left hover:bg-gray-50 transition-colors duration-200"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`w-8 h-8 ${getScoreColor(score)} rounded-lg flex items-center justify-center`}>
                        <IconComponent className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{info.name}</div>
                        <div className="text-xs text-gray-600">{score.toFixed(1)}/100</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className={`text-sm font-bold ${getScoreTextColor(score)}`}>
                        {score.toFixed(1)}
                      </span>
                      {isExpanded ? 
                        <ChevronUp className="w-4 h-4 text-gray-400" /> : 
                        <ChevronDown className="w-4 h-4 text-gray-400" />
                      }
                    </div>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="mt-3 w-full bg-gray-200 rounded-full h-2">
                    <motion.div
                      className={`h-2 rounded-full ${getScoreColor(score)}`}
                      initial={{ width: 0 }}
                      animate={{ width: `${score}%` }}
                      transition={{ duration: 1, ease: "easeOut" }}
                    />
                  </div>
                </button>

                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="border-t border-gray-200 bg-gray-50"
                    >
                      <div className="p-4">
                        <p className="text-sm text-gray-700 mb-3">{info.description}</p>
                        
                        <div className="space-y-2">
                          <h4 className="text-xs font-semibold text-gray-900 uppercase tracking-wide">
                            Tips para mejorar:
                          </h4>
                          {info.tips.map((tip, index) => (
                            <div key={index} className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                              <span className="text-xs text-gray-600">{tip}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            );
          })}
        </div>
      </div>

      {/* Confidence Level */}
      <div className="p-6 border-t border-gray-100 bg-gray-50">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-sm font-medium text-gray-900">Nivel de Confianza</div>
            <div className="text-xs text-gray-600">Precisión del análisis</div>
          </div>
          <div className={`px-3 py-1 rounded-full text-xs font-medium ${
            analysis.confidence_level === 'high' 
              ? 'bg-green-100 text-green-700'
              : analysis.confidence_level === 'medium'
              ? 'bg-yellow-100 text-yellow-700'
              : 'bg-red-100 text-red-700'
          }`}>
            {analysis.confidence_level === 'high' ? 'Alto' :
             analysis.confidence_level === 'medium' ? 'Medio' : 'Bajo'}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default GPTRankPanel;

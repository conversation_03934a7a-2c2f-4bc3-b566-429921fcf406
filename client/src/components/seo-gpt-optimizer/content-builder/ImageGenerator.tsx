/**
 * Image Generator - Generador de imágenes con Ideogram AI
 * Integrado en el editor de blog
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Sparkles,
  Download,
  Copy,
  Check,
  Loader2,
  Image as ImageIcon,
  Wand2,
  Palette
} from 'lucide-react';

interface ImageGeneratorProps {
  onImageGenerated: (imageUrl: string, prompt: string) => void;
  onClose: () => void;
  className?: string;
}

const ImageGenerator: React.FC<ImageGeneratorProps> = ({
  onImageGenerated,
  onClose,
  className = ''
}) => {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImages, setGeneratedImages] = useState<Array<{
    url: string;
    prompt: string;
    id: string;
  }>>([]);
  const [selectedStyle, setSelectedStyle] = useState('realistic');
  const [copiedId, setCopiedId] = useState<string | null>(null);

  const styles = [
    { id: 'realistic', name: 'Realista', description: 'Fotografías realistas' },
    { id: 'illustration', name: 'Ilustración', description: 'Estilo ilustrativo' },
    { id: 'digital-art', name: 'Arte Digital', description: 'Arte digital moderno' },
    { id: 'minimalist', name: 'Minimalista', description: 'Diseño limpio y simple' },
    { id: 'vintage', name: 'Vintage', description: 'Estilo retro y nostálgico' },
    { id: 'abstract', name: 'Abstracto', description: 'Arte abstracto y conceptual' }
  ];

  const generateImage = async () => {
    if (!prompt.trim()) return;

    setIsGenerating(true);
    
    try {
      // Construir prompt mejorado según el estilo
      let enhancedPrompt = prompt;
      
      switch (selectedStyle) {
        case 'realistic':
          enhancedPrompt = `Professional high-quality photograph of ${prompt}, photorealistic, detailed, sharp focus, professional lighting`;
          break;
        case 'illustration':
          enhancedPrompt = `Beautiful illustration of ${prompt}, artistic style, vibrant colors, detailed artwork`;
          break;
        case 'digital-art':
          enhancedPrompt = `Digital art of ${prompt}, modern digital painting, artistic, creative composition`;
          break;
        case 'minimalist':
          enhancedPrompt = `Minimalist design of ${prompt}, clean, simple, elegant, white background`;
          break;
        case 'vintage':
          enhancedPrompt = `Vintage style ${prompt}, retro aesthetic, nostalgic mood, classic composition`;
          break;
        case 'abstract':
          enhancedPrompt = `Abstract representation of ${prompt}, conceptual art, creative interpretation`;
          break;
      }

      const response = await fetch('/api/generate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: enhancedPrompt,
          style: selectedStyle,
          model: 'ideogram-3.0-quality'
        }),
      });

      if (!response.ok) {
        throw new Error('Error al generar imagen');
      }

      const data = await response.json();
      
      const newImage = {
        id: Date.now().toString(),
        url: data.imageUrl,
        prompt: prompt
      };

      setGeneratedImages(prev => [newImage, ...prev]);
      
    } catch (error) {
      console.error('Error generating image:', error);
      alert('Error al generar la imagen. Inténtalo de nuevo.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      generateImage();
    }
  };

  const copyImageUrl = async (imageUrl: string, imageId: string) => {
    try {
      await navigator.clipboard.writeText(imageUrl);
      setCopiedId(imageId);
      setTimeout(() => setCopiedId(null), 2000);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
    }
  };

  const insertImage = (imageUrl: string, prompt: string) => {
    onImageGenerated(imageUrl, prompt);
    onClose();
  };

  const suggestedPrompts = [
    "Una oficina moderna con plantas y luz natural",
    "Persona trabajando en una computadora con gráficos",
    "Equipo de trabajo colaborando en una mesa",
    "Gráficos y estadísticas en una pantalla",
    "Concepto de inteligencia artificial y tecnología",
    "Diseño web responsive en diferentes dispositivos"
  ];

  return (
    <motion.div
      className={`bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
    >
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-gray-900">Generar Imagen con IA</h2>
              <p className="text-sm text-gray-600">Powered by Ideogram AI</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            ×
          </button>
        </div>
      </div>

      {/* Style Selection */}
      <div className="p-6 border-b border-gray-100">
        <h3 className="text-sm font-semibold text-gray-900 mb-3">Estilo de imagen</h3>
        <div className="grid grid-cols-3 gap-2">
          {styles.map((style) => (
            <button
              key={style.id}
              onClick={() => setSelectedStyle(style.id)}
              className={`p-3 text-left rounded-lg border transition-all duration-200 ${
                selectedStyle === style.id
                  ? 'border-purple-500 bg-purple-50 text-purple-700'
                  : 'border-gray-200 hover:border-gray-300 text-gray-700'
              }`}
            >
              <div className="font-medium text-sm">{style.name}</div>
              <div className="text-xs text-gray-500 mt-1">{style.description}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Prompt Input */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex gap-3">
          <div className="flex-1">
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Describe la imagen que quieres generar..."
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
              rows={3}
            />
            
            {/* Suggested Prompts */}
            <div className="mt-3">
              <p className="text-xs text-gray-600 mb-2">Sugerencias:</p>
              <div className="flex flex-wrap gap-2">
                {suggestedPrompts.slice(0, 3).map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => setPrompt(suggestion)}
                    className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors duration-200"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          </div>
          
          <button
            onClick={generateImage}
            disabled={!prompt.trim() || isGenerating}
            className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center gap-2"
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Generando...
              </>
            ) : (
              <>
                <Wand2 className="w-4 h-4" />
                Generar
              </>
            )}
          </button>
        </div>
      </div>

      {/* Generated Images */}
      <div className="p-6 max-h-96 overflow-y-auto">
        <AnimatePresence>
          {generatedImages.length > 0 ? (
            <div className="space-y-4">
              <h3 className="text-sm font-semibold text-gray-900">Imágenes generadas</h3>
              <div className="grid grid-cols-2 gap-4">
                {generatedImages.map((image) => (
                  <motion.div
                    key={image.id}
                    className="relative group bg-gray-100 rounded-lg overflow-hidden"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <img
                      src={image.url}
                      alt={image.prompt}
                      className="w-full h-32 object-cover"
                    />
                    
                    {/* Overlay */}
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-2">
                      <button
                        onClick={() => insertImage(image.url, image.prompt)}
                        className="p-2 bg-white/90 text-gray-900 rounded-lg hover:bg-white transition-colors duration-200"
                        title="Insertar en blog"
                      >
                        <ImageIcon className="w-4 h-4" />
                      </button>
                      
                      <button
                        onClick={() => copyImageUrl(image.url, image.id)}
                        className="p-2 bg-white/90 text-gray-900 rounded-lg hover:bg-white transition-colors duration-200"
                        title="Copiar URL"
                      >
                        {copiedId === image.id ? (
                          <Check className="w-4 h-4 text-green-600" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                    
                    {/* Prompt */}
                    <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white p-2">
                      <p className="text-xs truncate">{image.prompt}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <Palette className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Genera tu primera imagen con IA</p>
              <p className="text-sm text-gray-500 mt-1">Describe lo que quieres ver y Emma lo creará</p>
            </div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

export default ImageGenerator;

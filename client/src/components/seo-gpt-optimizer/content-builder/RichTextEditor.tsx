/**
 * Rich Text Editor - Editor de blog inteligente tipo Grammarly
 * Con formato, imágenes, scroll infinito y sugerencias de Emma
 */

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Bold,
  Italic,
  Underline,
  Heading1,
  Heading2,
  Heading3,
  List,
  ListOrdered,
  Quote,
  Image,
  Link,
  Sparkles,
  Eye,
  Save,
  Download,
  Maximize2,
  Minimize2
} from 'lucide-react';

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  onSave?: () => void;
  placeholder?: string;
  className?: string;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  content,
  onChange,
  onSave,
  placeholder = "Comienza a escribir tu blog...",
  className = ''
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showImageGenerator, setShowImageGenerator] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [cursorPosition, setCursorPosition] = useState(0);
  
  const editorRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Handle text selection
  const handleTextSelect = useCallback(() => {
    if (textareaRef.current) {
      const start = textareaRef.current.selectionStart;
      const end = textareaRef.current.selectionEnd;
      const selected = content.substring(start, end);
      setSelectedText(selected);
      setCursorPosition(start);
    }
  }, [content]);

  // Format text functions
  const formatText = useCallback((format: string, value?: string) => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);
    
    let newText = '';
    let newContent = '';
    let newCursorPos = start;

    switch (format) {
      case 'h1':
        newText = `# ${selectedText || 'Encabezado 1'}`;
        break;
      case 'h2':
        newText = `## ${selectedText || 'Encabezado 2'}`;
        break;
      case 'h3':
        newText = `### ${selectedText || 'Encabezado 3'}`;
        break;
      case 'bold':
        newText = `**${selectedText || 'texto en negrita'}**`;
        break;
      case 'italic':
        newText = `*${selectedText || 'texto en cursiva'}*`;
        break;
      case 'underline':
        newText = `<u>${selectedText || 'texto subrayado'}</u>`;
        break;
      case 'quote':
        newText = `> ${selectedText || 'cita importante'}`;
        break;
      case 'list':
        newText = `- ${selectedText || 'elemento de lista'}`;
        break;
      case 'ordered-list':
        newText = `1. ${selectedText || 'elemento numerado'}`;
        break;
      case 'link':
        newText = `[${selectedText || 'texto del enlace'}](${value || 'https://ejemplo.com'})`;
        break;
      case 'image':
        newText = `![${selectedText || 'descripción de imagen'}](${value || 'url-de-imagen'})`;
        break;
      default:
        return;
    }

    newContent = content.substring(0, start) + newText + content.substring(end);
    newCursorPos = start + newText.length;

    onChange(newContent);

    // Restore cursor position
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
      }
    }, 0);
  }, [content, onChange]);

  // Insert content at cursor
  const insertAtCursor = useCallback((text: string) => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    const newContent = content.substring(0, start) + text + content.substring(end);
    onChange(newContent);

    // Restore cursor position
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        textareaRef.current.setSelectionRange(start + text.length, start + text.length);
      }
    }, 0);
  }, [content, onChange]);

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          formatText('bold');
          break;
        case 'i':
          e.preventDefault();
          formatText('italic');
          break;
        case 'u':
          e.preventDefault();
          formatText('underline');
          break;
        case 's':
          e.preventDefault();
          onSave?.();
          break;
        case '1':
          e.preventDefault();
          formatText('h1');
          break;
        case '2':
          e.preventDefault();
          formatText('h2');
          break;
        case '3':
          e.preventDefault();
          formatText('h3');
          break;
      }
    }
  }, [formatText, onSave]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [content]);

  const toolbarButtons = [
    { icon: Heading1, action: () => formatText('h1'), tooltip: 'Encabezado 1 (Ctrl+1)', shortcut: 'Ctrl+1' },
    { icon: Heading2, action: () => formatText('h2'), tooltip: 'Encabezado 2 (Ctrl+2)', shortcut: 'Ctrl+2' },
    { icon: Heading3, action: () => formatText('h3'), tooltip: 'Encabezado 3 (Ctrl+3)', shortcut: 'Ctrl+3' },
    { icon: Bold, action: () => formatText('bold'), tooltip: 'Negrita (Ctrl+B)', shortcut: 'Ctrl+B' },
    { icon: Italic, action: () => formatText('italic'), tooltip: 'Cursiva (Ctrl+I)', shortcut: 'Ctrl+I' },
    { icon: Underline, action: () => formatText('underline'), tooltip: 'Subrayado (Ctrl+U)', shortcut: 'Ctrl+U' },
    { icon: Quote, action: () => formatText('quote'), tooltip: 'Cita' },
    { icon: List, action: () => formatText('list'), tooltip: 'Lista con viñetas' },
    { icon: ListOrdered, action: () => formatText('ordered-list'), tooltip: 'Lista numerada' },
    { icon: Link, action: () => formatText('link'), tooltip: 'Enlace' },
    { icon: Image, action: () => setShowImageGenerator(true), tooltip: 'Insertar imagen' },
  ];

  return (
    <div className={`bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden ${className}`}>
      {/* Toolbar */}
      <div className="border-b border-gray-100 bg-gray-50 p-4">
        <div className="flex items-center justify-between">
          {/* Format Tools */}
          <div className="flex items-center gap-1">
            {toolbarButtons.map((button, index) => (
              <button
                key={index}
                onClick={button.action}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded-lg transition-colors duration-200 group relative"
                title={button.tooltip}
              >
                <button.icon className="w-4 h-4" />
                
                {/* Tooltip */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                  {button.tooltip}
                </div>
              </button>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowImageGenerator(!showImageGenerator)}
              className="p-2 text-purple-600 hover:text-purple-700 hover:bg-purple-50 rounded-lg transition-colors duration-200"
              title="Generar imagen con IA"
            >
              <Sparkles className="w-4 h-4" />
            </button>

            <button
              onClick={onSave}
              className="p-2 text-green-600 hover:text-green-700 hover:bg-green-50 rounded-lg transition-colors duration-200"
              title="Guardar (Ctrl+S)"
            >
              <Save className="w-4 h-4" />
            </button>

            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              title={isFullscreen ? "Salir de pantalla completa" : "Pantalla completa"}
            >
              {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </button>
          </div>
        </div>
      </div>

      {/* Editor */}
      <div className={`relative ${isFullscreen ? 'h-screen' : 'min-h-96'}`}>
        <textarea
          ref={textareaRef}
          value={content}
          onChange={(e) => onChange(e.target.value)}
          onSelect={handleTextSelect}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={`w-full p-6 border-none outline-none resize-none font-mono text-gray-900 bg-white leading-relaxed placeholder-gray-500 overflow-y-auto ${
            isFullscreen ? 'h-full' : 'min-h-96'
          }`}
          style={{
            minHeight: isFullscreen ? '100vh' : '400px',
            maxHeight: 'none'
          }}
        />

        {/* Writing Stats */}
        <div className="absolute bottom-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 text-sm text-gray-600 border border-gray-200">
          <div className="flex items-center gap-4">
            <span>{content.split(/\s+/).filter(w => w.length > 0).length} palabras</span>
            <span>{content.length} caracteres</span>
            <span>~{Math.ceil(content.split(/\s+/).filter(w => w.length > 0).length / 200)} min lectura</span>
          </div>
        </div>
      </div>

      {/* Image Generator Panel */}
      {showImageGenerator && (
        <motion.div
          className="border-t border-gray-100 bg-gray-50 p-4"
          initial={{ height: 0, opacity: 0 }}
          animate={{ height: 'auto', opacity: 1 }}
          exit={{ height: 0, opacity: 0 }}
        >
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium text-gray-900">Generar Imagen con IA</h3>
            <button
              onClick={() => setShowImageGenerator(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ×
            </button>
          </div>
          
          <div className="flex gap-3">
            <input
              type="text"
              placeholder="Describe la imagen que quieres generar..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
            <button className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200">
              Generar
            </button>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default RichTextEditor;

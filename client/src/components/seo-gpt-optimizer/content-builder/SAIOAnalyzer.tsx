/**
 * Emma AI Rank - World's First Real AI Content Optimizer
 * Based on comprehensive research of 500+ AI search queries
 */

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Brain, 
  Target, 
  TrendingUp, 
  CheckCircle, 
  AlertTriangle,
  Sparkles,
  BarChart3,
  Image,
  Clock,
  Shield,
  Search
} from 'lucide-react';

import { useSAIOAgent } from '../../../hooks/seo-gpt-optimizer/useSAIOAgent';

interface EmmaAIRankProps {
  projectId: string;
  content: string;
  onAnalyze?: () => void;
  className?: string;
}

const EmmaAIRank: React.FC<EmmaAIRankProps> = ({
  projectId,
  content,
  onAnalyze,
  className = ''
}) => {
  const {
    saioAnalysis,
    loading,
    error,
    analyzeSAIOContent,
    clearError,
    hasSAIOAnalysis,
    saioScore,
    saioGrade,
    citationProbability,
    isAnalyzing,
    getHighPriorityRecommendations,
    googleReady,
    chatgptReady,
    perplexityReady
  } = useSAIOAgent();

  const handleAnalyze = async () => {
    if (content.trim().length < 20) return;
    
    await analyzeSAIOContent({
      project_id: projectId,
      content: content,
      analysis_type: 'full'
    });
    
    if (onAnalyze) onAnalyze();
  };

  const getScoreColor = (score: number): string => {
    if (score >= 85) return 'text-green-600';
    if (score >= 70) return 'text-blue-600';
    if (score >= 55) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score: number): string => {
    if (score >= 85) return 'bg-green-50 border-green-200';
    if (score >= 70) return 'bg-blue-50 border-blue-200';
    if (score >= 55) return 'bg-yellow-50 border-yellow-200';
    return 'bg-red-50 border-red-200';
  };

  const componentIcons = {
    bing_seo_signals: Search,
    content_structure: BarChart3,
    authority_signals: Shield,
    multimedia_content: Image,
    content_freshness: Clock,
    schema_markup: Target
  };

  const componentNames = {
    bing_seo_signals: 'SEO Bing',
    content_structure: 'Estructura',
    authority_signals: 'Autoridad',
    multimedia_content: 'Multimedia',
    content_freshness: 'Frescura',
    schema_markup: 'Schema'
  };

  return (
    <div className={`bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-purple-50 to-blue-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
              <Brain className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-gray-900">Emma AI Rank</h3>
              <p className="text-sm text-gray-600">Optimizador de contenido para IA</p>
            </div>
          </div>
          
          <button
            onClick={handleAnalyze}
            disabled={content.trim().length < 20 || isAnalyzing}
            className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 rounded-xl font-medium hover:from-purple-700 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center gap-2"
          >
            <Sparkles className="w-4 h-4" />
            {isAnalyzing ? 'Analizando...' : 'Analizar con Emma'}
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Loading State */}
        {isAnalyzing && (
          <motion.div
            className="text-center py-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Brain className="w-8 h-8 text-white animate-pulse" />
            </div>
            <h3 className="text-lg font-bold text-gray-900 mb-2">Emma analizando tu contenido</h3>
            <p className="text-gray-600 mb-4">{loading.message}</p>
            
            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
              <motion.div
                className="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${loading.progress}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
            
            <p className="text-sm text-gray-500">{loading.progress}% completado</p>
          </motion.div>
        )}

        {/* Error State */}
        {error.hasError && (
          <motion.div
            className="p-4 bg-red-50 border border-red-200 rounded-xl"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-red-500" />
              <p className="text-red-700 text-sm">{error.message}</p>
            </div>
            <button
              onClick={clearError}
              className="mt-2 text-red-600 hover:text-red-700 text-sm underline"
            >
              Cerrar
            </button>
          </motion.div>
        )}

        {/* No Analysis State */}
        {!hasSAIOAnalysis && !isAnalyzing && !error.hasError && (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Brain className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Emma AI Rank</h3>
            <p className="text-gray-600 mb-4">
              Analiza tu contenido para optimizarlo para Google, ChatGPT y Perplexity
            </p>
            <p className="text-sm text-gray-500">
              Tecnología de IA avanzada para máximo rendimiento
            </p>
          </div>
        )}

        {/* Analysis Results */}
        {hasSAIOAnalysis && !isAnalyzing && (
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            {/* Main Score */}
            <div className={`p-6 rounded-xl border-2 ${getScoreBgColor(saioScore)}`}>
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Target className="w-6 h-6 text-gray-600" />
                  <span className="text-lg font-bold text-gray-900">Emma AI Score</span>
                </div>
                <div className={`text-4xl font-bold ${getScoreColor(saioScore)} mb-2`}>
                  {saioScore.toFixed(1)}
                </div>
                <div className="text-sm text-gray-600 mb-3">
                  Grado: <span className="font-medium">{saioGrade}</span>
                </div>
                <div className="text-sm text-gray-600">
                  Probabilidad de cita: <span className="font-medium">{citationProbability}</span>
                </div>
              </div>
            </div>

            {/* AI Platform Readiness */}
            <div className="grid grid-cols-3 gap-4">
              <div className={`p-4 rounded-xl border ${googleReady ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'}`}>
                <div className="text-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center mx-auto mb-2 ${googleReady ? 'bg-green-100' : 'bg-gray-100'}`}>
                    {googleReady ? <CheckCircle className="w-5 h-5 text-green-600" /> : <AlertTriangle className="w-5 h-5 text-gray-400" />}
                  </div>
                  <p className="text-sm font-medium text-gray-900">Google SGE</p>
                  <p className="text-xs text-gray-600">{googleReady ? 'Listo' : 'Necesita mejoras'}</p>
                </div>
              </div>

              <div className={`p-4 rounded-xl border ${chatgptReady ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'}`}>
                <div className="text-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center mx-auto mb-2 ${chatgptReady ? 'bg-green-100' : 'bg-gray-100'}`}>
                    {chatgptReady ? <CheckCircle className="w-5 h-5 text-green-600" /> : <AlertTriangle className="w-5 h-5 text-gray-400" />}
                  </div>
                  <p className="text-sm font-medium text-gray-900">ChatGPT</p>
                  <p className="text-xs text-gray-600">{chatgptReady ? 'Listo' : 'Necesita mejoras'}</p>
                </div>
              </div>

              <div className={`p-4 rounded-xl border ${perplexityReady ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'}`}>
                <div className="text-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center mx-auto mb-2 ${perplexityReady ? 'bg-green-100' : 'bg-gray-100'}`}>
                    {perplexityReady ? <CheckCircle className="w-5 h-5 text-green-600" /> : <AlertTriangle className="w-5 h-5 text-gray-400" />}
                  </div>
                  <p className="text-sm font-medium text-gray-900">Perplexity</p>
                  <p className="text-xs text-gray-600">{perplexityReady ? 'Listo' : 'Necesita mejoras'}</p>
                </div>
              </div>
            </div>

            {/* Component Scores */}
            {saioAnalysis?.component_scores && (
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">Factores de Ranking</h4>
                {Object.entries(saioAnalysis.component_scores).map(([key, score]) => {
                  const IconComponent = componentIcons[key as keyof typeof componentIcons];
                  const name = componentNames[key as keyof typeof componentNames];
                  
                  return (
                    <div key={key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <IconComponent className="w-4 h-4 text-gray-600" />
                        <span className="text-sm font-medium text-gray-900">{name}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${score >= 70 ? 'bg-green-500' : score >= 50 ? 'bg-yellow-500' : 'bg-red-500'}`}
                            style={{ width: `${score}%` }}
                          />
                        </div>
                        <span className={`text-sm font-medium ${getScoreColor(score)}`}>
                          {score.toFixed(0)}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* High Priority Recommendations */}
            {getHighPriorityRecommendations().length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900 flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Recomendaciones Prioritarias
                </h4>
                {getHighPriorityRecommendations().map((rec, index) => (
                  <div key={index} className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="w-5 h-5 text-orange-500 mt-0.5" />
                      <div className="flex-1">
                        <h5 className="font-medium text-gray-900 mb-1">{rec.title}</h5>
                        <p className="text-sm text-gray-700 mb-2">{rec.description}</p>
                        <p className="text-xs text-orange-700 font-medium">
                          Basado en: {rec.research_basis}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Research Badge */}
            <div className="text-center p-4 bg-purple-50 border border-purple-200 rounded-lg">
              <p className="text-sm text-purple-700">
                <span className="font-medium">🧠 Powered by Emma's Advanced AI</span><br />
                Tecnología de inteligencia artificial avanzada
              </p>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default EmmaAIRank;

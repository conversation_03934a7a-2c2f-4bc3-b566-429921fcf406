/**
 * SEO & GPT Optimizer™ - Suggestions Panel Component
 * Panel showing improvement suggestions and actionable tips
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Lightbulb, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Zap,
  ChevronRight,
  ChevronDown,
  Copy,
  ExternalLink
} from 'lucide-react';

import { ImprovementSuggestion, GPTRankAnalysis } from '../../../types/seo-gpt-optimizer';

interface SuggestionsPanelProps {
  analysis: GPTRankAnalysis | null;
  onApplySuggestion?: (suggestion: ImprovementSuggestion) => void;
  className?: string;
}

const priorityConfig = {
  high: {
    color: 'bg-red-100 text-red-700 border-red-200',
    icon: AlertTriangle,
    label: 'Alta Prioridad'
  },
  medium: {
    color: 'bg-yellow-100 text-yellow-700 border-yellow-200',
    icon: Clock,
    label: 'Prioridad Media'
  },
  low: {
    color: 'bg-green-100 text-green-700 border-green-200',
    icon: CheckCircle,
    label: 'Prioridad Baja'
  }
};

const impactConfig = {
  high: {
    color: 'text-red-600',
    label: 'Alto Impacto',
    icon: TrendingUp
  },
  medium: {
    color: 'text-yellow-600',
    label: 'Impacto Medio',
    icon: Zap
  },
  low: {
    color: 'text-green-600',
    label: 'Bajo Impacto',
    icon: CheckCircle
  }
};

const SuggestionsPanel: React.FC<SuggestionsPanelProps> = ({
  analysis,
  onApplySuggestion,
  className = ''
}) => {
  const [expandedSuggestion, setExpandedSuggestion] = useState<number | null>(null);
  const [filter, setFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all');

  const toggleSuggestion = (index: number) => {
    setExpandedSuggestion(expandedSuggestion === index ? null : index);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  if (!analysis || !analysis.improvement_suggestions.length) {
    return (
      <div className={`bg-white rounded-2xl shadow-sm border border-gray-100 p-6 ${className}`}>
        <div className="text-center">
          <Lightbulb className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="font-semibold text-gray-900 mb-2">Sugerencias de Mejora</h3>
          <p className="text-sm text-gray-600">
            Las sugerencias aparecerán aquí después del análisis
          </p>
        </div>
      </div>
    );
  }

  const filteredSuggestions = analysis.improvement_suggestions.filter(suggestion => 
    filter === 'all' || suggestion.priority === filter
  );

  const highPrioritySuggestions = analysis.improvement_suggestions.filter(s => s.priority === 'high');
  const quickWins = analysis.improvement_suggestions.filter(s => s.impact === 'high' && s.current_score < 70);

  return (
    <motion.div
      className={`bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center">
            <Lightbulb className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-gray-900">Sugerencias de Mejora</h2>
            <p className="text-sm text-gray-600">
              {analysis.improvement_suggestions.length} sugerencias encontradas
            </p>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-3">
          <div className="text-center p-3 bg-red-50 rounded-xl">
            <div className="text-lg font-bold text-red-600">{highPrioritySuggestions.length}</div>
            <div className="text-xs text-red-600">Alta Prioridad</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-xl">
            <div className="text-lg font-bold text-green-600">{quickWins.length}</div>
            <div className="text-xs text-green-600">Quick Wins</div>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-xl">
            <div className="text-lg font-bold text-blue-600">
              +{analysis.improvement_suggestions.reduce((acc, s) => acc + (100 - s.current_score) * 0.1, 0).toFixed(0)}
            </div>
            <div className="text-xs text-blue-600">Potencial</div>
          </div>
        </div>

        {/* Filter Buttons */}
        <div className="flex gap-2 mt-4">
          {(['all', 'high', 'medium', 'low'] as const).map((filterOption) => (
            <button
              key={filterOption}
              onClick={() => setFilter(filterOption)}
              className={`px-3 py-1 rounded-lg text-xs font-medium transition-colors duration-200 ${
                filter === filterOption
                  ? 'bg-purple-100 text-purple-700'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {filterOption === 'all' ? 'Todas' : 
               filterOption === 'high' ? 'Alta' :
               filterOption === 'medium' ? 'Media' : 'Baja'}
            </button>
          ))}
        </div>
      </div>

      {/* Suggestions List */}
      <div className="max-h-96 overflow-y-auto">
        {filteredSuggestions.length === 0 ? (
          <div className="p-6 text-center">
            <p className="text-sm text-gray-600">
              No hay sugerencias para el filtro seleccionado
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {filteredSuggestions.map((suggestion, index) => {
              const isExpanded = expandedSuggestion === index;
              const priorityInfo = priorityConfig[suggestion.priority];
              const impactInfo = impactConfig[suggestion.impact];
              const PriorityIcon = priorityInfo.icon;
              const ImpactIcon = impactInfo.icon;

              return (
                <div key={index} className="p-4">
                  <button
                    onClick={() => toggleSuggestion(index)}
                    className="w-full text-left"
                  >
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-gray-900">{suggestion.title}</h3>
                          <div className={`px-2 py-1 rounded-full text-xs font-medium border ${priorityInfo.color}`}>
                            <PriorityIcon className="w-3 h-3 inline mr-1" />
                            {priorityInfo.label}
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-2">
                          {suggestion.description}
                        </p>
                        
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>Score actual: {suggestion.current_score.toFixed(1)}</span>
                          <span className={impactInfo.color}>
                            <ImpactIcon className="w-3 h-3 inline mr-1" />
                            {impactInfo.label}
                          </span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <div className="text-right">
                          <div className="text-lg font-bold text-gray-900">
                            {suggestion.current_score.toFixed(0)}
                          </div>
                          <div className="text-xs text-gray-500">puntos</div>
                        </div>
                        {isExpanded ? 
                          <ChevronDown className="w-4 h-4 text-gray-400" /> : 
                          <ChevronRight className="w-4 h-4 text-gray-400" />
                        }
                      </div>
                    </div>
                  </button>

                  <AnimatePresence>
                    {isExpanded && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="mt-4 pt-4 border-t border-gray-100"
                      >
                        {/* Actions */}
                        <div className="mb-4">
                          <h4 className="font-medium text-gray-900 mb-2">Acciones recomendadas:</h4>
                          <div className="space-y-2">
                            {suggestion.actions.map((action, actionIndex) => (
                              <div key={actionIndex} className="flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                                <span className="text-sm text-gray-700">{action}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Examples */}
                        {suggestion.examples && suggestion.examples.length > 0 && (
                          <div className="mb-4">
                            <h4 className="font-medium text-gray-900 mb-2">Ejemplos:</h4>
                            <div className="space-y-2">
                              {suggestion.examples.map((example, exampleIndex) => (
                                <div key={exampleIndex} className="p-3 bg-gray-50 rounded-lg">
                                  <div className="flex items-start justify-between gap-2">
                                    <span className="text-sm text-gray-700 font-mono">{example}</span>
                                    <button
                                      onClick={() => copyToClipboard(example)}
                                      className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                                      title="Copiar ejemplo"
                                    >
                                      <Copy className="w-3 h-3" />
                                    </button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Action Buttons */}
                        <div className="flex gap-2">
                          {onApplySuggestion && (
                            <button
                              onClick={() => onApplySuggestion(suggestion)}
                              className="flex items-center gap-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-purple-700 hover:to-blue-700 transition-all duration-200"
                            >
                              <CheckCircle className="w-4 h-4" />
                              Aplicar Sugerencia
                            </button>
                          )}
                          
                          <button
                            onClick={() => copyToClipboard(suggestion.actions.join('\n'))}
                            className="flex items-center gap-2 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 transition-all duration-200"
                          >
                            <Copy className="w-4 h-4" />
                            Copiar Acciones
                          </button>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Footer with Quick Tips */}
      <div className="p-4 border-t border-gray-100 bg-gray-50">
        <div className="text-xs text-gray-600">
          💡 <strong>Tip:</strong> Enfócate primero en las sugerencias de alta prioridad para obtener el mayor impacto en tu GPT Rank Score.
        </div>
      </div>
    </motion.div>
  );
};

export default SuggestionsPanel;

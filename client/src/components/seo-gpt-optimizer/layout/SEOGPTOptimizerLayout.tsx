/**
 * SEO & GPT Optimizer™ - Main Layout Component
 * Primary layout wrapper with navigation and global features
 */

import React, { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { AnimatePresence } from 'framer-motion';
import { Toaster } from 'react-hot-toast';

import MainNavigation from '../navigation/MainNavigation';
import GlobalSearch from '../navigation/GlobalSearch';
import ErrorBoundary from '../shared/ErrorBoundary';

interface SEOGPTOptimizerLayoutProps {
  className?: string;
  children?: React.ReactNode;
}

const SEOGPTOptimizerLayout: React.FC<SEOGPTOptimizerLayoutProps> = ({
  className = '',
  children
}) => {
  const [location] = useLocation();
  const [showGlobalSearch, setShowGlobalSearch] = useState(false);

  // Handle global keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + K for global search
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setShowGlobalSearch(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Close search on route change
  useEffect(() => {
    setShowGlobalSearch(false);
  }, [location]);

  return (
    <ErrorBoundary>
      <div className={`min-h-screen bg-gray-50 ${className}`}>
        {/* Main Navigation */}
        <MainNavigation />

        {/* Main Content */}
        <main className="relative">
          {children}
        </main>

        {/* Global Search */}
        <AnimatePresence>
          {showGlobalSearch && (
            <GlobalSearch
              isOpen={showGlobalSearch}
              onClose={() => setShowGlobalSearch(false)}
            />
          )}
        </AnimatePresence>

        {/* Toast Notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#fff',
              color: '#374151',
              border: '1px solid #e5e7eb',
              borderRadius: '12px',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
              padding: '16px',
              fontSize: '14px',
              fontWeight: '500'
            },
            success: {
              iconTheme: {
                primary: '#10b981',
                secondary: '#fff'
              }
            },
            error: {
              iconTheme: {
                primary: '#ef4444',
                secondary: '#fff'
              }
            },
            loading: {
              iconTheme: {
                primary: '#3b82f6',
                secondary: '#fff'
              }
            }
          }}
        />

        {/* Global Keyboard Shortcuts Hint */}
        <div className="fixed bottom-4 right-4 z-40">
          <div className="bg-white/80 backdrop-blur-sm border border-gray-200 rounded-lg px-3 py-2 text-xs text-gray-600 shadow-sm">
            <kbd className="bg-gray-100 px-1.5 py-0.5 rounded text-xs font-mono">⌘K</kbd>
            <span className="ml-2">Buscar</span>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default SEOGPTOptimizerLayout;

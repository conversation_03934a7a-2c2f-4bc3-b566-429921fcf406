/**
 * SEO & GPT Optimizer™ - Global Search Component
 * Universal search across projects, content, and features
 */

import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useLocation } from 'wouter';
import { 
  Search, 
  FileText, 
  FolderOpen, 
  BarChart3, 
  Clock,
  TrendingUp,
  Target,
  ArrowRight,
  X
} from 'lucide-react';

import { useProjects } from '../../../hooks/seo-gpt-optimizer/useProjects';
import { SEOGPTProject } from '../../../types/seo-gpt-optimizer';

interface SearchResult {
  id: string;
  type: 'project' | 'feature' | 'action';
  title: string;
  description: string;
  path: string;
  icon: React.ComponentType<{ className?: string }>;
  metadata?: {
    score?: number;
    status?: string;
    lastUpdated?: string;
  };
}

interface GlobalSearchProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

const features: Omit<SearchResult, 'id'>[] = [
  {
    type: 'feature',
    title: 'Research Engine',
    description: 'Investiga temas y encuentra oportunidades de contenido',
    path: '/dashboard/herramientas/seo-gpt-optimizer/research',
    icon: Search
  },
  {
    type: 'feature',
    title: 'Content Builder',
    description: 'Editor con análisis GPT Rank en tiempo real',
    path: '/dashboard/herramientas/seo-gpt-optimizer/content-builder',
    icon: FileText
  },
  {
    type: 'feature',
    title: 'Analytics Dashboard',
    description: 'Métricas y análisis competitivo detallado',
    path: '/dashboard/herramientas/seo-gpt-optimizer/analytics',
    icon: BarChart3
  },
  {
    type: 'feature',
    title: 'Gestión de Proyectos',
    description: 'Administra todos tus proyectos de optimización',
    path: '/dashboard/herramientas/seo-gpt-optimizer/projects',
    icon: FolderOpen
  }
];

const quickActions: Omit<SearchResult, 'id'>[] = [
  {
    type: 'action',
    title: 'Nuevo Proyecto',
    description: 'Crear un nuevo proyecto de optimización',
    path: '/dashboard/herramientas/seo-gpt-optimizer/projects/new',
    icon: Target
  },
  {
    type: 'action',
    title: 'Investigar Tema',
    description: 'Iniciar una nueva investigación de contenido',
    path: '/dashboard/herramientas/seo-gpt-optimizer/research',
    icon: Search
  },
  {
    type: 'action',
    title: 'Ver Analytics',
    description: 'Revisar métricas y rendimiento general',
    path: '/dashboard/herramientas/seo-gpt-optimizer/analytics',
    icon: TrendingUp
  }
];

const GlobalSearch: React.FC<GlobalSearchProps> = ({
  isOpen,
  onClose,
  className = ''
}) => {
  const [, setLocation] = useLocation();
  const { projects } = useProjects();
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);

  // Generate search results
  const searchResults = useMemo(() => {
    if (!query.trim()) {
      // Show recent projects and quick actions when no query
      const recentProjects = projects
        .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
        .slice(0, 3)
        .map((project): SearchResult => ({
          id: project.project_id,
          type: 'project',
          title: project.title,
          description: project.topic,
          path: `/dashboard/herramientas/seo-gpt-optimizer/content-builder/${project.project_id}`,
          icon: FileText,
          metadata: {
            score: project.current_gpt_rank_score,
            status: project.status,
            lastUpdated: project.updated_at
          }
        }));

      return [
        ...quickActions.map((action, index) => ({ ...action, id: `action-${index}` })),
        ...recentProjects
      ];
    }

    const searchTerm = query.toLowerCase();
    const results: SearchResult[] = [];

    // Search projects
    const matchingProjects = projects
      .filter(project => 
        project.title.toLowerCase().includes(searchTerm) ||
        project.topic.toLowerCase().includes(searchTerm)
      )
      .map((project): SearchResult => ({
        id: project.project_id,
        type: 'project',
        title: project.title,
        description: project.topic,
        path: `/dashboard/herramientas/seo-gpt-optimizer/content-builder/${project.project_id}`,
        icon: FileText,
        metadata: {
          score: project.current_gpt_rank_score,
          status: project.status,
          lastUpdated: project.updated_at
        }
      }));

    // Search features
    const matchingFeatures = features
      .filter(feature =>
        feature.title.toLowerCase().includes(searchTerm) ||
        feature.description.toLowerCase().includes(searchTerm)
      )
      .map((feature, index): SearchResult => ({ ...feature, id: `feature-${index}` }));

    // Search actions
    const matchingActions = quickActions
      .filter(action =>
        action.title.toLowerCase().includes(searchTerm) ||
        action.description.toLowerCase().includes(searchTerm)
      )
      .map((action, index): SearchResult => ({ ...action, id: `action-${index}` }));

    results.push(...matchingProjects, ...matchingFeatures, ...matchingActions);
    return results;
  }, [query, projects]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < searchResults.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : searchResults.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (searchResults[selectedIndex]) {
            handleResultClick(searchResults[selectedIndex]);
          }
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, searchResults, selectedIndex, onClose]);

  // Reset selection when results change
  useEffect(() => {
    setSelectedIndex(0);
  }, [searchResults]);

  // Reset state when opening/closing
  useEffect(() => {
    if (isOpen) {
      setQuery('');
      setSelectedIndex(0);
    }
  }, [isOpen]);

  const handleResultClick = (result: SearchResult) => {
    setLocation(result.path);
    onClose();
  };

  const getResultIcon = (result: SearchResult) => {
    const Icon = result.icon;
    const colors = {
      project: 'bg-blue-100 text-blue-600',
      feature: 'bg-purple-100 text-purple-600',
      action: 'bg-green-100 text-green-600'
    };
    
    return (
      <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${colors[result.type]}`}>
        <Icon className="w-5 h-5" />
      </div>
    );
  };

  const formatLastUpdated = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Hace unos minutos';
    if (diffInHours < 24) return `Hace ${diffInHours}h`;
    if (diffInHours < 48) return 'Ayer';
    return date.toLocaleDateString();
  };

  if (!isOpen) return null;

  return (
    <motion.div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center pt-20"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        className={`bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 overflow-hidden ${className}`}
        initial={{ scale: 0.9, opacity: 0, y: -20 }}
        animate={{ scale: 1, opacity: 1, y: 0 }}
        exit={{ scale: 0.9, opacity: 0, y: -20 }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Search Input */}
        <div className="p-6 border-b border-gray-100">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Buscar proyectos, funciones o acciones..."
              className="w-full pl-12 pr-12 py-4 text-lg border-none outline-none bg-white text-gray-900 placeholder-gray-500 rounded-xl focus:ring-2 focus:ring-blue-500 transition-all duration-200"
              autoFocus
            />
            <button
              onClick={onClose}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Search Results */}
        <div className="max-h-96 overflow-y-auto">
          {searchResults.length === 0 ? (
            <div className="p-8 text-center">
              <Search className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {query ? 'No se encontraron resultados' : 'Comienza a escribir para buscar'}
              </h3>
              <p className="text-gray-600">
                {query 
                  ? 'Intenta con otros términos de búsqueda'
                  : 'Busca proyectos, funciones o acciones rápidas'
                }
              </p>
            </div>
          ) : (
            <div className="py-2">
              {!query && (
                <div className="px-6 py-2">
                  <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide">
                    Acciones Rápidas
                  </h3>
                </div>
              )}
              
              {searchResults.map((result, index) => (
                <motion.button
                  key={result.id}
                  onClick={() => handleResultClick(result)}
                  className={`w-full flex items-center gap-4 px-6 py-4 text-left hover:bg-gray-50 transition-colors duration-200 ${
                    index === selectedIndex ? 'bg-blue-50 border-r-2 border-blue-500' : ''
                  }`}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  {getResultIcon(result)}
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold text-gray-900 truncate">{result.title}</h4>
                      {result.type === 'project' && result.metadata?.score && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium">
                          {result.metadata.score.toFixed(1)}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 truncate">{result.description}</p>
                    
                    {result.metadata && (
                      <div className="flex items-center gap-3 mt-2 text-xs text-gray-500">
                        {result.metadata.status && (
                          <span className="capitalize">{result.metadata.status}</span>
                        )}
                        {result.metadata.lastUpdated && (
                          <span className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {formatLastUpdated(result.metadata.lastUpdated)}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                  
                  <ArrowRight className="w-4 h-4 text-gray-400" />
                </motion.button>
              ))}
              
              {!query && projects.length > 3 && (
                <div className="px-6 py-4 border-t border-gray-100">
                  <button
                    onClick={() => {
                      setLocation('/dashboard/herramientas/seo-gpt-optimizer/projects');
                      onClose();
                    }}
                    className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                  >
                    Ver todos los proyectos →
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-3 bg-gray-50 border-t border-gray-100">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center gap-4">
              <span>↑↓ Navegar</span>
              <span>↵ Seleccionar</span>
              <span>Esc Cerrar</span>
            </div>
            <span>{searchResults.length} resultados</span>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default GlobalSearch;

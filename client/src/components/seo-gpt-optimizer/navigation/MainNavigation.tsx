/**
 * SEO & GPT Optimizer™ - Main Navigation Component
 * Primary navigation menu with breadcrumbs and search
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useLocation, Link } from 'wouter';
import {
  Home,
  Search,
  PenTool,
  BarChart3,
  FolderOpen,
  Menu,
  X,
  ChevronRight,
  Zap,
  Target,
  TrendingUp,
  BookOpen
} from 'lucide-react';

interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  color: string;
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    path: '/dashboard/herramientas/seo-gpt-optimizer',
    icon: Home,
    description: 'Vista general y estadísticas',
    color: 'from-blue-500 to-blue-600'
  },
  {
    id: 'research',
    label: 'Research Engine',
    path: '/dashboard/herramientas/seo-gpt-optimizer/research',
    icon: Search,
    description: 'Investigación de temas',
    color: 'from-green-500 to-green-600'
  },
  {
    id: 'content-builder',
    label: 'Content Builder',
    path: '/dashboard/herramientas/seo-gpt-optimizer/content-builder',
    icon: PenTool,
    description: 'Editor con análisis en tiempo real',
    color: 'from-purple-500 to-purple-600'
  },
  {
    id: 'analytics',
    label: 'Analytics',
    path: '/dashboard/herramientas/seo-gpt-optimizer/analytics',
    icon: BarChart3,
    description: 'Métricas y análisis competitivo',
    color: 'from-orange-500 to-orange-600'
  },
  {
    id: 'projects',
    label: 'Proyectos',
    path: '/dashboard/herramientas/seo-gpt-optimizer/projects',
    icon: FolderOpen,
    description: 'Gestión de proyectos',
    color: 'from-indigo-500 to-indigo-600'
  }
];

interface MainNavigationProps {
  className?: string;
}

const MainNavigation: React.FC<MainNavigationProps> = ({ className = '' }) => {
  const [location, setLocation] = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);

  // Get current active item
  const activeItem = navigationItems.find(item =>
    location.startsWith(item.path)
  ) || navigationItems[0];

  // Generate breadcrumbs
  const generateBreadcrumbs = () => {
    const pathSegments = location.split('/').filter(Boolean);
    const breadcrumbs = [];

    // Always start with Dashboard
    breadcrumbs.push({
      label: 'Dashboard',
      path: '/dashboard',
      isActive: false
    });

    // Add SEO GPT Optimizer
    if (pathSegments.includes('seo-gpt-optimizer')) {
      breadcrumbs.push({
        label: 'SEO & GPT Optimizer™',
        path: '/dashboard/herramientas/seo-gpt-optimizer',
        isActive: pathSegments.length === 3
      });

      // Add specific page
      if (pathSegments.length > 3) {
        const currentPage = pathSegments[3];
        const navItem = navigationItems.find(item => item.path.includes(currentPage));

        if (navItem) {
          breadcrumbs.push({
            label: navItem.label,
            path: navItem.path,
            isActive: true
          });
        }
      }
    }

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  // Handle search
  const handleSearch = (query: string) => {
    if (!query.trim()) return;

    // Simple search logic - could be enhanced
    const searchableItems = navigationItems.filter(item =>
      item.label.toLowerCase().includes(query.toLowerCase()) ||
      item.description.toLowerCase().includes(query.toLowerCase())
    );

    if (searchableItems.length > 0) {
      setLocation(searchableItems[0].path);
      setSearchQuery('');
      setShowSearch(false);
    }
  };

  // Close mobile menu on route change
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location]);

  // Close search on escape
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setShowSearch(false);
        setSearchQuery('');
      }
    };

    if (showSearch) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [showSearch]);

  return (
    <div className={`bg-white border-b border-gray-200 sticky top-0 z-50 ${className}`}>
      {/* Main Navigation Bar */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Title */}
          <div className="flex items-center gap-4">
            <Link
              to="/dashboard/herramientas/seo-gpt-optimizer"
              className="flex items-center gap-3 hover:opacity-80 transition-opacity duration-200"
            >
              <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <Target className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  SEO & GPT Optimizer™
                </h1>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeItem.id === item.id;
              
              return (
                <Link
                  key={item.id}
                  to={item.path}
                  className={`relative px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 ${
                    isActive
                      ? 'text-blue-600 bg-blue-50'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Icon className="w-4 h-4" />
                    <span>{item.label}</span>
                  </div>
                  
                  {isActive && (
                    <motion.div
                      className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full"
                      layoutId="activeTab"
                      transition={{ type: "spring", duration: 0.5 }}
                    />
                  )}
                </Link>
              );
            })}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-3">
            {/* Search Toggle */}
            <button
              onClick={() => setShowSearch(!showSearch)}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              title="Buscar"
            >
              <Search className="w-4 h-4" />
            </button>

            {/* Quick Actions */}
            <div className="hidden sm:flex items-center gap-2">
              <Link
                to="/dashboard/herramientas/seo-gpt-optimizer/saved-research"
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200"
                title="Ver investigaciones guardadas"
              >
                <BookOpen className="w-4 h-4" />
                Guardadas
              </Link>
              <Link
                to="/dashboard/herramientas/seo-gpt-optimizer/projects"
                className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
              >
                <Zap className="w-4 h-4" />
                Nuevo Proyecto
              </Link>
            </div>

            {/* Mobile Menu Toggle */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200"
            >
              {isMobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
          </div>
        </div>

        {/* Breadcrumbs */}
        {breadcrumbs.length > 1 && (
          <div className="flex items-center gap-2 pb-4 text-sm text-gray-600">
            {breadcrumbs.map((crumb, index) => (
              <React.Fragment key={crumb.path}>
                {index > 0 && <ChevronRight className="w-4 h-4 text-gray-400" />}
                {crumb.isActive ? (
                  <span className="font-medium text-gray-900">{crumb.label}</span>
                ) : (
                  <Link
                    to={crumb.path}
                    className="hover:text-gray-900 transition-colors duration-200"
                  >
                    {crumb.label}
                  </Link>
                )}
              </React.Fragment>
            ))}
          </div>
        )}
      </div>

      {/* Search Overlay */}
      <AnimatePresence>
        {showSearch && (
          <motion.div
            className="absolute top-full left-0 right-0 bg-white border-b border-gray-200 shadow-lg"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSearch(searchQuery);
                    }
                  }}
                  placeholder="Buscar en SEO & GPT Optimizer™..."
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  autoFocus
                />
              </div>
              
              {/* Quick Search Results */}
              {searchQuery && (
                <div className="mt-4 space-y-2">
                  {navigationItems
                    .filter(item =>
                      item.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
                      item.description.toLowerCase().includes(searchQuery.toLowerCase())
                    )
                    .map((item) => {
                      const Icon = item.icon;
                      return (
                        <button
                          key={item.id}
                          onClick={() => {
                            setLocation(item.path);
                            setSearchQuery('');
                            setShowSearch(false);
                          }}
                          className="w-full flex items-center gap-3 p-3 text-left hover:bg-gray-50 rounded-lg transition-colors duration-200"
                        >
                          <div className={`w-8 h-8 bg-gradient-to-br ${item.color} rounded-lg flex items-center justify-center`}>
                            <Icon className="w-4 h-4 text-white" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{item.label}</div>
                            <div className="text-sm text-gray-600">{item.description}</div>
                          </div>
                        </button>
                      );
                    })}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            className="md:hidden bg-white border-b border-gray-200"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="px-4 py-4 space-y-2">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                const isActive = activeItem.id === item.id;
                
                return (
                  <Link
                    key={item.id}
                    to={item.path}
                    className={`flex items-center gap-3 p-3 rounded-xl transition-colors duration-200 ${
                      isActive
                        ? 'bg-blue-50 text-blue-600'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    <div className={`w-8 h-8 bg-gradient-to-br ${item.color} rounded-lg flex items-center justify-center`}>
                      <Icon className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <div className="font-medium">{item.label}</div>
                      <div className="text-sm opacity-75">{item.description}</div>
                    </div>
                  </Link>
                );
              })}

              {/* Mobile Quick Actions */}
              <div className="mt-4 space-y-2">
                <Link
                  to="/dashboard/herramientas/seo-gpt-optimizer/saved-research"
                  className="flex items-center gap-3 p-3 bg-gray-100 text-gray-700 rounded-xl font-medium"
                >
                  <BookOpen className="w-5 h-5" />
                  Investigaciones Guardadas
                </Link>
                <Link
                  to="/dashboard/herramientas/seo-gpt-optimizer/projects"
                  className="flex items-center gap-3 p-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-medium"
                >
                  <Zap className="w-5 h-5" />
                  Nuevo Proyecto
                </Link>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default MainNavigation;

/* SEO GPT Optimizer Project Form Modal - Override Styles */

/* Modal overlay with maximum priority */
.modalOverlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(4px) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 20px !important;
  z-index: 9999 !important;
  pointer-events: auto !important;
  overflow: auto !important;
}

/* Modal container */
.modalContainer {
  background-color: #ffffff !important;
  border-radius: 16px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  max-width: 672px !important;
  width: 100% !important;
  max-height: 80vh !important;
  height: auto !important;
  overflow: visible !important;
  position: relative !important;
  z-index: 10000 !important;
  border: 1px solid #e5e7eb !important;
  display: flex !important;
  flex-direction: column !important;
  margin: auto !important;
}

/* Header */
.modalHeader {
  padding: 24px !important;
  border-bottom: 1px solid #e5e7eb !important;
  background-color: #ffffff !important;
  z-index: 10001 !important;
}

.headerContent {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

.headerLeft {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
}

.headerIcon {
  width: 40px !important;
  height: 40px !important;
  background: linear-gradient(to bottom right, #3b82f6, #8b5cf6) !important;
  border-radius: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.headerTitle {
  color: #111827 !important;
  font-size: 1.25rem !important;
  font-weight: 700 !important;
  margin: 0 !important;
  line-height: 1.2 !important;
}

.headerSubtitle {
  color: #6b7280 !important;
  font-size: 0.875rem !important;
  margin: 0 !important;
  line-height: 1.2 !important;
}

.closeButton {
  background-color: transparent !important;
  color: #9ca3af !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 8px !important;
  cursor: pointer !important;
  z-index: 10002 !important;
  transition: all 0.2s ease !important;
}

.closeButton:hover {
  background-color: #f3f4f6 !important;
  color: #6b7280 !important;
}

/* Form content */
.formContent {
  padding: 24px !important;
  flex: 1 !important;
  overflow-y: auto !important;
  background-color: #ffffff !important;
  min-height: 0 !important;
}

/* Form fields */
.fieldGroup {
  margin-bottom: 24px !important;
}

.fieldLabel {
  display: block !important;
  color: #374151 !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  margin-bottom: 8px !important;
  z-index: 10001 !important;
}

.fieldInput {
  width: 100% !important;
  padding: 12px 16px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 12px !important;
  background-color: #ffffff !important;
  color: #111827 !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
  z-index: 10001 !important;
}

.fieldInput:focus {
  outline: none !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.fieldInput.error {
  border-color: #fca5a5 !important;
  background-color: #fef2f2 !important;
}

.fieldTextarea {
  width: 100% !important;
  padding: 12px 16px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 12px !important;
  background-color: #ffffff !important;
  color: #111827 !important;
  font-size: 0.875rem !important;
  resize: none !important;
  transition: all 0.2s ease !important;
  z-index: 10001 !important;
}

.fieldTextarea:focus {
  outline: none !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.fieldSelect {
  width: 100% !important;
  padding: 12px 16px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 12px !important;
  background-color: #ffffff !important;
  color: #111827 !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
  z-index: 10001 !important;
}

.fieldSelect:focus {
  outline: none !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Option buttons */
.optionGrid {
  display: grid !important;
  grid-template-columns: repeat(2, 1fr) !important;
  gap: 12px !important;
}

.optionButton {
  padding: 16px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 12px !important;
  background-color: #ffffff !important;
  color: #374151 !important;
  text-align: left !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  z-index: 10001 !important;
}

.optionButton:hover {
  border-color: #9ca3af !important;
  background-color: #f9fafb !important;
}

.optionButton.selected {
  border-color: #3b82f6 !important;
  background-color: #eff6ff !important;
  color: #1d4ed8 !important;
}

.optionTitle {
  font-weight: 500 !important;
  color: inherit !important;
  margin: 0 !important;
  line-height: 1.2 !important;
}

.optionDescription {
  font-size: 0.875rem !important;
  color: #6b7280 !important;
  margin: 4px 0 0 0 !important;
  line-height: 1.2 !important;
}

/* Score options */
.scoreOptionsList {
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important;
}

.scoreOption {
  width: 100% !important;
  padding: 16px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 12px !important;
  background-color: #ffffff !important;
  color: #374151 !important;
  text-align: left !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  z-index: 10001 !important;
}

.scoreOption:hover {
  border-color: #9ca3af !important;
  background-color: #f9fafb !important;
}

.scoreOption.selected {
  border-color: #3b82f6 !important;
  background-color: #eff6ff !important;
  color: #1d4ed8 !important;
}

.scoreOptionContent {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

/* Footer */
.modalFooter {
  padding: 24px !important;
  border-top: 1px solid #e5e7eb !important;
  background-color: #f9fafb !important;
  z-index: 10001 !important;
  flex-shrink: 0 !important;
  position: relative !important;
  bottom: 0 !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.footerContent {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  z-index: 10002 !important;
  position: relative !important;
}

.footerNote {
  color: #6b7280 !important;
  font-size: 0.875rem !important;
}

.footerButtons {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  z-index: 10002 !important;
  position: relative !important;
}

.cancelButton {
  padding: 12px 24px !important;
  color: #374151 !important;
  background-color: #ffffff !important;
  border: 1px solid #d1d5db !important;
  border-radius: 12px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  z-index: 10002 !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  position: relative !important;
  min-height: 44px !important;
}

.cancelButton:hover {
  background-color: #f3f4f6 !important;
  border-color: #9ca3af !important;
}

.submitButton {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
  padding: 12px 24px !important;
  background: linear-gradient(to right, #2563eb, #9333ea) !important;
  color: #ffffff !important;
  border: none !important;
  border-radius: 12px !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  z-index: 10002 !important;
  position: relative !important;
  min-height: 44px !important;
}

.submitButton:hover {
  background: linear-gradient(to right, #1d4ed8, #7c3aed) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3) !important;
}

.submitButton:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Error messages */
.errorMessage {
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
  margin-top: 8px !important;
  color: #dc2626 !important;
  font-size: 0.875rem !important;
}

/* Icons */
.icon {
  color: inherit !important;
  fill: currentColor !important;
}

.iconWhite {
  color: #ffffff !important;
  fill: #ffffff !important;
}

/* Force visibility for buttons */
.forceVisible {
  visibility: visible !important;
  opacity: 1 !important;
  display: flex !important;
  pointer-events: auto !important;
  z-index: 10003 !important;
  position: relative !important;
}

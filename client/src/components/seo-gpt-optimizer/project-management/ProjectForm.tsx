/**
 * SEO & GPT Optimizer™ - Project Form Component
 * Form for creating and editing projects
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Save,
  X,
  FileText,
  Type,
  Lightbulb,
  AlertCircle,
  Loader
} from 'lucide-react';

import {
  SEOGPTProject,
  ProjectCreateRequest,
  ContentType
} from '../../../types/seo-gpt-optimizer';

import styles from './ProjectForm.module.css';

interface ProjectFormProps {
  project?: SEOGPTProject;
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: ProjectCreateRequest | Partial<SEOGPTProject>) => Promise<void>;
  loading?: boolean;
  className?: string;
}



const ProjectForm: React.FC<ProjectFormProps> = ({
  project,
  isOpen,
  onClose,
  onSave,
  loading = false,
  className = ''
}) => {
  const [formData, setFormData] = useState({
    title: '',
    topic: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEditing = !!project;

  // Initialize form data
  useEffect(() => {
    if (project) {
      setFormData({
        title: project.title,
        topic: project.topic
      });
    } else {
      setFormData({
        title: '',
        topic: ''
      });
    }
    setErrors({});
  }, [project, isOpen]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'El título es requerido';
    } else if (formData.title.length < 3) {
      newErrors.title = 'Mínimo 3 caracteres';
    }

    if (!formData.topic.trim()) {
      newErrors.topic = 'El tema es requerido';
    } else if (formData.topic.length < 5) {
      newErrors.topic = 'Mínimo 5 caracteres';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      setIsSubmitting(true);
      // Agregar valores por defecto para campos eliminados
      const projectData = {
        ...formData,
        target_language: 'es', // Español por defecto
        content_type: ContentType.ARTICLE, // Artículo por defecto
        target_gpt_rank_score: 95 // Máximo ranking por defecto
      };
      await onSave(projectData);
      onClose();
    } catch (error) {
      console.error('Error saving project:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <motion.div
      className={`${styles.modalOverlay}`}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        backdropFilter: 'blur(4px)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
        zIndex: 9999,
        overflow: 'auto'
      }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className={`${styles.modalContainer} ${className}`}
        style={{
          backgroundColor: '#ffffff',
          borderRadius: '16px',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          maxWidth: '672px',
          width: '100%',
          maxHeight: '80vh',
          overflow: 'visible',
          position: 'relative',
          zIndex: 10000,
          border: '1px solid #e5e7eb',
          display: 'flex',
          flexDirection: 'column'
        }}
        initial={{ scale: 0.9, opacity: 0, y: 20 }}
        animate={{ scale: 1, opacity: 1, y: 0 }}
        exit={{ scale: 0.9, opacity: 0, y: 20 }}
        transition={{ type: "spring", duration: 0.5 }}
      >
        {/* Header */}
        <div className={styles.modalHeader}>
          <div className={styles.headerContent}>
            <div className={styles.headerLeft}>
              <div className={styles.headerIcon}>
                <FileText className={`w-5 h-5 ${styles.iconWhite}`} />
              </div>
              <div>
                <h2 className={styles.headerTitle}>
                  {isEditing ? 'Editar Proyecto' : 'Nuevo Proyecto'}
                </h2>
                <p className={styles.headerSubtitle}>
                  {isEditing ? 'Modifica los detalles del proyecto' : 'Optimiza tu contenido para motores de búsqueda y LLMs'}
                </p>
              </div>
            </div>

            <button
              onClick={handleClose}
              disabled={isSubmitting}
              className={styles.closeButton}
            >
              <X className={`w-5 h-5 ${styles.icon}`} />
            </button>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className={styles.formContent}>
          {/* Title */}
          <div className={styles.fieldGroup}>
            <label className={styles.fieldLabel}>
              <Type className={`w-4 h-4 inline mr-1 ${styles.icon}`} />
              Título del Proyecto *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="Ej: Guía completa sobre marketing digital"
              className={`${styles.fieldInput} ${errors.title ? styles.error : ''}`}
              disabled={isSubmitting}
            />
            {errors.title && (
              <div className={styles.errorMessage}>
                <AlertCircle className={`w-4 h-4 ${styles.icon}`} />
                {errors.title}
              </div>
            )}
          </div>

          {/* Topic */}
          <div className={styles.fieldGroup}>
            <label className={styles.fieldLabel}>
              <Lightbulb className={`w-4 h-4 inline mr-1 ${styles.icon}`} />
              Tema Principal *
            </label>
            <textarea
              value={formData.topic}
              onChange={(e) => handleInputChange('topic', e.target.value)}
              placeholder="Ej: Estrategias de marketing digital para pequeñas empresas, mejores prácticas de SEO, guía completa de redes sociales..."
              rows={3}
              className={`${styles.fieldTextarea} ${errors.topic ? styles.error : ''}`}
              disabled={isSubmitting}
            />
            {errors.topic && (
              <div className={styles.errorMessage}>
                <AlertCircle className={`w-4 h-4 ${styles.icon}`} />
                {errors.topic}
              </div>
            )}
          </div>




        </form>

        {/* Footer */}
        <div
          className={styles.modalFooter}
          style={{
            padding: '24px',
            borderTop: '1px solid #e5e7eb',
            backgroundColor: '#f9fafb',
            zIndex: 10001,
            flexShrink: 0,
            position: 'relative',
            bottom: 0,
            width: '100%',
            boxSizing: 'border-box'
          }}
        >
          <div
            className={styles.footerContent}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              zIndex: 10002,
              position: 'relative'
            }}
          >
            <div
              className={styles.footerNote}
              style={{
                color: '#6b7280',
                fontSize: '0.875rem'
              }}
            >
              Optimización automática para máximo ranking
            </div>

            <div
              className={`${styles.footerButtons} ${styles.forceVisible}`}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                zIndex: 10002,
                position: 'relative'
              }}
            >
              <button
                type="button"
                onClick={handleClose}
                disabled={isSubmitting}
                className={`${styles.cancelButton} ${styles.forceVisible}`}
                style={{
                  padding: '12px 24px',
                  color: '#374151',
                  backgroundColor: '#ffffff',
                  border: '1px solid #d1d5db',
                  borderRadius: '12px',
                  cursor: 'pointer',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  zIndex: 10003,
                  position: 'relative',
                  minHeight: '44px'
                }}
              >
                Cancelar
              </button>

              <button
                onClick={handleSubmit}
                disabled={isSubmitting || loading}
                className={`${styles.submitButton} ${styles.forceVisible}`}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px',
                  padding: '12px 24px',
                  background: 'linear-gradient(to right, #2563eb, #9333ea)',
                  color: '#ffffff',
                  border: 'none',
                  borderRadius: '12px',
                  fontWeight: '500',
                  fontSize: '0.875rem',
                  cursor: 'pointer',
                  zIndex: 10003,
                  position: 'relative',
                  minHeight: '44px'
                }}
              >
                {(isSubmitting || loading) ? (
                  <>
                    <Loader className={`w-4 h-4 animate-spin ${styles.icon}`} />
                    {isEditing ? 'Guardando...' : 'Creando...'}
                  </>
                ) : (
                  <>
                    <Save className={`w-4 h-4 ${styles.icon}`} />
                    {isEditing ? 'Guardar Cambios' : 'Crear Proyecto'}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default ProjectForm;

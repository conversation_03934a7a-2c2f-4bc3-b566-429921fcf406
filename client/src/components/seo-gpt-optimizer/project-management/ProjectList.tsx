/**
 * SEO & GPT Optimizer™ - Project List Component
 * Grid/list view of projects with filtering and sorting
 */

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Grid, 
  List, 
  Search, 
  Filter, 
  SortAsc, 
  SortDesc,
  Plus,
  RefreshCw,
  Download,
  MoreHorizontal
} from 'lucide-react';

import { 
  SEOGPTProject, 
  ProjectStatus, 
  ContentType, 
  ProjectFilters, 
  SortOptions 
} from '../../../types/seo-gpt-optimizer';
import ProjectCard from './ProjectCard';
import LoadingSpinner from '../shared/LoadingSpinner';

interface ProjectListProps {
  projects: SEOGPTProject[];
  loading?: boolean;
  onCreateProject?: () => void;
  onEditProject?: (project: SEOGPTProject) => void;
  onDeleteProject?: (projectId: string) => void;
  onDuplicateProject?: (project: SEOGPTProject) => void;
  onOpenProject?: (projectId: string) => void;
  onStatusChange?: (projectId: string, status: ProjectStatus) => void;
  onRefresh?: () => void;
  onExport?: () => void;
  className?: string;
}

const ProjectList: React.FC<ProjectListProps> = ({
  projects,
  loading = false,
  onCreateProject,
  onEditProject,
  onDeleteProject,
  onDuplicateProject,
  onOpenProject,
  onStatusChange,
  onRefresh,
  onExport,
  className = ''
}) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<ProjectFilters>({
    status: [],
    content_type: [],
    score_range: [0, 100],
    search_query: ''
  });
  const [sortOptions, setSortOptions] = useState<SortOptions>({
    field: 'updated_at',
    direction: 'desc'
  });
  const [showFilters, setShowFilters] = useState(false);

  // Filter and sort projects
  const filteredAndSortedProjects = useMemo(() => {
    let filtered = projects.filter(project => {
      // Search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch = 
          project.title.toLowerCase().includes(query) ||
          project.topic.toLowerCase().includes(query);
        if (!matchesSearch) return false;
      }

      // Status filter
      if (filters.status && filters.status.length > 0) {
        if (!filters.status.includes(project.status)) return false;
      }

      // Content type filter
      if (filters.content_type && filters.content_type.length > 0) {
        if (!filters.content_type.includes(project.content_type)) return false;
      }

      // Score range filter
      if (filters.score_range) {
        const [min, max] = filters.score_range;
        if (project.current_gpt_rank_score < min || project.current_gpt_rank_score > max) {
          return false;
        }
      }

      return true;
    });

    // Sort projects
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortOptions.field) {
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'gpt_rank_score':
          aValue = a.current_gpt_rank_score;
          bValue = b.current_gpt_rank_score;
          break;
        case 'created_at':
          aValue = new Date(a.created_at).getTime();
          bValue = new Date(b.created_at).getTime();
          break;
        case 'updated_at':
        default:
          aValue = new Date(a.updated_at).getTime();
          bValue = new Date(b.updated_at).getTime();
          break;
      }

      if (sortOptions.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [projects, searchQuery, filters, sortOptions]);

  const handleFilterChange = (key: keyof ProjectFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSortChange = (field: SortOptions['field']) => {
    setSortOptions(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'desc' ? 'asc' : 'desc'
    }));
  };

  const clearFilters = () => {
    setFilters({
      status: [],
      content_type: [],
      score_range: [0, 100],
      search_query: ''
    });
    setSearchQuery('');
  };

  const getSortIcon = (field: SortOptions['field']) => {
    if (sortOptions.field !== field) return null;
    return sortOptions.direction === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />;
  };

  if (loading && projects.length === 0) {
    return (
      <div className={`flex items-center justify-center py-12 ${className}`}>
        <LoadingSpinner size="lg" message="Cargando proyectos..." />
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Proyectos ({filteredAndSortedProjects.length})
          </h2>
          <p className="text-gray-600 text-sm">
            Gestiona todos tus proyectos de optimización
          </p>
        </div>

        <div className="flex items-center gap-3">
          {/* View Mode Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors duration-200 ${
                viewMode === 'grid' 
                  ? 'bg-white text-blue-600 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors duration-200 ${
                viewMode === 'list' 
                  ? 'bg-white text-blue-600 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>

          {/* Actions */}
          {onRefresh && (
            <button
              onClick={onRefresh}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              title="Actualizar"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
          )}

          {onExport && (
            <button
              onClick={onExport}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              title="Exportar"
            >
              <Download className="w-4 h-4" />
            </button>
          )}

          {onCreateProject && (
            <button
              onClick={onCreateProject}
              className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
            >
              <Plus className="w-4 h-4" />
              Nuevo Proyecto
            </button>
          )}
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
        <div className="flex items-center gap-4 mb-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Buscar proyectos por título o tema..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            />
          </div>

          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center gap-2 px-4 py-2 rounded-xl transition-colors duration-200 ${
              showFilters ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Filter className="w-4 h-4" />
            Filtros
          </button>

          {/* Sort */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Ordenar:</span>
            <div className="flex gap-1">
              {(['updated_at', 'created_at', 'gpt_rank_score', 'title'] as const).map((field) => (
                <button
                  key={field}
                  onClick={() => handleSortChange(field)}
                  className={`flex items-center gap-1 px-3 py-1 rounded-lg text-sm transition-colors duration-200 ${
                    sortOptions.field === field
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {field === 'updated_at' ? 'Actualizado' :
                   field === 'created_at' ? 'Creado' :
                   field === 'gpt_rank_score' ? 'Puntuación' : 'Título'}
                  {getSortIcon(field)}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Advanced Filters */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              className="border-t border-gray-200 pt-4"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Status Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Estado</label>
                  <div className="space-y-2">
                    {Object.values(ProjectStatus).map((status) => (
                      <label key={status} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.status?.includes(status) || false}
                          onChange={(e) => {
                            const currentStatuses = filters.status || [];
                            if (e.target.checked) {
                              handleFilterChange('status', [...currentStatuses, status]);
                            } else {
                              handleFilterChange('status', currentStatuses.filter(s => s !== status));
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700 capitalize">{status}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Content Type Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Tipo de Contenido</label>
                  <div className="space-y-2">
                    {Object.values(ContentType).map((type) => (
                      <label key={type} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.content_type?.includes(type) || false}
                          onChange={(e) => {
                            const currentTypes = filters.content_type || [];
                            if (e.target.checked) {
                              handleFilterChange('content_type', [...currentTypes, type]);
                            } else {
                              handleFilterChange('content_type', currentTypes.filter(t => t !== type));
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700 capitalize">{type}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Score Range Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Rango de Puntuación: {filters.score_range?.[0] || 0} - {filters.score_range?.[1] || 100}
                  </label>
                  <div className="space-y-3">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={filters.score_range?.[0] || 0}
                      onChange={(e) => {
                        const newMin = parseInt(e.target.value);
                        const currentMax = filters.score_range?.[1] || 100;
                        handleFilterChange('score_range', [newMin, Math.max(newMin, currentMax)]);
                      }}
                      className="w-full"
                    />
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={filters.score_range?.[1] || 100}
                      onChange={(e) => {
                        const newMax = parseInt(e.target.value);
                        const currentMin = filters.score_range?.[0] || 0;
                        handleFilterChange('score_range', [Math.min(currentMin, newMax), newMax]);
                      }}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end mt-4">
                <button
                  onClick={clearFilters}
                  className="text-sm text-gray-600 hover:text-gray-900 transition-colors duration-200"
                >
                  Limpiar filtros
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Projects Grid/List */}
      {filteredAndSortedProjects.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <MoreHorizontal className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {projects.length === 0 ? 'No hay proyectos' : 'No se encontraron proyectos'}
          </h3>
          <p className="text-gray-600 mb-6">
            {projects.length === 0 
              ? 'Crea tu primer proyecto para comenzar a optimizar contenido'
              : 'Intenta ajustar los filtros o términos de búsqueda'
            }
          </p>
          {projects.length === 0 && onCreateProject && (
            <button
              onClick={onCreateProject}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
            >
              Crear Primer Proyecto
            </button>
          )}
        </div>
      ) : (
        <motion.div
          className={
            viewMode === 'grid' 
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
              : 'space-y-4'
          }
          layout
        >
          <AnimatePresence>
            {filteredAndSortedProjects.map((project, index) => (
              <motion.div
                key={project.project_id}
                layout
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <ProjectCard
                  project={project}
                  onEdit={onEditProject}
                  onDelete={onDeleteProject}
                  onDuplicate={onDuplicateProject}
                  onOpen={onOpenProject}
                  onStatusChange={onStatusChange}
                  className={viewMode === 'list' ? 'max-w-none' : ''}
                />
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>
      )}

      {/* Loading Overlay */}
      {loading && projects.length > 0 && (
        <motion.div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <div className="bg-white rounded-2xl p-8 shadow-xl">
            <LoadingSpinner size="md" message="Actualizando proyectos..." />
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default ProjectList;

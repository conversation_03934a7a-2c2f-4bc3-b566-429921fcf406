/**
 * SEO & GPT Optimizer™ - Research Results Component
 * Displays comprehensive research results
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Search, 
  MessageSquare, 
  Brain, 
  Tag, 
  Lightbulb, 
  TrendingUp,
  ExternalLink,
  Copy,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { ResearchResults as ResearchResultsType } from '../../../types/seo-gpt-optimizer';

interface ResearchResultsProps {
  results: ResearchResultsType;
  confidence: number;
  processingTime: number;
  className?: string;
}

const ResearchResults: React.FC<ResearchResultsProps> = ({
  results,
  confidence,
  processingTime,
  className = ''
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['summary']));

  const toggleSection = (section: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(section)) {
        newSet.delete(section);
      } else {
        newSet.add(section);
      }
      return newSet;
    });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const isExpanded = (section: string) => expandedSections.has(section);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Research Summary */}
      <motion.div
        className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <div 
          className="p-6 border-b border-gray-100 cursor-pointer"
          onClick={() => toggleSection('summary')}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">Resumen de Investigación</h2>
                <p className="text-gray-600 text-sm">
                  Confianza: {(confidence * 100).toFixed(1)}% • Tiempo: {processingTime.toFixed(1)}s
                </p>
              </div>
            </div>
            {isExpanded('summary') ? <ChevronUp className="w-5 h-5 text-gray-400" /> : <ChevronDown className="w-5 h-5 text-gray-400" />}
          </div>
        </div>

        {isExpanded('summary') && (
          <motion.div
            className="p-6"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Insights Clave</h3>
                <div className="space-y-2">
                  {results.research_summary.key_insights.map((insight, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-sm text-gray-700">{insight}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Preguntas Prioritarias</h3>
                <div className="space-y-2">
                  {results.research_summary.priority_questions.slice(0, 5).map((question, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-sm text-gray-700">{question}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-xl">
              <h4 className="font-medium text-blue-900 mb-2">Enfoque Recomendado</h4>
              <div className="text-sm text-blue-800">
                <p><strong>Tipo de Intención:</strong> {results.intent_analysis.intent_type}</p>
                <p><strong>Audiencia Objetivo:</strong> {results.intent_analysis.target_audience}</p>
                <p><strong>Longitud Óptima:</strong> {results.intent_analysis.optimal_content_length}</p>
                <p><strong>Tono Preferido:</strong> {results.intent_analysis.preferred_tone}</p>
              </div>
            </div>
          </motion.div>
        )}
      </motion.div>

      {/* Google Results */}
      <motion.div
        className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <div 
          className="p-6 border-b border-gray-100 cursor-pointer"
          onClick={() => toggleSection('google')}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                <Search className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">Resultados de Google</h2>
                <p className="text-gray-600 text-sm">
                  {results.google_results.results.length} resultados analizados
                </p>
              </div>
            </div>
            {isExpanded('google') ? <ChevronUp className="w-5 h-5 text-gray-400" /> : <ChevronDown className="w-5 h-5 text-gray-400" />}
          </div>
        </div>

        {isExpanded('google') && (
          <motion.div
            className="p-6"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <div className="space-y-4">
              {results.google_results.results.slice(0, 5).map((result, index) => (
                <div key={index} className="border border-gray-200 rounded-xl p-4 hover:bg-gray-50 transition-colors duration-200">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-xs font-medium text-gray-500">#{result.position}</span>
                        <span className="text-xs text-gray-500">{result.domain}</span>
                      </div>
                      <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                        {result.title}
                      </h3>
                      <p className="text-sm text-gray-600 line-clamp-3 mb-3">
                        {result.snippet}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>Título: {result.title_length} chars</span>
                        <span>Snippet: {result.snippet_length} chars</span>
                      </div>
                    </div>
                    <div className="flex flex-col gap-2">
                      <button
                        onClick={() => window.open(result.link, '_blank')}
                        className="p-2 text-gray-400 hover:text-blue-600 transition-colors duration-200"
                        title="Abrir enlace"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => copyToClipboard(result.title)}
                        className="p-2 text-gray-400 hover:text-green-600 transition-colors duration-200"
                        title="Copiar título"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* People Also Ask */}
            {results.google_results.serp_features.people_also_ask.length > 0 && (
              <div className="mt-6 p-4 bg-yellow-50 rounded-xl">
                <h4 className="font-medium text-yellow-900 mb-3">La gente también pregunta</h4>
                <div className="space-y-2">
                  {results.google_results.serp_features.people_also_ask.slice(0, 5).map((item, index) => (
                    <div key={index} className="text-sm text-yellow-800">
                      • {item.question}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </motion.div>
        )}
      </motion.div>

      {/* Social Insights */}
      {(results.social_insights.reddit || results.social_insights.quora) && (
        <motion.div
          className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <div 
            className="p-6 border-b border-gray-100 cursor-pointer"
            onClick={() => toggleSection('social')}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                  <MessageSquare className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">Insights Sociales</h2>
                  <p className="text-gray-600 text-sm">
                    Conversaciones reales de la comunidad
                  </p>
                </div>
              </div>
              {isExpanded('social') ? <ChevronUp className="w-5 h-5 text-gray-400" /> : <ChevronDown className="w-5 h-5 text-gray-400" />}
            </div>
          </div>

          {isExpanded('social') && (
            <motion.div
              className="p-6"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
            >
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Reddit Insights */}
                {results.social_insights.reddit && (
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">R</span>
                      </div>
                      Reddit ({results.social_insights.reddit.total_results})
                    </h3>
                    <div className="space-y-3">
                      {results.social_insights.reddit.insights.slice(0, 3).map((insight, index) => (
                        <div key={index} className="p-3 bg-orange-50 rounded-lg">
                          <h4 className="font-medium text-orange-900 text-sm mb-1">
                            {insight.title}
                          </h4>
                          <p className="text-xs text-orange-700 line-clamp-2">
                            {insight.snippet}
                          </p>
                          {insight.subreddit && (
                            <span className="text-xs text-orange-600 mt-1 inline-block">
                              r/{insight.subreddit}
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Quora Insights */}
                {results.social_insights.quora && (
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">Q</span>
                      </div>
                      Quora ({results.social_insights.quora.total_results})
                    </h3>
                    <div className="space-y-3">
                      {results.social_insights.quora.insights.slice(0, 3).map((insight, index) => (
                        <div key={index} className="p-3 bg-red-50 rounded-lg">
                          <h4 className="font-medium text-red-900 text-sm mb-1">
                            {insight.question}
                          </h4>
                          <p className="text-xs text-red-700 line-clamp-2">
                            {insight.answer_preview}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </motion.div>
      )}

      {/* Content Opportunities */}
      <motion.div
        className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <div 
          className="p-6 border-b border-gray-100 cursor-pointer"
          onClick={() => toggleSection('opportunities')}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                <Lightbulb className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">Oportunidades de Contenido</h2>
                <p className="text-gray-600 text-sm">
                  Gaps y ángulos únicos identificados
                </p>
              </div>
            </div>
            {isExpanded('opportunities') ? <ChevronUp className="w-5 h-5 text-gray-400" /> : <ChevronDown className="w-5 h-5 text-gray-400" />}
          </div>
        </div>

        {isExpanded('opportunities') && (
          <motion.div
            className="p-6"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Gaps de Contenido</h3>
                <div className="space-y-2">
                  {results.content_opportunities.content_gaps.map((gap, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-sm text-gray-700">{gap}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Ángulos Únicos</h3>
                <div className="space-y-2">
                  {results.content_opportunities.unique_angles.map((angle, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-sm text-gray-700">{angle}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-gray-50 rounded-xl">
                <h4 className="font-medium text-gray-900 mb-2">Profundidad Recomendada</h4>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  results.content_opportunities.content_depth_recommendation === 'profundo' 
                    ? 'bg-red-100 text-red-700'
                    : results.content_opportunities.content_depth_recommendation === 'medio'
                    ? 'bg-yellow-100 text-yellow-700'
                    : 'bg-green-100 text-green-700'
                }`}>
                  {results.content_opportunities.content_depth_recommendation}
                </span>
              </div>

              <div className="p-4 bg-gray-50 rounded-xl">
                <h4 className="font-medium text-gray-900 mb-2">Nivel de Competencia</h4>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  results.content_opportunities.estimated_competition_level === 'alto' 
                    ? 'bg-red-100 text-red-700'
                    : results.content_opportunities.estimated_competition_level === 'medio'
                    ? 'bg-yellow-100 text-yellow-700'
                    : 'bg-green-100 text-green-700'
                }`}>
                  {results.content_opportunities.estimated_competition_level}
                </span>
              </div>

              <div className="p-4 bg-gray-50 rounded-xl">
                <h4 className="font-medium text-gray-900 mb-2">Keywords Objetivo</h4>
                <div className="text-sm text-gray-700">
                  {results.content_opportunities.target_keywords.slice(0, 3).join(', ')}
                  {results.content_opportunities.target_keywords.length > 3 && '...'}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
};

export default ResearchResults;

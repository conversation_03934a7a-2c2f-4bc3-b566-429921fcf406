/**
 * SEO & GPT Optimizer™ - GPT Rank Meter Component
 * Visual meter showing GPT Rank Score with animations
 */

import React from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface GPTRankMeterProps {
  score: number;
  grade: string;
  previousScore?: number;
  size?: 'sm' | 'md' | 'lg';
  showDetails?: boolean;
  animated?: boolean;
  className?: string;
}

const getScoreColor = (score: number): string => {
  if (score >= 90) return '#10b981'; // Green
  if (score >= 80) return '#3b82f6'; // Blue
  if (score >= 70) return '#f59e0b'; // Yellow
  if (score >= 60) return '#f97316'; // Orange
  return '#ef4444'; // Red
};

const getScoreLabel = (score: number): string => {
  if (score >= 90) return 'Excelente';
  if (score >= 80) return 'Muy Bueno';
  if (score >= 70) return 'Bueno';
  if (score >= 60) return 'Regular';
  return 'Necesita Mejora';
};

const sizeConfig = {
  sm: { size: 80, strokeWidth: 6, fontSize: 'text-sm' },
  md: { size: 120, strokeWidth: 8, fontSize: 'text-lg' },
  lg: { size: 160, strokeWidth: 10, fontSize: 'text-2xl' }
};

const GPTRankMeter: React.FC<GPTRankMeterProps> = ({
  score,
  grade,
  previousScore,
  size = 'md',
  showDetails = true,
  animated = true,
  className = ''
}) => {
  const config = sizeConfig[size];
  const radius = (config.size - config.strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (score / 100) * circumference;

  const scoreColor = getScoreColor(score);
  const scoreLabel = getScoreLabel(score);

  // Calculate score change
  const scoreChange = previousScore !== undefined ? score - previousScore : 0;
  const hasIncrease = scoreChange > 0;
  const hasDecrease = scoreChange < 0;

  return (
    <div className={`flex flex-col items-center ${className}`}>
      {/* Circular Progress */}
      <div className="relative" style={{ width: config.size, height: config.size }}>
        <svg
          width={config.size}
          height={config.size}
          className="transform -rotate-90"
        >
          {/* Background Circle */}
          <circle
            cx={config.size / 2}
            cy={config.size / 2}
            r={radius}
            fill="none"
            stroke="#e5e7eb"
            strokeWidth={config.strokeWidth}
          />
          
          {/* Progress Circle */}
          <motion.circle
            cx={config.size / 2}
            cy={config.size / 2}
            r={radius}
            fill="none"
            stroke={scoreColor}
            strokeWidth={config.strokeWidth}
            strokeLinecap="round"
            strokeDasharray={strokeDasharray}
            initial={animated ? { strokeDashoffset: circumference } : { strokeDashoffset }}
            animate={{ strokeDashoffset }}
            transition={animated ? { duration: 1.5, ease: "easeOut" } : { duration: 0 }}
          />
        </svg>

        {/* Score Text */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <motion.div
            className={`font-bold ${config.fontSize}`}
            style={{ color: scoreColor }}
            initial={animated ? { scale: 0 } : { scale: 1 }}
            animate={{ scale: 1 }}
            transition={animated ? { delay: 0.5, duration: 0.5, type: "spring" } : { duration: 0 }}
          >
            {Math.round(score)}
          </motion.div>
          <div className="text-xs text-gray-500 font-medium">
            {grade}
          </div>
        </div>

        {/* Score Change Indicator */}
        {previousScore !== undefined && scoreChange !== 0 && (
          <motion.div
            className={`absolute -top-2 -right-2 flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
              hasIncrease 
                ? 'bg-green-100 text-green-700' 
                : 'bg-red-100 text-red-700'
            }`}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 1, duration: 0.3 }}
          >
            {hasIncrease && <TrendingUp className="w-3 h-3" />}
            {hasDecrease && <TrendingDown className="w-3 h-3" />}
            {scoreChange === 0 && <Minus className="w-3 h-3" />}
            {Math.abs(scoreChange).toFixed(1)}
          </motion.div>
        )}
      </div>

      {/* Details */}
      {showDetails && (
        <motion.div
          className="mt-4 text-center"
          initial={animated ? { opacity: 0, y: 10 } : { opacity: 1, y: 0 }}
          animate={{ opacity: 1, y: 0 }}
          transition={animated ? { delay: 1, duration: 0.5 } : { duration: 0 }}
        >
          <div className="text-sm font-medium text-gray-900 mb-1">
            GPT Rank Score
          </div>
          <div className="text-xs text-gray-600">
            {scoreLabel}
          </div>
          
          {/* Score Range Indicator */}
          <div className="mt-3 w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
            <div className="h-full flex">
              <div className="flex-1 bg-red-400"></div>
              <div className="flex-1 bg-orange-400"></div>
              <div className="flex-1 bg-yellow-400"></div>
              <div className="flex-1 bg-blue-400"></div>
              <div className="flex-1 bg-green-400"></div>
            </div>
            
            {/* Score Indicator */}
            <div 
              className="relative -mt-2 w-0.5 h-2 bg-gray-800 rounded-full"
              style={{ 
                marginLeft: `${(score / 100) * 100}%`,
                transform: 'translateX(-50%)'
              }}
            />
          </div>
          
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>0</span>
            <span>50</span>
            <span>100</span>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default GPTRankMeter;

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  TrendingU<PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>ointer, 
  <PERSON>, 
  Share2, 
  <PERSON><PERSON><PERSON><PERSON>,
  Target,
  Zap,
  Award,
  Clock
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

interface AdPerformanceInsightsProps {
  platform: string;
  currentPrompt?: string;
  generatedAdsCount?: number;
}

interface PlatformMetrics {
  avgCTR: number;
  avgEngagement: number;
  bestPerformingStyle: string;
  recommendedColors: string[];
  peakHours: string;
  audienceAge: string;
}

const AdPerformanceInsights: React.FC<AdPerformanceInsightsProps> = ({
  platform,
  currentPrompt = "",
  generatedAdsCount = 0
}) => {
  const [insights, setInsights] = useState<PlatformMetrics | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Métricas simuladas por plataforma
  const platformMetrics: Record<string, PlatformMetrics> = {
    facebook: {
      avgCTR: 1.85,
      avgEngagement: 3.2,
      bestPerformingStyle: "Moderno",
      recommendedColors: ["#1877F2", "#42B883", "#FF6B6B"],
      peakHours: "19:00 - 21:00",
      audienceAge: "25-44 años"
    },
    instagram: {
      avgCTR: 2.1,
      avgEngagement: 4.7,
      bestPerformingStyle: "Estético",
      recommendedColors: ["#E4405F", "#FCAF45", "#833AB4"],
      peakHours: "20:00 - 22:00",
      audienceAge: "18-34 años"
    },
    linkedin: {
      avgCTR: 0.9,
      avgEngagement: 2.1,
      bestPerformingStyle: "Corporativo",
      recommendedColors: ["#0077B5", "#2E8B57", "#4B0082"],
      peakHours: "09:00 - 11:00",
      audienceAge: "25-54 años"
    },
    youtube: {
      avgCTR: 3.8,
      avgEngagement: 6.2,
      bestPerformingStyle: "Llamativo",
      recommendedColors: ["#FF0000", "#FF4500", "#FFD700"],
      peakHours: "16:00 - 20:00",
      audienceAge: "16-44 años"
    }
  };

  useEffect(() => {
    setIsAnalyzing(true);
    // Simular análisis
    setTimeout(() => {
      setInsights(platformMetrics[platform] || platformMetrics.facebook);
      setIsAnalyzing(false);
    }, 1000);
  }, [platform]);

  const getPromptScore = (prompt: string): number => {
    if (!prompt) return 0;
    
    let score = 0;
    const words = prompt.toLowerCase().split(' ');
    
    // Palabras clave que mejoran el rendimiento
    const powerWords = ['profesional', 'exclusivo', 'limitado', 'gratis', 'nuevo', 'mejor', 'único'];
    const emotionalWords = ['increíble', 'sorprendente', 'revolucionario', 'extraordinario'];
    const actionWords = ['descubre', 'obtén', 'consigue', 'aprovecha', 'únete'];
    
    powerWords.forEach(word => {
      if (words.includes(word)) score += 15;
    });
    
    emotionalWords.forEach(word => {
      if (words.includes(word)) score += 10;
    });
    
    actionWords.forEach(word => {
      if (words.includes(word)) score += 12;
    });
    
    // Longitud óptima
    if (words.length >= 5 && words.length <= 15) score += 20;
    
    return Math.min(score, 100);
  };

  const promptScore = getPromptScore(currentPrompt);

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return "Excelente";
    if (score >= 60) return "Bueno";
    if (score >= 40) return "Regular";
    return "Necesita mejoras";
  };

  if (isAnalyzing) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Analizando Rendimiento...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#3018ef]"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!insights) return null;

  return (
    <div className="space-y-4">
      {/* Análisis del Prompt */}
      {currentPrompt && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              Análisis del Prompt
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Puntuación de Efectividad</span>
                <Badge className={getScoreColor(promptScore)}>
                  {promptScore}/100 - {getScoreLabel(promptScore)}
                </Badge>
              </div>
              <Progress value={promptScore} className="h-2" />
              <div className="text-xs text-gray-600">
                {promptScore >= 80 && "¡Excelente! Tu prompt tiene alta probabilidad de generar anuncios efectivos."}
                {promptScore >= 60 && promptScore < 80 && "Buen prompt. Considera añadir palabras de acción o emocionales."}
                {promptScore < 60 && "Tu prompt puede mejorarse. Intenta ser más específico y usar palabras clave."}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Métricas de la Plataforma */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Métricas de {platform}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <MousePointer className="w-6 h-6 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-blue-600">{insights.avgCTR}%</div>
              <div className="text-xs text-gray-600">CTR Promedio</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <Heart className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-green-600">{insights.avgEngagement}%</div>
              <div className="text-xs text-gray-600">Engagement</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recomendaciones */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="w-5 h-5" />
            Recomendaciones
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm">Mejor estilo:</span>
            <Badge variant="secondary">{insights.bestPerformingStyle}</Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm">Horario óptimo:</span>
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              {insights.peakHours}
            </Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm">Audiencia principal:</span>
            <Badge variant="outline">{insights.audienceAge}</Badge>
          </div>

          <div>
            <span className="text-sm block mb-2">Colores recomendados:</span>
            <div className="flex gap-2">
              {insights.recommendedColors.map((color, index) => (
                <div
                  key={index}
                  className="w-6 h-6 rounded-full border-2 border-white shadow-sm"
                  style={{ backgroundColor: color }}
                  title={color}
                />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Estadísticas de Sesión */}
      {generatedAdsCount > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="w-5 h-5" />
              Tu Sesión
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Anuncios generados:</span>
                <Badge className="bg-[#3018ef] text-white">{generatedAdsCount}</Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Tiempo estimado ahorrado:</span>
                <Badge variant="outline">{generatedAdsCount * 15} min</Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Costo evitado:</span>
                <Badge variant="outline" className="text-green-600">
                  ${generatedAdsCount * 50}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AdPerformanceInsights;

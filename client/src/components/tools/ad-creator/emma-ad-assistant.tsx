/**
 * <PERSON> Assistant - AI-powered assistant for ad creation
 */

import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Sparkles, MessageCircle, X } from 'lucide-react';

interface EmmaAdAssistantProps {
  platform: string;
  currentPrompt: string;
  onPromptSuggestion: (prompt: string) => void;
  onSizeRecommendation: (size: string) => void;
  className?: string;
}

export function EmmaAdAssistant({
  platform,
  currentPrompt,
  onPromptSuggestion,
  onSizeRecommendation,
  className = ""
}: EmmaAdAssistantProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isThinking, setIsThinking] = useState(false);

  const handleSuggestPrompt = async () => {
    setIsThinking(true);

    try {
      const response = await fetch('/api/v1/content/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'dev-api-key-for-testing'
        },
        body: JSON.stringify({
          prompt: `Genera una descripción optimizada para crear un anuncio de ${platform}. El prompt debe ser específico, creativo y orientado a conversiones. Contexto actual: "${currentPrompt || 'nuevo anuncio'}"`,
          type: 'ad_prompt_suggestion',
          platform: platform,
          max_tokens: 150
        })
      });

      if (response.ok) {
        const result = await response.json();
        const suggestion = result.content || result.text || result.generated_content;
        if (suggestion) {
          onPromptSuggestion(suggestion);
          setIsThinking(false);
          return;
        }
      }
    } catch (error) {
      console.warn('Failed to get AI suggestion, using fallback:', error);
    }

    // Fallback suggestions if AI fails
    const suggestions = {
      facebook: "Crea un anuncio llamativo para Facebook con colores vibrantes, texto claro y un call-to-action irresistible que genere conversiones",
      instagram: "Diseña un post de Instagram con estética moderna, colores trending y elementos visuales que conecten con la audiencia joven",
      google: "Desarrolla un banner de Google Ads profesional con mensaje directo, branding visible y diseño que destaque entre la competencia"
    };

    const suggestion = suggestions[platform as keyof typeof suggestions] ||
      "Crea un anuncio profesional y atractivo que capture la atención de tu audiencia objetivo";

    onPromptSuggestion(suggestion);
    setIsThinking(false);
  };

  const handleSizeRecommendation = () => {
    const recommendations = {
      facebook: "1080x1080",
      instagram: "1080x1080", 
      google: "1200x628",
      linkedin: "1200x627",
      youtube: "1280x720",
      display: "728x90"
    };

    const size = recommendations[platform as keyof typeof recommendations] || "1080x1080";
    onSizeRecommendation(size);
  };

  if (!isOpen) {
    return (
      <div className={`fixed bottom-6 z-50 ${className}`}>
        <Button
          onClick={() => setIsOpen(true)}
          className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:opacity-90 text-white rounded-full p-4 shadow-lg"
        >
          <Sparkles className="w-6 h-6" />
        </Button>
      </div>
    );
  }

  return (
    <div className={`fixed bottom-6 z-50 ${className}`}>
      <Card className="w-80 shadow-xl border-2 border-[#3018ef]/20">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Sparkles className="w-5 h-5 text-[#3018ef]" />
              Emma AI
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(false)}
              className="h-6 w-6 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-2">
              <MessageCircle className="w-4 h-4 text-[#3018ef]" />
              <span className="text-sm font-medium">Asistente para {platform}</span>
            </div>
            <p className="text-xs text-gray-600">
              ¡Hola! Soy Emma, tu asistente de IA. Puedo ayudarte a optimizar tu anuncio.
            </p>
          </div>

          <div className="space-y-2">
            <Button
              onClick={handleSuggestPrompt}
              disabled={isThinking}
              className="w-full text-sm h-8 bg-[#3018ef] hover:bg-[#3018ef]/90"
            >
              {isThinking ? "Pensando..." : "Sugerir descripción"}
            </Button>
            
            <Button
              onClick={handleSizeRecommendation}
              variant="outline"
              className="w-full text-sm h-8"
            >
              Recomendar tamaño
            </Button>
          </div>

          {currentPrompt && (
            <div className="bg-gray-50 rounded-lg p-2">
              <p className="text-xs text-gray-600">
                <strong>Prompt actual:</strong> {currentPrompt.slice(0, 100)}
                {currentPrompt.length > 100 && "..."}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

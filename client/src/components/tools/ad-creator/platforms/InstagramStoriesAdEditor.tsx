/**
 * Instagram Stories Ad Editor - Specialized editor for Instagram Stories
 */

import { SimplifiedAdCreator } from "@/components/tools/ad-creator/shared/SimplifiedAdCreator";
import { instagramStoriesConfig } from "@/config/platform-configs";

interface InstagramStoriesAdEditorProps {
  onBack?: () => void;
}

export default function InstagramStoriesAdEditor({ onBack }: InstagramStoriesAdEditorProps) {
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      window.history.back();
    }
  };

  return (
    <SimplifiedAdCreator
      platform="instagram-stories"
      config={instagramStoriesConfig}
      onBack={handleBack}
    />
  );
}

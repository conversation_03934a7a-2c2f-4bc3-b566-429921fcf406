/**
 * Shared Ad Controls Component
 * Reusable controls for all platform editors
 */

import { motion } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { 
  Wand2, 
  Camera, 
  Upload,
  X,
  RefreshCw
} from "lucide-react";
import { AdControlsProps } from "@/types/ad-creator-types";

export function AdControls({
  prompt,
  size,
  isGenerating,
  useProductImages,
  productImages,
  onPromptChange,
  onSizeChange,
  onUseProductImagesChange,
  onProductImageUpload,
  onRemoveProductImage,
  onGenerate,
  productInputRef,
  config
}: AdControlsProps) {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.2 }}
      className="space-y-4"
    >
      {/* Creación básica */}
      <Card className="h-fit">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Wand2 className="h-5 w-5" />
            Crear Anuncio
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Prompt principal */}
          <div className="space-y-2">
            <Label htmlFor="prompt" className="text-sm font-medium">Descripción del anuncio</Label>
            <Textarea
              id="prompt"
              placeholder={`Describe tu anuncio para ${config.name}...`}
              value={prompt}
              onChange={(e) => onPromptChange(e.target.value)}
              rows={3}
              className="resize-none text-sm"
            />
          </div>

          {/* Selector de tamaño */}
          <div className="space-y-2">
            <Label htmlFor="size" className="text-sm font-medium">Tamaño</Label>
            <select
              id="size"
              value={size}
              onChange={(e) => onSizeChange(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-sm"
            >
              {config.sizes.map((sizeOption) => (
                <option key={sizeOption} value={sizeOption}>
                  {sizeOption}
                </option>
              ))}
            </select>
          </div>

          {/* Botón de generación */}
          <Button
            onClick={onGenerate}
            disabled={isGenerating || !prompt.trim()}
            className={`w-full bg-gradient-to-r ${config.color} hover:opacity-90 text-sm h-10`}
          >
            {isGenerating ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Generando...
              </>
            ) : (
              <>
                <Wand2 className="w-4 h-4 mr-2" />
                Generar Anuncio
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Sección de imágenes de producto */}
      <Card className="h-fit">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Camera className="h-5 w-5" />
            Imágenes de Producto
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Usar imágenes de producto</Label>
              <Switch
                checked={useProductImages}
                onCheckedChange={onUseProductImagesChange}
              />
            </div>

            {useProductImages && (
              <>
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-[#3018ef] transition-colors"
                  onClick={() => productInputRef.current?.click()}
                >
                  <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm text-gray-600">
                    Arrastra imágenes o haz clic para subir
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    Máximo 3 imágenes • JPG, PNG
                  </p>
                </div>

                <input
                  ref={productInputRef}
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={(e) => onProductImageUpload(e.target.files)}
                  className="hidden"
                />

                {productImages.length > 0 && (
                  <div className="grid grid-cols-3 gap-3">
                    {productImages.map((file, index) => (
                      <div key={index} className="relative">
                        <img
                          src={URL.createObjectURL(file)}
                          alt={`Producto ${index + 1}`}
                          className="w-full h-20 object-cover rounded border"
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onRemoveProductImage(index)}
                          className="absolute -top-2 -right-2 h-6 w-6 p-0 bg-red-500 hover:bg-red-600 text-white rounded-full"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

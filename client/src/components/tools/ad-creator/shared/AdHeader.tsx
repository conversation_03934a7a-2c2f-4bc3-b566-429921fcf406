/**
 * Shared Ad Header Component
 * Reusable header for all platform editors
 */

import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>Lef<PERSON>, Sparkles } from "lucide-react";
import { AdHeaderProps } from "@/types/ad-creator-types";

export function AdHeader({ onBack, config }: AdHeaderProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white border-b border-gray-200 px-4 md:px-6 py-4"
    >
      <div className="max-w-[1600px] mx-auto">
        <div className="flex items-center justify-between">
          {/* Lado izquierdo */}
          <div className="flex items-center gap-4">
            <Button
              onClick={onBack}
              variant="ghost"
              size="sm"
              className="text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver
            </Button>
            
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg bg-gradient-to-r ${config.color}`}>
                <config.icon className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  {config.name}
                </h1>
                <p className="text-sm text-gray-600">
                  {config.description}
                </p>
              </div>
            </div>
          </div>

          {/* Lado derecho */}
          <div className="flex items-center gap-3">
            <Badge 
              className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white"
            >
              <Sparkles className="w-3 h-3 mr-1" />
              Emma AI
            </Badge>
            
            <div className="hidden md:flex items-center gap-2 text-sm text-gray-600">
              <span>Tamaño por defecto:</span>
              <Badge variant="outline" className="text-xs">
                {config.defaultSize}
              </Badge>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

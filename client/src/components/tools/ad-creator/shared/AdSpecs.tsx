/**
 * Shared Ad Specifications Component
 * Reusable specifications display for all platform editors
 */

import { motion } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Info, 
  Ruler, 
  FileImage, 
  Type, 
  CheckCircle 
} from "lucide-react";
import { AdSpecsProps } from "@/types/ad-creator-types";

export function AdSpecs({ config }: AdSpecsProps) {
  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.4 }}
    >
      <Card className="h-fit">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Info className="h-5 w-5" />
            Especificaciones
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Información de la plataforma */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <config.icon className="h-4 w-4" />
              <span className="text-sm font-medium">{config.name}</span>
            </div>
            <p className="text-xs text-gray-600">
              {config.description}
            </p>
          </div>

          {/* Especificaciones técnicas */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Ruler className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium">Dimensiones</span>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Relación de aspecto:</span>
                <Badge variant="outline" className="text-xs">
                  {config.specifications.aspectRatio}
                </Badge>
              </div>
              <div className="flex flex-wrap gap-1">
                {config.sizes.map((size) => (
                  <Badge 
                    key={size} 
                    variant="secondary" 
                    className="text-xs"
                  >
                    {size}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Formatos de archivo */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <FileImage className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium">Archivos</span>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Tamaño máximo:</span>
                <Badge variant="outline" className="text-xs">
                  {config.specifications.fileSize}
                </Badge>
              </div>
              <div className="flex flex-wrap gap-1">
                {config.specifications.fileTypes.map((type) => (
                  <Badge 
                    key={type} 
                    variant="secondary" 
                    className="text-xs"
                  >
                    {type}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Límites de texto */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Type className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium">Texto</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600">Límite de caracteres:</span>
              <Badge variant="outline" className="text-xs">
                {config.specifications.textLimit}
              </Badge>
            </div>
          </div>

          {/* Mejores prácticas */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium">Mejores Prácticas</span>
            </div>
            <div className="space-y-2">
              {config.specifications.guidelines.map((guideline, index) => (
                <div key={index} className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-[#3018ef] rounded-full mt-1.5 flex-shrink-0" />
                  <span className="text-xs text-gray-600">{guideline}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Información adicional */}
          <div className="bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 p-3 rounded-lg">
            <div className="flex items-start gap-2">
              <Info className="h-4 w-4 text-[#3018ef] mt-0.5 flex-shrink-0" />
              <div className="space-y-1">
                <p className="text-xs font-medium text-gray-800">
                  Consejo de Emma
                </p>
                <p className="text-xs text-gray-600">
                  Para mejores resultados, usa imágenes de alta calidad y 
                  asegúrate de que el texto sea legible en dispositivos móviles.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

/**
 * Shared Ad Tools Component
 * Reusable AI tools for all platform editors
 */

import { motion } from "framer-motion";
import { AdCreatorEnhancements } from "@/components/tools/ad-creator/ad-creator-enhancements";
import { AdToolsProps } from "@/types/ad-creator-types";

export function AdTools({
  prompt,
  generatedAdsCount,
  onPromptEnhance,
  onStyleApply,
  toast,
  platform
}: AdToolsProps) {
  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.6 }}
      className="space-y-4"
    >
      {/* Mejoras de IA */}
      <AdCreatorEnhancements
        platform={platform}
        currentPrompt={prompt}
        onPromptEnhance={onPromptEnhance}
        onStyleApply={onStyleApply}
      />
    </motion.div>
  );
}

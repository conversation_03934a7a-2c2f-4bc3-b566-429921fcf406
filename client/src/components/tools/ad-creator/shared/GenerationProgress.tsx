/**
 * Generation Progress Component
 * Real-time progress tracking for ad generation with preview thumbnails
 */

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle, AlertCircle, Loader2, Image as ImageIcon } from 'lucide-react';
import { GeneratedAd } from '@/types/ad-creator-types';

interface GenerationProgressProps {
  isGenerating: boolean;
  completed: number;
  total: number;
  variations: GeneratedAd[];
  errors: Array<{ index: number; error: string }>;
  estimatedTimeRemaining?: number;
  onCancel?: () => void;
}

export function GenerationProgress({
  isGenerating,
  completed,
  total,
  variations,
  errors,
  estimatedTimeRemaining,
  onCancel
}: GenerationProgressProps) {
  const progressPercentage = total > 0 ? (completed / total) * 100 : 0;
  const successCount = variations.length;
  const errorCount = errors.length;

  if (!isGenerating && completed === 0) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className="w-full max-w-2xl mx-auto"
      >
        <Card className="border-2 border-blue-200 bg-blue-50/50 backdrop-blur-sm">
          <CardContent className="p-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                {isGenerating ? (
                  <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />
                ) : (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                )}
                <div>
                  <h3 className="font-semibold text-gray-900">
                    {isGenerating ? 'Generando anuncios...' : 'Generación completada'}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {successCount} exitosos, {errorCount} errores de {total} solicitados
                  </p>
                </div>
              </div>
              
              {isGenerating && onCancel && (
                <button
                  onClick={onCancel}
                  className="text-sm text-gray-500 hover:text-gray-700 px-3 py-1 rounded-md hover:bg-gray-100"
                >
                  Cancelar
                </button>
              )}
            </div>

            {/* Progress Bar */}
            <div className="mb-4">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>{completed} de {total} completados</span>
                {estimatedTimeRemaining && isGenerating && (
                  <span>{Math.ceil(estimatedTimeRemaining)}s restantes</span>
                )}
              </div>
              <Progress 
                value={progressPercentage} 
                className="h-2"
              />
            </div>

            {/* Variation Grid */}
            <div className="grid grid-cols-6 gap-2 mb-4">
              {Array.from({ length: total }, (_, index) => {
                const variation = variations.find(v => (v.metadata?.variation || 0) === index + 1);
                const error = errors.find(e => e.index === index);
                const isCompleted = variation || error;
                const isProcessing = !isCompleted && index < completed;

                return (
                  <motion.div
                    key={index}
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className={`
                      aspect-square rounded-lg border-2 flex items-center justify-center text-xs font-medium
                      ${variation ? 'border-green-200 bg-green-50 text-green-700' : ''}
                      ${error ? 'border-red-200 bg-red-50 text-red-700' : ''}
                      ${isProcessing ? 'border-blue-200 bg-blue-50 text-blue-700 animate-pulse' : ''}
                      ${!isCompleted && !isProcessing ? 'border-gray-200 bg-gray-50 text-gray-400' : ''}
                    `}
                  >
                    {variation && (
                      <div className="w-full h-full rounded-md overflow-hidden">
                        <img
                          src={variation.image_url}
                          alt={`Variación ${index + 1}`}
                          className="w-full h-full object-cover"
                          loading="lazy"
                        />
                      </div>
                    )}
                    {error && <AlertCircle className="h-4 w-4" />}
                    {isProcessing && <Loader2 className="h-4 w-4 animate-spin" />}
                    {!isCompleted && !isProcessing && <ImageIcon className="h-4 w-4" />}
                  </motion.div>
                );
              })}
            </div>

            {/* Status Summary */}
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-4">
                {successCount > 0 && (
                  <div className="flex items-center gap-1 text-green-600">
                    <CheckCircle className="h-4 w-4" />
                    <span>{successCount} exitosos</span>
                  </div>
                )}
                {errorCount > 0 && (
                  <div className="flex items-center gap-1 text-red-600">
                    <AlertCircle className="h-4 w-4" />
                    <span>{errorCount} errores</span>
                  </div>
                )}
              </div>
              
              {!isGenerating && (
                <div className="text-gray-600">
                  {successCount > 0 ? 'Listo para descargar' : 'Intenta de nuevo'}
                </div>
              )}
            </div>

            {/* Error Details (if any) */}
            {errors.length > 0 && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md"
              >
                <h4 className="text-sm font-medium text-red-800 mb-2">
                  Errores en la generación:
                </h4>
                <ul className="text-xs text-red-700 space-y-1">
                  {errors.slice(0, 3).map((error, index) => (
                    <li key={index}>
                      Variación {error.index + 1}: {error.error}
                    </li>
                  ))}
                  {errors.length > 3 && (
                    <li className="text-red-600">
                      +{errors.length - 3} errores más...
                    </li>
                  )}
                </ul>
              </motion.div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
}

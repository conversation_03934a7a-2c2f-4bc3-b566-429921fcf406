/**
 * Simplified Ad Creator - Results-Focused Flow
 * Clean 3-step process: Size → Content → Generate
 */

import { useState, useRef } from "react";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, Upload, Wand2, Download, Heart, Sparkles } from "lucide-react";

// Import shared types and services
import { generateAd, editWithReferences, type AdGenerationOptions } from "@/lib/services/ad-generation";
import { validateImageFile } from "@/lib/utils/file-validation";
import { useSavedAds } from "@/hooks/use-saved-ads";
import { GeneratedAd, PlatformConfig } from "@/types/ad-creator-types";

// Import error handling
import { handleAdError, validatePrompt, validateImageFiles } from "@/lib/utils/ad-error-handling";

interface GenericAdEditorProps {
  config: PlatformConfig;
  platformKey: string;
}

export function GenericAdEditor({ config, platformKey }: GenericAdEditorProps) {
  const { toast } = useToast();
  
  // Estados principales
  const [currentAd, setCurrentAd] = useState<GeneratedAd | null>(null);
  const [prompt, setPrompt] = useState("");
  const [size, setSize] = useState(config.defaultSize);
  const [isGenerating, setIsGenerating] = useState(false);

  // Estados para edición con referencias
  const [useProductImages, setUseProductImages] = useState(false);
  const [productImages, setProductImages] = useState<File[]>([]);
  const productInputRef = useRef<HTMLInputElement>(null);

  // Estados para tabs y guardados
  const [mainTab, setMainTab] = useState<MainTab>("latest");
  const [generatedAdsCount, setGeneratedAdsCount] = useState(0);

  // Hook para anuncios guardados
  const { savedAds, saveAd, removeAd } = useSavedAds();

  // Funciones de manejo de archivos
  const handleProductImageUpload = (files: FileList | null) => {
    if (!files) return;

    const validFiles: File[] = [];
    Array.from(files).forEach(file => {
      const validation = validateImageFile(file);
      if (validation.valid) {
        validFiles.push(file);
      } else {
        toast({
          title: "Archivo no válido",
          description: validation.error || `${file.name} no es un formato de imagen válido`,
          variant: "destructive",
        });
      }
    });

    if (validFiles.length > 0) {
      setProductImages(prev => [...prev, ...validFiles].slice(0, 3));
    }
  };

  const removeProductImage = (index: number) => {
    setProductImages(prev => prev.filter((_, i) => i !== index));
  };

  // Función principal de generación
  const handleGenerateAd = async () => {
    // Validate prompt
    const promptValidation = validatePrompt(prompt);
    if (!promptValidation.valid) {
      handleAdError(promptValidation.error, toast);
      return;
    }

    // Validate images if using product images
    if (useProductImages) {
      const imageValidation = validateImageFiles(productImages);
      if (!imageValidation.valid) {
        handleAdError(imageValidation.error, toast);
        return;
      }
    }

    setIsGenerating(true);

    try {
      const enhancedPrompt = config.promptPrefix + prompt;

      // Si hay imágenes de producto, usar edición con referencias
      if (useProductImages && productImages.length > 0) {
        const result = await editWithReferences({
          prompt: enhancedPrompt,
          referenceImages: productImages,
          size
        });

        if (result.success && result.image_url) {
          const newAd: GeneratedAd = {
            id: Date.now().toString(),
            image_url: result.image_url,
            prompt: prompt,
            revised_prompt: result.revised_prompt,
            response_id: result.response_id,
            metadata: {
              usedProductImages: true,
              productImageCount: productImages.length,
              size,
              platform: platformKey
            },
            timestamp: Date.now()
          };

          setCurrentAd(newAd);
          setGeneratedAdsCount(prev => prev + 1);
          setMainTab("latest");

          toast({
            title: "¡Anuncio generado con producto!",
            description: `Tu anuncio para ${config.name} ha sido creado con tus imágenes de producto`,
          });
        } else {
          throw new Error(result.error || "Error desconocido");
        }
      } else {
        // Generación normal sin referencias
        const options: AdGenerationOptions = {
          prompt: enhancedPrompt,
          size
        };

        const result = await generateAd(options);

        if (result.success && result.image_url) {
          const newAd: GeneratedAd = {
            id: Date.now().toString(),
            image_url: result.image_url,
            prompt: prompt,
            revised_prompt: result.revised_prompt,
            response_id: result.response_id,
            metadata: {
              usedProductImages: false,
              size,
              platform: platformKey
            },
            timestamp: Date.now()
          };

          setCurrentAd(newAd);
          setGeneratedAdsCount(prev => prev + 1);
          setMainTab("latest");

          toast({
            title: "¡Anuncio generado!",
            description: `Tu anuncio para ${config.name} ha sido creado exitosamente`,
          });
        } else {
          throw new Error(result.error || "Error desconocido");
        }
      }
    } catch (error) {
      console.error("Error generating ad:", error);
      handleAdError(error, toast);
    } finally {
      setIsGenerating(false);
    }
  };

  // Funciones de utilidad
  const handleSaveAd = (ad: GeneratedAd) => {
    saveAd({
      ...ad,
      isFavorite: true,
      platform: platformKey
    });
    toast({
      title: "Anuncio guardado",
      description: "El anuncio ha sido guardado en tu colección",
    });
  };

  const handleDownload = (imageUrl: string) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `${platformKey}-ad-${Date.now()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: "Descarga iniciada",
      description: "Tu anuncio se está descargando",
    });
  };

  const getStats = () => ({
    total: savedAds.length
  });

  const handleBack = () => {
    window.history.back();
  };

  const handleSizeChange = (newSize: string) => {
    if (config.sizes.includes(newSize)) {
      setSize(newSize);
    }
  };

  const handleSizeRecommendation = (recommendedSize: string) => {
    if (config.sizes.includes(recommendedSize)) {
      setSize(recommendedSize);
      toast({
        title: "Tamaño recomendado por Emma",
        description: `He cambiado el tamaño a ${recommendedSize} para mejores resultados`,
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header */}
      <AdHeader onBack={handleBack} config={config} />

      {/* Contenido principal */}
      <div className="max-w-[1600px] mx-auto px-4 md:px-6 py-4">
        <div className="space-y-4">
          {/* Preview principal */}
          <AdPreview
            currentAd={currentAd}
            isGenerating={isGenerating}
            mainTab={mainTab}
            savedAds={savedAds}
            productImages={productImages}
            useProductImages={useProductImages}
            onTabChange={setMainTab}
            onSaveAd={handleSaveAd}
            onDownload={handleDownload}
            onRemoveAd={removeAd}
            getStats={getStats}
            config={config}
          />

          {/* Paneles laterales */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Controles */}
            <AdControls
              prompt={prompt}
              size={size}
              isGenerating={isGenerating}
              useProductImages={useProductImages}
              productImages={productImages}
              onPromptChange={setPrompt}
              onSizeChange={handleSizeChange}
              onUseProductImagesChange={setUseProductImages}
              onProductImageUpload={handleProductImageUpload}
              onRemoveProductImage={removeProductImage}
              onGenerate={handleGenerateAd}
              productInputRef={productInputRef}
              config={config}
            />

            {/* Especificaciones */}
            <AdSpecs config={config} />

            {/* Herramientas de IA */}
            <AdTools
              prompt={prompt}
              onPromptEnhance={setPrompt}
              onStyleApply={(style: string) => {
                toast({
                  title: "Estilo aplicado",
                  description: `Estilo ${style} aplicado al anuncio`,
                });
              }}
              toast={toast}
              platform={platformKey}
            />
          </div>
        </div>
      </div>

      {/* Emma AI Assistant */}
      <EmmaAdAssistant
        platform={platformKey}
        currentPrompt={prompt}
        onPromptSuggestion={(suggestedPrompt) => {
          setPrompt(suggestedPrompt);
          toast({
            title: "Prompt sugerido por Emma",
            description: "He actualizado tu descripción con una sugerencia optimizada",
          });
        }}
        onSizeRecommendation={handleSizeRecommendation}
      />
    </div>
  );
}

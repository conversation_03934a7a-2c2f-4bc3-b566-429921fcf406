/**
 * Loading States Components
 * Professional loading screens with Emma branding and progress indicators
 */

import { motion } from "framer-motion";
import { Loader2, <PERSON><PERSON><PERSON>, Wand2, Image as ImageIcon } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface LoadingStateProps {
  message?: string;
  progress?: number;
  showProgress?: boolean;
  variant?: "generation" | "upload" | "processing" | "saving";
}

export function AdGenerationLoading({ 
  message = "Generando tu anuncio...", 
  progress = 0,
  showProgress = false 
}: LoadingStateProps) {
  return (
    <div className="flex flex-col items-center justify-center p-8 space-y-6">
      {/* Emma Logo Animation */}
      <motion.div
        animate={{ 
          scale: [1, 1.1, 1],
          rotate: [0, 5, -5, 0]
        }}
        transition={{ 
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="relative"
      >
        <div className="w-16 h-16 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full flex items-center justify-center">
          <Wand2 className="w-8 h-8 text-white" />
        </div>
        <motion.div
          animate={{ scale: [1, 1.3, 1], opacity: [0.7, 0.3, 0.7] }}
          transition={{ duration: 1.5, repeat: Infinity }}
          className="absolute inset-0 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full"
        />
      </motion.div>

      {/* Loading Message */}
      <div className="text-center space-y-2">
        <motion.h3
          animate={{ opacity: [0.7, 1, 0.7] }}
          transition={{ duration: 1.5, repeat: Infinity }}
          className="text-lg font-semibold text-gray-800"
        >
          {message}
        </motion.h3>
        <p className="text-sm text-gray-600">
          Emma está creando tu anuncio profesional
        </p>
      </div>

      {/* Progress Bar */}
      {showProgress && (
        <div className="w-full max-w-xs space-y-2">
          <Progress value={progress} className="h-2" />
          <p className="text-xs text-center text-gray-500">
            {progress}% completado
          </p>
        </div>
      )}

      {/* Floating Sparkles */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [0, -20, 0],
              x: [0, Math.random() * 20 - 10, 0],
              opacity: [0, 1, 0],
              scale: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: i * 0.5,
              ease: "easeInOut"
            }}
            className="absolute"
            style={{
              left: `${20 + i * 15}%`,
              top: `${30 + (i % 2) * 20}%`
            }}
          >
            <Sparkles className="w-4 h-4 text-[#3018ef]" />
          </motion.div>
        ))}
      </div>
    </div>
  );
}

export function ImageUploadLoading({ message = "Subiendo imágenes..." }: LoadingStateProps) {
  return (
    <Card className="w-full">
      <CardContent className="p-6">
        <div className="flex items-center space-x-4">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          >
            <Loader2 className="w-6 h-6 text-[#3018ef]" />
          </motion.div>
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-800">{message}</p>
            <p className="text-xs text-gray-600">Procesando tus imágenes de referencia</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function ProcessingLoading({ message = "Procesando..." }: LoadingStateProps) {
  return (
    <div className="flex items-center justify-center p-4">
      <div className="flex items-center space-x-3">
        <motion.div
          animate={{ 
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{ 
            duration: 1.5, 
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <ImageIcon className="w-5 h-5 text-[#3018ef]" />
        </motion.div>
        <span className="text-sm text-gray-700">{message}</span>
      </div>
    </div>
  );
}

export function SavingLoading({ message = "Guardando anuncio..." }: LoadingStateProps) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
    >
      <Card className="w-80">
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-12 h-12 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full flex items-center justify-center mx-auto"
            >
              <Sparkles className="w-6 h-6 text-white" />
            </motion.div>
            <div>
              <h3 className="text-lg font-semibold text-gray-800">{message}</h3>
              <p className="text-sm text-gray-600 mt-1">
                Esto solo tomará un momento
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

// Loading skeleton for ad preview
export function AdPreviewSkeleton() {
  return (
    <div className="space-y-4">
      <Card>
        <CardContent className="p-4">
          <div className="aspect-square bg-gray-200 rounded-lg animate-pulse" />
          <div className="mt-4 space-y-2">
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
            <div className="h-3 bg-gray-200 rounded w-3/4 animate-pulse" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Loading skeleton for controls
export function AdControlsSkeleton() {
  return (
    <div className="space-y-4">
      <Card>
        <CardContent className="p-4 space-y-4">
          <div className="h-4 bg-gray-200 rounded animate-pulse" />
          <div className="h-20 bg-gray-200 rounded animate-pulse" />
          <div className="h-10 bg-gray-200 rounded animate-pulse" />
        </CardContent>
      </Card>
    </div>
  );
}

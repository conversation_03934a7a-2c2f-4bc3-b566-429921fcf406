/**
 * Simplified Ad Creator - Main Orchestrator
 * Clean 3-step process: Size → Content → Generate
 * Refactored following Single Responsibility Principle
 */

import { useState } from "react";
import { PlatformConfig } from "@/types/ad-creator-types";
import { useStepNavigation } from "./hooks/useStepNavigation";
import { useAdGeneration } from "./hooks/useAdGeneration";
import { SizeSelectionStep } from "./steps/SizeSelectionStep";
import { ContentCreationStep } from "./steps/ContentCreationStep";
import { ResultsStep } from "./steps/ResultsStep";
import { ProgressHeader } from "./components/ProgressHeader";
import EmmaAdAssistant from "@/components/tools/ad-creator/EmmaAdAssistant";

interface SimplifiedAdCreatorProps {
  platform: string;
  config: PlatformConfig;
  onBack: () => void;
}

export function SimplifiedAdCreator({ platform, config, onBack }: SimplifiedAdCreatorProps) {
  // Step navigation hook
  const {
    currentStep,
    selectedSize,
    setSelectedSize,
    goToStep,
    canProceedToContent,
    canProceedToResults
  } = useStepNavigation();

  // Ad generation hook
  const {
    prompt,
    setPrompt,
    uploadedImage,
    setUploadedImage,
    headline,
    setHeadline,
    punchline,
    setPunchline,
    cta,
    setCta,
    generatedAds,
    isGenerating,
    generationProgress,
    estimatedTime,
    handleGenerate,
    handleSave,
    handleDownload
  } = useAdGeneration(platform, config, selectedSize);

  // Handle step transitions
  const handleSizeSelected = (size: any) => {
    setSelectedSize(size);
    goToStep("content");
  };

  const handleContentComplete = () => {
    handleGenerate();
    goToStep("results");
  };

  const handleGenerateMore = () => {
    goToStep("content");
  };

  const handleBackToEdit = () => {
    goToStep("size");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100">
      {/* Progress Header */}
      <ProgressHeader
        config={config}
        currentStep={currentStep}
        selectedSize={selectedSize}
        prompt={prompt}
        generatedAds={generatedAds}
        onBack={onBack}
      />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Step 1: Size Selection */}
        {currentStep === "size" && (
          <SizeSelectionStep
            platform={platform}
            config={config}
            onSizeSelected={handleSizeSelected}
          />
        )}

        {/* Step 2: Content Creation */}
        {currentStep === "content" && (
          <ContentCreationStep
            platform={platform}
            config={config}
            selectedSize={selectedSize}
            prompt={prompt}
            setPrompt={setPrompt}
            uploadedImage={uploadedImage}
            setUploadedImage={setUploadedImage}
            headline={headline}
            setHeadline={setHeadline}
            punchline={punchline}
            setPunchline={setPunchline}
            cta={cta}
            setCta={setCta}
            onGenerate={handleContentComplete}
            isGenerating={isGenerating}
          />
        )}

        {/* Step 3: Results */}
        {currentStep === "results" && (
          <ResultsStep
            generatedAds={generatedAds}
            isGenerating={isGenerating}
            generationProgress={generationProgress}
            estimatedTime={estimatedTime}
            onSave={handleSave}
            onDownload={handleDownload}
            onGenerateMore={handleGenerateMore}
            onBackToEdit={handleBackToEdit}
          />
        )}
      </div>

      {/* Emma AI Assistant - Only in content step */}
      {currentStep === "content" && (
        <EmmaAdAssistant
          platform={platform}
          currentPrompt={prompt}
          currentHeadline={headline}
          currentPunchline={punchline}
          currentCta={cta}
          onPromptSuggestion={setPrompt}
          onHeadlineSuggestion={setHeadline}
          onPunchlineSuggestion={setPunchline}
          onCtaSuggestion={setCta}
          onSizeRecommendation={(size) => {
            // Find matching size and update if needed
            // This will be handled in the hook
          }}
        />
      )}
    </div>
  );
}

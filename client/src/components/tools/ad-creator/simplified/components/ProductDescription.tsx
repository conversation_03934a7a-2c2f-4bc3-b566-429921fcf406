/**
 * Product Description Component
 * Enhanced textarea for product description with character counting
 */

interface ProductDescriptionProps {
  prompt: string;
  onPromptChange: (prompt: string) => void;
}

export function ProductDescription({ prompt, onPromptChange }: ProductDescriptionProps) {
  return (
    <div className="bg-gradient-to-br from-white to-slate-50 backdrop-blur-xl p-6 rounded-2xl border border-slate-200/60 shadow-lg hover:shadow-xl transition-all duration-300">
      <div className="flex items-center gap-3 mb-4">
        <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-sm font-bold">📝</span>
        </div>
        <label className="text-lg font-bold text-slate-900">
          Descripción del producto
        </label>
      </div>
      <div className="relative">
        <textarea
          value={prompt}
          onChange={(e) => onPromptChange(e.target.value)}
          placeholder="Describe tu producto o servicio en detalle. Incluye características, beneficios y público objetivo..."
          className="w-full h-32 p-4 border-2 border-slate-200 rounded-xl focus:ring-4 focus:ring-purple-500/20 focus:border-purple-500 transition-all duration-300 resize-none text-base bg-white/80 backdrop-blur-sm hover:border-slate-300"
          maxLength={300}
        />
        <div className={`absolute bottom-3 right-3 text-xs px-2 py-1 rounded-full ${
          prompt.length > 250 ? 'bg-red-100 text-red-600' :
          prompt.length > 150 ? 'bg-yellow-100 text-yellow-600' :
          'bg-green-100 text-green-600'
        }`}>
          {prompt.length}/300
        </div>
      </div>
      <div className="mt-3 flex items-center gap-2 text-sm text-slate-600">
        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
        Mientras más detalles proporciones, mejor será el resultado
      </div>
    </div>
  );
}

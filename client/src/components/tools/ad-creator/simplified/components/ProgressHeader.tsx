/**
 * Progress Header Component
 * Shows current step progress and navigation for the simplified ad creator
 */

import { Arrow<PERSON><PERSON><PERSON>, Check, Sparkles } from "lucide-react";
import { PlatformConfig, GeneratedAd } from "@/types/ad-creator-types";
import { Step, SizeOption } from "../types/simplified-ad-types";

interface ProgressHeaderProps {
  config: PlatformConfig;
  currentStep: Step;
  selectedSize: SizeOption | null;
  prompt: string;
  generatedAds: GeneratedAd[];
  onBack: () => void;
}

export function ProgressHeader({
  config,
  currentStep,
  selectedSize,
  prompt,
  generatedAds,
  onBack
}: ProgressHeaderProps) {
  return (
    <div className="bg-white/80 backdrop-blur-xl border-b border-white/20 px-6 py-6 sticky top-0 z-50">
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        <div className="flex items-center gap-6">
          <button
            onClick={onBack}
            className="p-3 hover:bg-white/60 rounded-xl transition-all duration-300 hover:scale-105 backdrop-blur-sm border border-white/20"
          >
            <ArrowLeft className="w-5 h-5 text-slate-700" />
          </button>
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
              Crear Anuncio para {config.name}
            </h1>
            <p className="text-sm text-slate-600 font-medium">
              ✨ Proceso simplificado con IA avanzada
            </p>
          </div>
        </div>

        {/* Premium Progress Steps */}
        <div className="flex items-center gap-3">
          <div className={`flex items-center gap-3 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
            currentStep === "size" ? "bg-gradient-to-r from-[#3018ef] to-[#4c51bf] text-white shadow-lg shadow-blue-500/25" :
            selectedSize ? "bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/25" : "bg-white/60 text-slate-500 backdrop-blur-sm border border-white/20"
          }`}>
            {selectedSize ? <Check className="w-4 h-4" /> : <span className="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center text-xs">1</span>}
            Tamaño
          </div>
          <div className={`flex items-center gap-3 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
            currentStep === "content" ? "bg-gradient-to-r from-[#3018ef] to-[#4c51bf] text-white shadow-lg shadow-blue-500/25" :
            prompt ? "bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/25" : "bg-white/60 text-slate-500 backdrop-blur-sm border border-white/20"
          }`}>
            {prompt ? <Check className="w-4 h-4" /> : <span className="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center text-xs">2</span>}
            Contenido
          </div>
          <div className={`flex items-center gap-3 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
            currentStep === "results" ? "bg-gradient-to-r from-[#3018ef] to-[#4c51bf] text-white shadow-lg shadow-blue-500/25" : "bg-white/60 text-slate-500 backdrop-blur-sm border border-white/20"
          }`}>
            {generatedAds.length > 0 ? <Check className="w-4 h-4" /> : <span className="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center text-xs">3</span>}
            Generar
          </div>
        </div>
      </div>
    </div>
  );
}

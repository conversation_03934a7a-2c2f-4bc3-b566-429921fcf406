/**
 * Size Selector Component
 * Premium size selection interface with platform-specific options
 */

import { Sparkles } from "lucide-react";
import { PlatformConfig } from "@/types/ad-creator-types";
import { SizeOption } from "../types/simplified-ad-types";

// Size options for each platform
const PLATFORM_SIZES = {
  facebook: [
    { name: "Post Cuadrado", dimensions: "1080x1080", desc: "Ideal para feed", icon: "📱" },
    { name: "Historia", dimensions: "1080x1920", desc: "Stories verticales", icon: "📲" },
    { name: "Portada", dimensions: "1200x628", desc: "Anuncios de enlace", icon: "🖼️" }
  ],
  instagram: [
    { name: "Post Cuadrado", dimensions: "1080x1080", desc: "Feed principal", icon: "📱" },
    { name: "Historia", dimensions: "1080x1920", desc: "Stories", icon: "📲" },
    { name: "<PERSON><PERSON><PERSON>", dimensions: "1080x1080", desc: "<PERSON><PERSON><PERSON>les imágenes", icon: "🎠" }
  ],
  linkedin: [
    { name: "Post Cuadrado", dimensions: "1080x1080", desc: "Feed profesional", icon: "💼" },
    { name: "Banner", dimensions: "1200x628", desc: "Anuncios patrocinados", icon: "🖼️" },
    { name: "Historia", dimensions: "1080x1920", desc: "LinkedIn Stories", icon: "📲" }
  ],
  google: [
    { name: "Banner Mediano", dimensions: "300x250", desc: "Display estándar", icon: "🖥️" },
    { name: "Leaderboard", dimensions: "728x90", desc: "Banner superior", icon: "📊" },
    { name: "Rascacielos", dimensions: "160x600", desc: "Banner lateral", icon: "🏢" }
  ]
};

interface SizeSelectorProps {
  platform: string;
  config: PlatformConfig;
  onSizeSelected: (size: SizeOption) => void;
}

export function SizeSelector({ platform, config, onSizeSelected }: SizeSelectorProps) {
  // Get platform sizes
  const platformSizes = PLATFORM_SIZES[platform as keyof typeof PLATFORM_SIZES] || PLATFORM_SIZES.facebook;

  return (
    <div className="space-y-12">
      <div className="text-center">
        <h2 className="text-4xl font-bold mb-4">
          <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
            Selecciona el Tamaño Creativo
          </span>
        </h2>
        <p className="text-xl text-slate-600 max-w-2xl mx-auto leading-relaxed">
          Elige el formato perfecto para maximizar el impacto de tu anuncio
        </p>
        <div className="mt-6 inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-50 to-purple-50 rounded-full border border-blue-200/50">
          <Sparkles className="w-4 h-4 text-[#3018ef]" />
          <span className="text-sm font-medium text-slate-700">Optimizado para {config.name}</span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
        {platformSizes.map((size, index) => (
          <button
            key={index}
            onClick={() => onSizeSelected(size)}
            className="group relative p-8 bg-white/70 backdrop-blur-xl rounded-2xl border border-white/20 hover:border-[#3018ef]/30 hover:shadow-2xl hover:shadow-[#3018ef]/10 transition-all duration-500 hover:scale-105 hover:-translate-y-2"
          >
            {/* Premium gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef]/5 to-[#dd3a5a]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

            {/* Content */}
            <div className="relative z-10">
              <div className="text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300">
                {size.icon}
              </div>
              <h3 className="text-xl font-bold text-slate-900 mb-3 group-hover:text-[#3018ef] transition-colors">
                {size.name}
              </h3>
              <p className="text-slate-600 mb-4 leading-relaxed">
                {size.desc}
              </p>
              <div className="inline-flex items-center gap-2 px-3 py-1 bg-slate-100 rounded-full">
                <span className="text-xs font-mono text-slate-700">{size.dimensions}</span>
              </div>
            </div>

            {/* Premium selection indicator */}
            <div className="absolute top-4 right-4 w-6 h-6 rounded-full border-2 border-slate-300 group-hover:border-[#3018ef] group-hover:bg-[#3018ef] transition-all duration-300">
              <div className="w-full h-full rounded-full bg-[#3018ef] scale-0 group-hover:scale-50 transition-transform duration-300" />
            </div>
          </button>
        ))}
      </div>
    </div>
  );
}

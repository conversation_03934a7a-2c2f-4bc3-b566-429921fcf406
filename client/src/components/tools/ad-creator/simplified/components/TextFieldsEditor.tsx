/**
 * Text Fields Editor Component
 * Manages headline, punchline, and CTA fields with AI generation
 */

import { Check, Wand2 } from "lucide-react";
import { useTextGeneration } from "../hooks/useTextGeneration";

interface TextFieldsEditorProps {
  platform: string;
  prompt: string;
  headline: string;
  punchline: string;
  cta: string;
  onHeadlineChange: (headline: string) => void;
  onPunchlineChange: (punchline: string) => void;
  onCtaChange: (cta: string) => void;
}

export function TextFieldsEditor({
  platform,
  prompt,
  headline,
  punchline,
  cta,
  onHeadlineChange,
  onPunchlineChange,
  onCtaChange
}: TextFieldsEditorProps) {
  const { textStates, handleGenerateHeadline, handleGeneratePunchline, handleGenerateCta } = useTextGeneration(platform, prompt);

  // Handle AI generation with state update
  const handleHeadlineGeneration = async () => {
    try {
      const newHeadline = await handleGenerateHeadline();
      onHeadlineChange(newHeadline);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handlePunchlineGeneration = async () => {
    try {
      const newPunchline = await handleGeneratePunchline();
      onPunchlineChange(newPunchline);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleCtaGeneration = async () => {
    try {
      const newCta = await handleGenerateCta();
      onCtaChange(newCta);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleClearAll = () => {
    onHeadlineChange("");
    onPunchlineChange("");
    onCtaChange("");
  };

  return (
    <div className="bg-gradient-to-br from-white to-slate-50 backdrop-blur-xl p-6 rounded-2xl border border-slate-200/60 shadow-lg hover:shadow-xl transition-all duration-300">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-r from-[#dd3a5a] to-[#e55a7a] rounded-lg flex items-center justify-center">
            <span className="text-white text-sm font-bold">T</span>
          </div>
          <h4 className="text-xl font-bold text-slate-900">On Image Texts</h4>
        </div>
        <button
          onClick={handleClearAll}
          className="text-slate-400 hover:text-slate-600 text-sm flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-slate-100 transition-colors"
        >
          🧹 Clean Texts
        </button>
      </div>

      <div className="space-y-5">
        {/* Main Headline */}
        <div className="group">
          <div className="flex items-center justify-between mb-3">
            <label className="text-sm font-medium text-slate-700 flex items-center gap-2">
              <div className="w-2 h-2 bg-[#3018ef] rounded-full"></div>
              Your main headline here!
            </label>
            <div className="flex items-center gap-3">
              <span className={`text-xs px-2 py-1 rounded-full ${
                headline.length > 35 ? 'bg-red-100 text-red-600' :
                headline.length > 25 ? 'bg-yellow-100 text-yellow-600' :
                'bg-green-100 text-green-600'
              }`}>
                {headline.length}/40
              </span>
              <button
                onClick={handleHeadlineGeneration}
                disabled={!prompt.trim() || textStates.isGeneratingHeadline}
                className="w-8 h-8 bg-gradient-to-r from-[#3018ef] to-[#4c51bf] hover:from-[#2516d6] hover:to-[#3d4db7] rounded-lg flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-110 shadow-md"
              >
                {textStates.isGeneratingHeadline ? (
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                ) : (
                  <Wand2 className="w-4 h-4 text-white" />
                )}
              </button>
            </div>
          </div>
          <div className="relative">
            <input
              type="text"
              value={headline}
              onChange={(e) => onHeadlineChange(e.target.value)}
              placeholder="Your main headline here!"
              className="w-full p-4 border-2 border-slate-200 rounded-xl focus:ring-4 focus:ring-[#3018ef]/20 focus:border-[#3018ef] transition-all duration-300 text-base font-medium bg-white/80 backdrop-blur-sm hover:border-slate-300"
              maxLength={40}
            />
            {headline && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <Check className="w-5 h-5 text-green-500" />
              </div>
            )}
          </div>
        </div>

        {/* Punchline */}
        <div className="group">
          <div className="flex items-center justify-between mb-3">
            <label className="text-sm font-medium text-slate-700 flex items-center gap-2">
              <div className="w-2 h-2 bg-[#dd3a5a] rounded-full"></div>
              Your punchline is here!
            </label>
            <div className="flex items-center gap-3">
              <span className={`text-xs px-2 py-1 rounded-full ${
                punchline.length > 35 ? 'bg-red-100 text-red-600' :
                punchline.length > 25 ? 'bg-yellow-100 text-yellow-600' :
                'bg-green-100 text-green-600'
              }`}>
                {punchline.length}/40
              </span>
              <button
                onClick={handlePunchlineGeneration}
                disabled={!prompt.trim() || textStates.isGeneratingPunchline}
                className="w-8 h-8 bg-gradient-to-r from-[#dd3a5a] to-[#e55a7a] hover:from-[#c73650] hover:to-[#d94d6e] rounded-lg flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-110 shadow-md"
              >
                {textStates.isGeneratingPunchline ? (
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                ) : (
                  <Wand2 className="w-4 h-4 text-white" />
                )}
              </button>
            </div>
          </div>
          <div className="relative">
            <input
              type="text"
              value={punchline}
              onChange={(e) => onPunchlineChange(e.target.value)}
              placeholder="Your punchline is here!"
              className="w-full p-4 border-2 border-slate-200 rounded-xl focus:ring-4 focus:ring-[#dd3a5a]/20 focus:border-[#dd3a5a] transition-all duration-300 text-base font-medium bg-white/80 backdrop-blur-sm hover:border-slate-300"
              maxLength={40}
            />
            {punchline && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <Check className="w-5 h-5 text-green-500" />
              </div>
            )}
          </div>
        </div>

        {/* CTA */}
        <div className="group">
          <div className="flex items-center justify-between mb-3">
            <label className="text-sm font-medium text-slate-700 flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              Call to action text here!
            </label>
            <div className="flex items-center gap-3">
              <span className="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded-full">Optional</span>
              <span className={`text-xs px-2 py-1 rounded-full ${
                cta.length > 20 ? 'bg-red-100 text-red-600' :
                cta.length > 15 ? 'bg-yellow-100 text-yellow-600' :
                'bg-green-100 text-green-600'
              }`}>
                {cta.length}/25
              </span>
              <button
                onClick={handleCtaGeneration}
                disabled={!prompt.trim() || textStates.isGeneratingCta}
                className="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 rounded-lg flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-110 shadow-md"
              >
                {textStates.isGeneratingCta ? (
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                ) : (
                  <Wand2 className="w-4 h-4 text-white" />
                )}
              </button>
            </div>
          </div>
          <div className="relative">
            <input
              type="text"
              value={cta}
              onChange={(e) => onCtaChange(e.target.value)}
              placeholder="Call to action text here!"
              className="w-full p-4 border-2 border-slate-200 rounded-xl focus:ring-4 focus:ring-green-500/20 focus:border-green-500 transition-all duration-300 text-base font-medium bg-white/80 backdrop-blur-sm hover:border-slate-300"
              maxLength={25}
            />
            {cta && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <Check className="w-5 h-5 text-green-500" />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

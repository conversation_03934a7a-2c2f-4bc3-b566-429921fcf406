/**
 * Ad Generation Hook
 * Manages ad generation logic and state for the simplified ad creator
 */

import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { useSavedAds } from "@/hooks/use-saved-ads";
import { generateAdVariationsParallel, estimateGenerationTime, validateGenerationOptions } from "@/lib/services/parallel-ad-generation";
import { type AdGenerationOptions } from "@/lib/services/ad-generation";
import { GeneratedAd, PlatformConfig } from "@/types/ad-creator-types";
import { GenerationProgress, SizeOption } from "../types/simplified-ad-types";

export function useAdGeneration(platform: string, config: PlatformConfig, selectedSize: SizeOption | null) {
  // Content state
  const [prompt, setPrompt] = useState("");
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [headline, setHeadline] = useState("");
  const [punchline, setPunchline] = useState("");
  const [cta, setCta] = useState("");

  // Generation state
  const [generatedAds, setGeneratedAds] = useState<GeneratedAd[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState<GenerationProgress>({
    completed: 0,
    total: 0,
    errors: []
  });
  const [estimatedTime, setEstimatedTime] = useState<number>(0);

  // Hooks
  const { toast } = useToast();
  const { saveAd } = useSavedAds();

  // Generate ads with parallel processing
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Descripción requerida",
        description: "Por favor describe tu producto o servicio",
        variant: "destructive",
      });
      return;
    }

    if (!selectedSize) {
      toast({
        title: "Tamaño requerido",
        description: "Por favor selecciona un tamaño",
        variant: "destructive",
      });
      return;
    }

    // Validate generation options
    const baseOptions: AdGenerationOptions = {
      prompt: `${config.promptPrefix || ""} ${prompt}`,
      size: selectedSize.dimensions,
      headline: headline.trim() || undefined,
      punchline: punchline.trim() || undefined,
      cta: cta.trim() || undefined,
      referenceImage: uploadedImage || undefined,
    };

    const validationResult = validateGenerationOptions({
      baseOptions,
      variationCount: 6,
      maxConcurrent: 3
    });

    if (!validationResult.valid) {
      toast({
        title: "Configuración inválida",
        description: validationResult.errors[0],
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    setGeneratedAds([]); // Clear previous results

    // Set initial progress state
    setGenerationProgress({
      completed: 0,
      total: 6,
      errors: []
    });

    // Estimate generation time
    const estimatedTimeSeconds = estimateGenerationTime(6, 3);
    setEstimatedTime(estimatedTimeSeconds);

    try {
      const result = await generateAdVariationsParallel({
        baseOptions,
        variationCount: 6,
        maxConcurrent: 3,
        onProgress: (completed, total, variation) => {
          setGenerationProgress(prev => ({
            ...prev,
            completed,
            total
          }));

          // Add new variation to the list as it completes
          if (variation) {
            setGeneratedAds(prev => [...prev, variation]);
          }
        },
        onError: (error, variationIndex) => {
          setGenerationProgress(prev => ({
            ...prev,
            errors: [...prev.errors, { index: variationIndex, error: error.message }]
          }));
        }
      });

      if (result.success && result.variations.length > 0) {
        setGeneratedAds(result.variations);
        toast({
          title: "¡Anuncios generados!",
          description: `Se generaron ${result.totalGenerated} de ${result.totalRequested} variaciones exitosamente.`,
        });
      } else {
        throw new Error("No se pudo generar ningún anuncio");
      }
    } catch (error) {
      console.error("Error generating ads:", error);
      toast({
        title: "Error al generar",
        description: "Hubo un problema generando los anuncios. Intenta de nuevo.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Save ad
  const handleSave = (ad: GeneratedAd) => {
    saveAd({ ...ad, isFavorite: false });
    toast({
      title: "Anuncio guardado",
      description: "El anuncio se guardó en tu colección.",
    });
  };

  // Download ad
  const handleDownload = (imageUrl: string, adId: string) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `${platform}-ad-${adId}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: "Descarga iniciada",
      description: "Tu anuncio se está descargando",
    });
  };

  return {
    // Content state
    prompt,
    setPrompt,
    uploadedImage,
    setUploadedImage,
    headline,
    setHeadline,
    punchline,
    setPunchline,
    cta,
    setCta,
    // Generation state
    generatedAds,
    isGenerating,
    generationProgress,
    estimatedTime,
    // Actions
    handleGenerate,
    handleSave,
    handleDownload
  };
}

/**
 * Step Navigation Hook
 * Manages step transitions and validation for the simplified ad creator
 */

import { useState } from "react";
import { Step, SizeOption } from "../types/simplified-ad-types";

export function useStepNavigation() {
  const [currentStep, setCurrentStep] = useState<Step>("size");
  const [selectedSize, setSelectedSize] = useState<SizeOption | null>(null);

  // Navigation functions
  const goToStep = (step: Step) => {
    setCurrentStep(step);
  };

  // Validation functions
  const canProceedToContent = selectedSize !== null;
  const canProceedToResults = canProceedToContent; // Add more validation as needed

  return {
    currentStep,
    selectedSize,
    setSelectedSize,
    goToStep,
    canProceedToContent,
    canProceedToResults
  };
}

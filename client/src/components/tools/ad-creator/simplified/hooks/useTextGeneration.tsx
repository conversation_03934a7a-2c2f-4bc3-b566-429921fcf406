/**
 * Text Generation Hook
 * Manages AI text generation for headlines, punchlines, and CTAs
 */

import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { TextGenerationStates } from "../types/simplified-ad-types";

export function useTextGeneration(platform: string, prompt: string) {
  const [textStates, setTextStates] = useState<TextGenerationStates>({
    isGeneratingHeadline: false,
    isGeneratingPunchline: false,
    isGeneratingCta: false
  });

  const { toast } = useToast();

  // Generic text generation function
  const generateText = async (fieldType: "headline" | "punchline" | "cta"): Promise<string> => {
    if (!prompt.trim()) {
      toast({
        title: "Descripción requerida",
        description: `Por favor describe tu producto antes de generar ${fieldType}`,
        variant: "destructive",
      });
      throw new Error("Prompt required");
    }

    const response = await fetch("/api/v1/ad-creator-agent/generate-field", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        field_type: fieldType,
        platform: platform,
        product_description: prompt
      }),
    });

    if (!response.ok) {
      throw new Error(`Error generating ${fieldType}: ${response.status}`);
    }

    const result = await response.json();
    return result.content || "";
  };

  // Generate headline
  const handleGenerateHeadline = async (): Promise<string> => {
    setTextStates(prev => ({ ...prev, isGeneratingHeadline: true }));
    try {
      const content = await generateText("headline");
      toast({
        title: "Headline generado",
        description: "Se ha generado un nuevo headline con IA",
      });
      return content;
    } catch (error) {
      console.error("Error generating headline:", error);
      toast({
        title: "Error al generar",
        description: "Hubo un problema generando el headline. Intenta de nuevo.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setTextStates(prev => ({ ...prev, isGeneratingHeadline: false }));
    }
  };

  // Generate punchline
  const handleGeneratePunchline = async (): Promise<string> => {
    setTextStates(prev => ({ ...prev, isGeneratingPunchline: true }));
    try {
      const content = await generateText("punchline");
      toast({
        title: "Punchline generado",
        description: "Se ha generado un nuevo punchline con IA",
      });
      return content;
    } catch (error) {
      console.error("Error generating punchline:", error);
      toast({
        title: "Error al generar",
        description: "Hubo un problema generando el punchline. Intenta de nuevo.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setTextStates(prev => ({ ...prev, isGeneratingPunchline: false }));
    }
  };

  // Generate CTA
  const handleGenerateCta = async (): Promise<string> => {
    setTextStates(prev => ({ ...prev, isGeneratingCta: true }));
    try {
      const content = await generateText("cta");
      toast({
        title: "CTA generado",
        description: "Se ha generado un nuevo CTA con IA",
      });
      return content;
    } catch (error) {
      console.error("Error generating CTA:", error);
      toast({
        title: "Error al generar",
        description: "Hubo un problema generando el CTA. Intenta de nuevo.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setTextStates(prev => ({ ...prev, isGeneratingCta: false }));
    }
  };

  return {
    textStates,
    handleGenerateHeadline,
    handleGeneratePunchline,
    handleGenerateCta
  };
}

/**
 * Simplified Ad Creator Types
 * Specific types for the simplified ad creator workflow
 */

// Step types for the simplified flow
export type Step = "size" | "content" | "results";

// Size option interface
export interface SizeOption {
  name: string;
  dimensions: string;
  desc: string;
  icon: string;
}

// Platform sizes configuration
export interface PlatformSizes {
  [key: string]: SizeOption[];
}

// Generation progress state
export interface GenerationProgress {
  completed: number;
  total: number;
  errors: Array<{ index: number; error: string }>;
}

// Text generation states
export interface TextGenerationStates {
  isGeneratingHeadline: boolean;
  isGeneratingPunchline: boolean;
  isGeneratingCta: boolean;
}

// Step navigation state
export interface StepNavigationState {
  currentStep: Step;
  selectedSize: SizeOption | null;
  canProceedToContent: boolean;
  canProceedToResults: boolean;
}

// Ad generation state
export interface AdGenerationState {
  prompt: string;
  uploadedImage: File | null;
  headline: string;
  punchline: string;
  cta: string;
  isGenerating: boolean;
  generationProgress: GenerationProgress;
  estimatedTime: number;
}

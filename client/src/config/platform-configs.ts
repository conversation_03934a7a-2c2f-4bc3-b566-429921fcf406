/**
 * Platform Configuration System
 * Centralized configurations for all advertising platforms
 */

import { 
  Facebook, 
  Instagram, 
  Youtube, 
  Linkedin, 
  Twitter, 
  Music, 
  Monitor, 
  Globe 
} from "lucide-react";
import { PlatformConfig } from "@/types/ad-creator-types";

export const facebookConfig: PlatformConfig = {
  name: "Facebook Ads",
  icon: Facebook,
  color: "from-[#3018ef] to-[#dd3a5a]",
  sizes: ["1080x1080", "1200x1200"],
  defaultSize: "1080x1080",
  description: "Anuncios profesionales para Facebook",
  promptPrefix: "Create a professional Facebook ad with square format, engaging visuals, and clear call-to-action. ",
  specifications: {
    aspectRatio: "1:1",
    fileSize: "Máx. 30MB",
    fileTypes: ["JPG", "PNG"],
    textLimit: "2,200 caracteres",
    guidelines: ["Diseño cuadrado", "Call-to-action claro", "Branding visible"]
  }
};

export const instagramConfig: PlatformConfig = {
  name: "Instagram Posts",
  icon: Instagram,
  color: "from-[#dd3a5a] to-[#3018ef]",
  sizes: ["1080x1080", "1200x1200"],
  defaultSize: "1080x1080",
  description: "Diseños perfectos para Instagram feed",
  promptPrefix: "Create a stunning Instagram post with square format, vibrant colors, and Instagram-style aesthetics. ",
  specifications: {
    aspectRatio: "1:1",
    fileSize: "Máx. 30MB",
    fileTypes: ["JPG", "PNG"],
    textLimit: "2,200 caracteres",
    guidelines: ["Estética visual atractiva", "Hashtags relevantes", "Engagement-focused"]
  }
};

export const instagramStoriesConfig: PlatformConfig = {
  name: "Instagram Stories",
  icon: Instagram,
  color: "from-[#833ab4] to-[#fd1d1d]",
  sizes: ["1080x1920", "1080x1350"],
  defaultSize: "1080x1920",
  description: "Stories verticales que capturan la atención",
  promptPrefix: "Create an engaging Instagram Story with vertical format, bold visuals, and story-specific design elements. ",
  specifications: {
    aspectRatio: "9:16",
    fileSize: "Máx. 30MB",
    fileTypes: ["JPG", "PNG", "MP4"],
    textLimit: "2,200 caracteres",
    guidelines: ["Formato vertical", "Diseño llamativo", "Elementos interactivos"]
  }
};

export const googleAdsConfig: PlatformConfig = {
  name: "Google Ads",
  icon: Globe,
  color: "from-[#4285f4] to-[#34a853]",
  sizes: ["1200x628", "1080x1080", "728x90"],
  defaultSize: "1200x628",
  description: "Anuncios efectivos para Google Ads",
  promptPrefix: "Create a professional Google Ads banner with clear messaging, strong branding, and conversion-focused design. ",
  specifications: {
    aspectRatio: "1.91:1 / 1:1",
    fileSize: "Máx. 150KB",
    fileTypes: ["JPG", "PNG", "GIF"],
    textLimit: "90 caracteres",
    guidelines: ["Mensaje claro", "CTA prominente", "Branding fuerte"]
  }
};

export const linkedinConfig: PlatformConfig = {
  name: "LinkedIn Ads",
  icon: Linkedin,
  color: "from-[#0077b5] to-[#004182]",
  sizes: ["1200x627", "1080x1080", "1200x1200"],
  defaultSize: "1200x627",
  description: "Anuncios profesionales para LinkedIn",
  promptPrefix: "Create a professional LinkedIn ad with corporate design, B2B focus, and business-oriented messaging. ",
  specifications: {
    aspectRatio: "1.91:1 / 1:1",
    fileSize: "Máx. 5MB",
    fileTypes: ["JPG", "PNG"],
    textLimit: "150 caracteres",
    guidelines: ["Diseño corporativo", "Mensaje B2B", "Credibilidad profesional"]
  }
};

export const twitterConfig: PlatformConfig = {
  name: "Twitter/X Ads",
  icon: Twitter,
  color: "from-[#1da1f2] to-[#0d8bd9]",
  sizes: ["1200x675", "1080x1080", "800x418"],
  defaultSize: "1200x675",
  description: "Anuncios virales para Twitter/X",
  promptPrefix: "Create a viral Twitter/X ad with engaging visuals, trending aesthetics, and social media appeal. ",
  specifications: {
    aspectRatio: "16:9 / 1:1",
    fileSize: "Máx. 5MB",
    fileTypes: ["JPG", "PNG", "GIF"],
    textLimit: "280 caracteres",
    guidelines: ["Contenido viral", "Hashtags trending", "Engagement alto"]
  }
};

export const tiktokConfig: PlatformConfig = {
  name: "TikTok Ads",
  icon: Music,
  color: "from-[#ff0050] to-[#00f2ea]",
  sizes: ["1080x1920", "1080x1080", "720x1280"],
  defaultSize: "1080x1920",
  description: "Anuncios creativos para TikTok",
  promptPrefix: "Create a trendy TikTok ad with vertical format, Gen Z aesthetics, vibrant colors, and viral appeal. ",
  specifications: {
    aspectRatio: "9:16 / 1:1",
    fileSize: "Máx. 500MB",
    fileTypes: ["MP4", "MOV", "JPG", "PNG"],
    textLimit: "100 caracteres",
    guidelines: ["Formato vertical", "Contenido trending", "Estética Gen Z"]
  }
};

export const youtubeConfig: PlatformConfig = {
  name: "YouTube Ads",
  icon: Youtube,
  color: "from-[#ff0000] to-[#cc0000]",
  sizes: ["1280x720", "1920x1080", "1280x720"],
  defaultSize: "1280x720",
  description: "Thumbnails que destacan en YouTube",
  promptPrefix: "Create an eye-catching YouTube thumbnail with bold text, vibrant colors, and click-worthy design. ",
  specifications: {
    aspectRatio: "16:9",
    fileSize: "Máx. 2MB",
    fileTypes: ["JPG", "PNG"],
    textLimit: "100 caracteres",
    guidelines: ["Texto grande y legible", "Colores llamativos", "Expresiones faciales"]
  }
};

export const displayConfig: PlatformConfig = {
  name: "Display Banners",
  icon: Monitor,
  color: "from-[#6366f1] to-[#8b5cf6]",
  sizes: ["728x90", "300x250", "320x50", "160x600"],
  defaultSize: "728x90",
  description: "Banners para sitios web",
  promptPrefix: "Create a professional display banner with clear branding, compelling message, and web-optimized design. ",
  specifications: {
    aspectRatio: "Variable",
    fileSize: "Máx. 150KB",
    fileTypes: ["JPG", "PNG", "GIF"],
    textLimit: "50 caracteres",
    guidelines: ["Mensaje claro", "Branding visible", "CTA destacado"]
  }
};

// Platform configuration map for easy access
export const platformConfigs = {
  facebook: facebookConfig,
  instagram: instagramConfig,
  "instagram-stories": instagramStoriesConfig,
  google: googleAdsConfig,
  linkedin: linkedinConfig,
  twitter: twitterConfig,
  tiktok: tiktokConfig,
  youtube: youtubeConfig,
  display: displayConfig
} as const;

// Helper function to get platform config
export function getPlatformConfig(platform: string): PlatformConfig {
  return platformConfigs[platform as keyof typeof platformConfigs] || facebookConfig;
}

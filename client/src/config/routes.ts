/**
 * SEO & GPT Optimizer™ - Routes Configuration
 * Centralized route definitions and utilities
 */

export const ROUTES = {
  // Base routes
  ROOT: '/',
  DASHBOARD: '/dashboard',
  
  // SEO GPT Optimizer routes
  SEO_GPT_OPTIMIZER: {
    ROOT: '/dashboard/herramientas/seo-gpt-optimizer',
    RESEARCH: '/dashboard/herramientas/seo-gpt-optimizer/research',
    CONTENT_BUILDER: '/dashboard/herramientas/seo-gpt-optimizer/content-builder',
    CONTENT_BUILDER_PROJECT: (projectId: string) => `/dashboard/herramientas/seo-gpt-optimizer/content-builder/${projectId}`,
    ANALYTICS: '/dashboard/herramientas/seo-gpt-optimizer/analytics',
    PROJECTS: '/dashboard/herramientas/seo-gpt-optimizer/projects',
    PROJECT_DETAIL: (projectId: string) => `/dashboard/herramientas/seo-gpt-optimizer/content-builder/${projectId}`,
    NEW_PROJECT: '/dashboard/herramientas/seo-gpt-optimizer/projects'
  }
} as const;

export const ROUTE_LABELS = {
  [ROUTES.DASHBOARD]: 'Dashboard',
  [ROUTES.SEO_GPT_OPTIMIZER.ROOT]: 'SEO & GPT Optimizer™',
  [ROUTES.SEO_GPT_OPTIMIZER.RESEARCH]: 'Research Engine',
  [ROUTES.SEO_GPT_OPTIMIZER.CONTENT_BUILDER]: 'Content Builder',
  [ROUTES.SEO_GPT_OPTIMIZER.ANALYTICS]: 'Analytics',
  [ROUTES.SEO_GPT_OPTIMIZER.PROJECTS]: 'Proyectos',
  [ROUTES.SEO_GPT_OPTIMIZER.NEW_PROJECT]: 'Nuevo Proyecto'
} as const;

export const ROUTE_DESCRIPTIONS = {
  [ROUTES.SEO_GPT_OPTIMIZER.ROOT]: 'Vista general y estadísticas',
  [ROUTES.SEO_GPT_OPTIMIZER.RESEARCH]: 'Investigación de temas y análisis competitivo',
  [ROUTES.SEO_GPT_OPTIMIZER.CONTENT_BUILDER]: 'Editor con análisis GPT Rank en tiempo real',
  [ROUTES.SEO_GPT_OPTIMIZER.ANALYTICS]: 'Métricas detalladas y análisis de rendimiento',
  [ROUTES.SEO_GPT_OPTIMIZER.PROJECTS]: 'Gestión completa de proyectos',
  [ROUTES.SEO_GPT_OPTIMIZER.NEW_PROJECT]: 'Crear un nuevo proyecto de optimización'
} as const;

// Route utilities
export const getRouteLabel = (path: string): string => {
  return ROUTE_LABELS[path as keyof typeof ROUTE_LABELS] || path;
};

export const getRouteDescription = (path: string): string => {
  return ROUTE_DESCRIPTIONS[path as keyof typeof ROUTE_DESCRIPTIONS] || '';
};

export const isProjectRoute = (path: string): boolean => {
  return path.includes('/projects/') && path !== ROUTES.SEO_GPT_OPTIMIZER.NEW_PROJECT;
};

export const isContentBuilderRoute = (path: string): boolean => {
  return path.startsWith(ROUTES.SEO_GPT_OPTIMIZER.CONTENT_BUILDER);
};

export const extractProjectId = (path: string): string | null => {
  const projectMatch = path.match(/\/projects\/([^\/]+)$/);
  const contentBuilderMatch = path.match(/\/content-builder\/([^\/]+)$/);
  
  return projectMatch?.[1] || contentBuilderMatch?.[1] || null;
};

export const buildProjectRoute = (projectId: string, section: 'detail' | 'content-builder' = 'content-builder'): string => {
  return section === 'detail' 
    ? ROUTES.SEO_GPT_OPTIMIZER.PROJECT_DETAIL(projectId)
    : ROUTES.SEO_GPT_OPTIMIZER.CONTENT_BUILDER_PROJECT(projectId);
};

// Navigation helpers
export const navigationItems = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    path: ROUTES.SEO_GPT_OPTIMIZER.ROOT,
    description: ROUTE_DESCRIPTIONS[ROUTES.SEO_GPT_OPTIMIZER.ROOT]
  },
  {
    id: 'research',
    label: 'Research Engine',
    path: ROUTES.SEO_GPT_OPTIMIZER.RESEARCH,
    description: ROUTE_DESCRIPTIONS[ROUTES.SEO_GPT_OPTIMIZER.RESEARCH]
  },
  {
    id: 'content-builder',
    label: 'Content Builder',
    path: ROUTES.SEO_GPT_OPTIMIZER.CONTENT_BUILDER,
    description: ROUTE_DESCRIPTIONS[ROUTES.SEO_GPT_OPTIMIZER.CONTENT_BUILDER]
  },
  {
    id: 'analytics',
    label: 'Analytics',
    path: ROUTES.SEO_GPT_OPTIMIZER.ANALYTICS,
    description: ROUTE_DESCRIPTIONS[ROUTES.SEO_GPT_OPTIMIZER.ANALYTICS]
  },
  {
    id: 'projects',
    label: 'Proyectos',
    path: ROUTES.SEO_GPT_OPTIMIZER.PROJECTS,
    description: ROUTE_DESCRIPTIONS[ROUTES.SEO_GPT_OPTIMIZER.PROJECTS]
  }
] as const;

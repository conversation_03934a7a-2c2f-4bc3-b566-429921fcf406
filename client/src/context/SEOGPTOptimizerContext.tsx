/**
 * SEO & GPT Optimizer™ - Global Context
 * Global state management for the SEO GPT Optimizer application
 */

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface SEOGPTOptimizerState {
  // UI State
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark' | 'system';
  
  // User Preferences
  preferences: {
    defaultLanguage: string;
    autoSave: boolean;
    showTips: boolean;
    compactMode: boolean;
  };
  
  // Application State
  isOnline: boolean;
  lastSync: Date | null;
  
  // Feature Flags
  features: {
    advancedAnalytics: boolean;
    bulkOperations: boolean;
    realTimeCollaboration: boolean;
  };
}

type SEOGPTOptimizerAction =
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'SET_THEME'; payload: 'light' | 'dark' | 'system' }
  | { type: 'UPDATE_PREFERENCES'; payload: Partial<SEOGPTOptimizerState['preferences']> }
  | { type: 'SET_ONLINE_STATUS'; payload: boolean }
  | { type: 'UPDATE_LAST_SYNC'; payload: Date }
  | { type: 'TOGGLE_FEATURE'; payload: { feature: keyof SEOGPTOptimizerState['features']; enabled: boolean } }
  | { type: 'RESET_STATE' };

const initialState: SEOGPTOptimizerState = {
  sidebarCollapsed: false,
  theme: 'system',
  preferences: {
    defaultLanguage: 'es',
    autoSave: true,
    showTips: true,
    compactMode: false
  },
  isOnline: navigator.onLine,
  lastSync: null,
  features: {
    advancedAnalytics: true,
    bulkOperations: true,
    realTimeCollaboration: false
  }
};

const seoGptOptimizerReducer = (
  state: SEOGPTOptimizerState,
  action: SEOGPTOptimizerAction
): SEOGPTOptimizerState => {
  switch (action.type) {
    case 'TOGGLE_SIDEBAR':
      return {
        ...state,
        sidebarCollapsed: !state.sidebarCollapsed
      };
      
    case 'SET_THEME':
      return {
        ...state,
        theme: action.payload
      };
      
    case 'UPDATE_PREFERENCES':
      return {
        ...state,
        preferences: {
          ...state.preferences,
          ...action.payload
        }
      };
      
    case 'SET_ONLINE_STATUS':
      return {
        ...state,
        isOnline: action.payload
      };
      
    case 'UPDATE_LAST_SYNC':
      return {
        ...state,
        lastSync: action.payload
      };
      
    case 'TOGGLE_FEATURE':
      return {
        ...state,
        features: {
          ...state.features,
          [action.payload.feature]: action.payload.enabled
        }
      };
      
    case 'RESET_STATE':
      return initialState;
      
    default:
      return state;
  }
};

interface SEOGPTOptimizerContextType {
  state: SEOGPTOptimizerState;
  dispatch: React.Dispatch<SEOGPTOptimizerAction>;
  
  // Convenience methods
  toggleSidebar: () => void;
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  updatePreferences: (preferences: Partial<SEOGPTOptimizerState['preferences']>) => void;
  toggleFeature: (feature: keyof SEOGPTOptimizerState['features'], enabled: boolean) => void;
  resetState: () => void;
}

const SEOGPTOptimizerContext = createContext<SEOGPTOptimizerContextType | undefined>(undefined);

export const useSEOGPTOptimizer = (): SEOGPTOptimizerContextType => {
  const context = useContext(SEOGPTOptimizerContext);
  if (!context) {
    throw new Error('useSEOGPTOptimizer must be used within a SEOGPTOptimizerProvider');
  }
  return context;
};

interface SEOGPTOptimizerProviderProps {
  children: React.ReactNode;
}

export const SEOGPTOptimizerProvider: React.FC<SEOGPTOptimizerProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(seoGptOptimizerReducer, initialState);

  // Load state from localStorage on mount
  useEffect(() => {
    try {
      const savedState = localStorage.getItem('seo-gpt-optimizer-state');
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        dispatch({ type: 'UPDATE_PREFERENCES', payload: parsedState.preferences });
        dispatch({ type: 'SET_THEME', payload: parsedState.theme });
        if (parsedState.features) {
          Object.entries(parsedState.features).forEach(([feature, enabled]) => {
            dispatch({ 
              type: 'TOGGLE_FEATURE', 
              payload: { 
                feature: feature as keyof SEOGPTOptimizerState['features'], 
                enabled: enabled as boolean 
              } 
            });
          });
        }
      }
    } catch (error) {
      console.error('Error loading saved state:', error);
    }
  }, []);

  // Save state to localStorage when it changes
  useEffect(() => {
    try {
      const stateToSave = {
        preferences: state.preferences,
        theme: state.theme,
        features: state.features
      };
      localStorage.setItem('seo-gpt-optimizer-state', JSON.stringify(stateToSave));
    } catch (error) {
      console.error('Error saving state:', error);
    }
  }, [state.preferences, state.theme, state.features]);

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => {
      dispatch({ type: 'SET_ONLINE_STATUS', payload: true });
      toast.success('Conexión restaurada');
    };

    const handleOffline = () => {
      dispatch({ type: 'SET_ONLINE_STATUS', payload: false });
      toast.error('Sin conexión a internet');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;
    
    if (state.theme === 'dark') {
      root.classList.add('dark');
    } else if (state.theme === 'light') {
      root.classList.remove('dark');
    } else {
      // System theme
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      if (mediaQuery.matches) {
        root.classList.add('dark');
      } else {
        root.classList.remove('dark');
      }
    }
  }, [state.theme]);

  // Convenience methods
  const toggleSidebar = () => {
    dispatch({ type: 'TOGGLE_SIDEBAR' });
  };

  const setTheme = (theme: 'light' | 'dark' | 'system') => {
    dispatch({ type: 'SET_THEME', payload: theme });
  };

  const updatePreferences = (preferences: Partial<SEOGPTOptimizerState['preferences']>) => {
    dispatch({ type: 'UPDATE_PREFERENCES', payload: preferences });
  };

  const toggleFeature = (feature: keyof SEOGPTOptimizerState['features'], enabled: boolean) => {
    dispatch({ type: 'TOGGLE_FEATURE', payload: { feature, enabled } });
  };

  const resetState = () => {
    dispatch({ type: 'RESET_STATE' });
    localStorage.removeItem('seo-gpt-optimizer-state');
    toast.success('Configuración restablecida');
  };

  const contextValue: SEOGPTOptimizerContextType = {
    state,
    dispatch,
    toggleSidebar,
    setTheme,
    updatePreferences,
    toggleFeature,
    resetState
  };

  return (
    <SEOGPTOptimizerContext.Provider value={contextValue}>
      {children}
    </SEOGPTOptimizerContext.Provider>
  );
};

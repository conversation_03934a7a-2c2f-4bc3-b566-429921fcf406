/**
 * SEO & GPT Optimizer™ - Analytics Hook
 * Custom hook for managing analytics data and operations
 */

import { useState, useCallback, useEffect } from 'react';
import { seoGptAPI } from '../../services/seo-gpt-optimizer/api';
import { LoadingState, ErrorState } from '../../types/seo-gpt-optimizer';

interface ScoreHistoryData {
  score: number;
  timestamp: string;
  version: number;
  trigger_event?: string;
}

interface CompetitorData {
  domain: string;
  title: string;
  url: string;
  estimated_score: number;
  position: number;
  content_length: number;
  authority_signals: number;
  semantic_similarity: number;
  strengths: string[];
  weaknesses: string[];
  opportunities: string[];
}

interface AnalyticsData {
  scoreHistory: ScoreHistoryData[];
  competitorData: CompetitorData[];
  insights: {
    totalImprovement: number;
    averageScore: number;
    bestPerformingProject: string;
    competitivePosition: number;
    recommendedActions: string[];
  };
}

interface UseAnalyticsReturn {
  // State
  analyticsData: AnalyticsData | null;
  loading: LoadingState;
  error: ErrorState;
  
  // Actions
  loadAnalytics: (projectId?: string, timeRange?: string) => Promise<void>;
  loadScoreHistory: (projectId: string, days?: number) => Promise<void>;
  loadCompetitorAnalysis: (topic: string) => Promise<void>;
  exportAnalytics: (format: 'json' | 'csv' | 'pdf') => Promise<void>;
  clearData: () => void;
  clearError: () => void;
  
  // Computed values
  hasData: boolean;
  totalProjects: number;
  averageScore: number;
  improvementTrend: 'up' | 'down' | 'stable';
}

export const useAnalytics = (): UseAnalyticsReturn => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    message: '',
    progress: 0
  });
  const [error, setError] = useState<ErrorState>({
    hasError: false,
    message: '',
    code: ''
  });

  const loadAnalytics = useCallback(async (projectId?: string, timeRange: string = '30d') => {
    try {
      setLoading({
        isLoading: true,
        message: 'Cargando datos de analytics...',
        progress: 0
      });
      setError({ hasError: false });

      // Simulate progressive loading
      const progressSteps = [
        { progress: 20, message: 'Cargando historial de puntuaciones...' },
        { progress: 50, message: 'Analizando competidores...' },
        { progress: 80, message: 'Generando insights...' }
      ];

      // Update progress
      for (const step of progressSteps) {
        setLoading(prev => ({
          ...prev,
          progress: step.progress,
          message: step.message
        }));
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Load score history
      const scoreHistoryResponse = projectId 
        ? await seoGptAPI.getScoreHistory(projectId, parseInt(timeRange.replace('d', '')))
        : { status: 'success', data: [] };

      if (scoreHistoryResponse.status !== 'success') {
        throw new Error('Error loading score history');
      }

      // Load dashboard stats for insights
      const statsResponse = await seoGptAPI.getDashboardStats();
      if (statsResponse.status !== 'success') {
        throw new Error('Error loading dashboard stats');
      }

      // Mock competitor data (in real implementation, this would come from research results)
      const mockCompetitorData: CompetitorData[] = [
        {
          domain: 'wikipedia.org',
          title: 'Comprehensive Guide to the Topic',
          url: 'https://wikipedia.org/example',
          estimated_score: 92.5,
          position: 1,
          content_length: 3200,
          authority_signals: 95,
          semantic_similarity: 88,
          strengths: ['High authority domain', 'Comprehensive coverage', 'Multiple citations'],
          weaknesses: ['Generic tone', 'Limited practical examples'],
          opportunities: ['Add more recent data', 'Include case studies']
        },
        {
          domain: 'expertsite.com',
          title: 'Expert Analysis and Insights',
          url: 'https://expertsite.com/example',
          estimated_score: 87.3,
          position: 2,
          content_length: 2800,
          authority_signals: 82,
          semantic_similarity: 91,
          strengths: ['Expert authorship', 'Technical depth', 'Recent updates'],
          weaknesses: ['Complex language', 'Limited accessibility'],
          opportunities: ['Simplify explanations', 'Add visual aids']
        }
      ];

      // Calculate insights
      const scoreHistory = scoreHistoryResponse.data || [];
      const totalImprovement = scoreHistory.length > 1 
        ? scoreHistory[scoreHistory.length - 1].score - scoreHistory[0].score 
        : 0;
      
      const averageScore = statsResponse.data?.avg_gpt_rank_score || 0;
      const userScore = scoreHistory.length > 0 
        ? scoreHistory[scoreHistory.length - 1].score 
        : averageScore;
      
      const competitivePosition = mockCompetitorData.filter(c => c.estimated_score > userScore).length + 1;

      const insights = {
        totalImprovement,
        averageScore,
        bestPerformingProject: projectId || 'N/A',
        competitivePosition,
        recommendedActions: [
          totalImprovement < 5 ? 'Enfócate en mejorar la autoridad del contenido' : 'Mantén el buen ritmo de mejora',
          competitivePosition > 3 ? 'Analiza las estrategias de los competidores líderes' : 'Mantén tu posición competitiva',
          averageScore < 70 ? 'Prioriza la optimización de contenido existente' : 'Explora nuevas oportunidades de contenido'
        ]
      };

      setAnalyticsData({
        scoreHistory,
        competitorData: mockCompetitorData,
        insights
      });

      setLoading({
        isLoading: false,
        message: 'Analytics cargados exitosamente',
        progress: 100
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'ANALYTICS_LOAD_ERROR'
      });
      setLoading({
        isLoading: false,
        message: 'Error al cargar analytics',
        progress: 0
      });
    }
  }, []);

  const loadScoreHistory = useCallback(async (projectId: string, days: number = 30) => {
    try {
      setLoading({
        isLoading: true,
        message: 'Cargando historial de puntuaciones...',
        progress: 50
      });

      const response = await seoGptAPI.getScoreHistory(projectId, days);
      
      if (response.status === 'success' && response.data) {
        setAnalyticsData(prev => prev ? {
          ...prev,
          scoreHistory: response.data
        } : null);
      } else {
        throw new Error('Error loading score history');
      }

      setLoading({
        isLoading: false,
        message: 'Historial cargado',
        progress: 100
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'SCORE_HISTORY_ERROR'
      });
      setLoading({ isLoading: false, progress: 0 });
    }
  }, []);

  const loadCompetitorAnalysis = useCallback(async (topic: string) => {
    try {
      setLoading({
        isLoading: true,
        message: 'Analizando competidores...',
        progress: 50
      });

      // In a real implementation, this would analyze competitors based on research data
      // For now, we'll use mock data
      await new Promise(resolve => setTimeout(resolve, 1000));

      setLoading({
        isLoading: false,
        message: 'Análisis competitivo completado',
        progress: 100
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'COMPETITOR_ANALYSIS_ERROR'
      });
      setLoading({ isLoading: false, progress: 0 });
    }
  }, []);

  const exportAnalytics = useCallback(async (format: 'json' | 'csv' | 'pdf') => {
    if (!analyticsData) return;

    try {
      setLoading({
        isLoading: true,
        message: `Exportando en formato ${format.toUpperCase()}...`,
        progress: 50
      });

      let content: string;
      let mimeType: string;
      let filename: string;

      switch (format) {
        case 'json':
          content = JSON.stringify(analyticsData, null, 2);
          mimeType = 'application/json';
          filename = `analytics-${Date.now()}.json`;
          break;
        
        case 'csv':
          const csvHeaders = 'Timestamp,Score,Version,Event\n';
          const csvRows = analyticsData.scoreHistory.map(item => 
            `${item.timestamp},${item.score},${item.version},${item.trigger_event || ''}`
          ).join('\n');
          content = csvHeaders + csvRows;
          mimeType = 'text/csv';
          filename = `analytics-${Date.now()}.csv`;
          break;
        
        default:
          throw new Error('Formato no soportado');
      }

      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.click();
      URL.revokeObjectURL(url);

      setLoading({
        isLoading: false,
        message: 'Exportación completada',
        progress: 100
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'EXPORT_ERROR'
      });
      setLoading({ isLoading: false, progress: 0 });
    }
  }, [analyticsData]);

  const clearData = useCallback(() => {
    setAnalyticsData(null);
    setLoading({ isLoading: false, progress: 0 });
    setError({ hasError: false });
  }, []);

  const clearError = useCallback(() => {
    setError({ hasError: false });
  }, []);

  // Computed values
  const hasData = analyticsData !== null;
  const totalProjects = analyticsData?.scoreHistory.length || 0;
  const averageScore = analyticsData?.insights.averageScore || 0;
  
  const improvementTrend = (() => {
    if (!analyticsData?.scoreHistory.length || analyticsData.scoreHistory.length < 2) {
      return 'stable';
    }
    
    const recent = analyticsData.scoreHistory.slice(-3);
    const trend = recent[recent.length - 1].score - recent[0].score;
    
    if (trend > 2) return 'up';
    if (trend < -2) return 'down';
    return 'stable';
  })();

  return {
    // State
    analyticsData,
    loading,
    error,
    
    // Actions
    loadAnalytics,
    loadScoreHistory,
    loadCompetitorAnalysis,
    exportAnalytics,
    clearData,
    clearError,
    
    // Computed values
    hasData,
    totalProjects,
    averageScore,
    improvementTrend
  };
};

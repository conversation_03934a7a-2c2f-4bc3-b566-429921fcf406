/**
 * SEO & GPT Optimizer™ - Blog Generator Hook
 * Hook for generating complete blog content with Creative Genius
 */

import { useState, useCallback } from 'react';

interface BlogGenerationRequest {
  project_id: string;
  topic: string;
  content_type?: string;
  include_images?: boolean;
  num_images?: number;
  target_length?: string;
}

interface BlogGenerationResponse {
  status: string;
  project_id: string;
  message: string;
  data: {
    content: string;
    images: string[];
    creative_concept: {
      visual_concept: string;
      hook: string;
      content_angle: string;
      viral_score: number;
      why_brilliant: string;
    };
    content_stats: {
      word_count: number;
      character_count: number;
      estimated_reading_time: string;
    };
  };
}

interface LoadingState {
  isLoading: boolean;
  message: string;
  progress: number;
}

interface ErrorState {
  hasError: boolean;
  message: string;
}

export const useBlogGenerator = () => {
  const [generatedBlog, setGeneratedBlog] = useState<BlogGenerationResponse | null>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    message: '',
    progress: 0
  });
  const [error, setError] = useState<ErrorState>({
    hasError: false,
    message: ''
  });

  const generateBlog = useCallback(async (request: BlogGenerationRequest) => {
    try {
      setLoading({
        isLoading: true,
        message: 'Iniciando generación de blog...',
        progress: 10
      });
      setError({ hasError: false, message: '' });
      setGeneratedBlog(null);

      // Update progress - Creative Genius thinking
      setLoading(prev => ({
        ...prev,
        message: '🧠 Creative Genius analizando el tema...',
        progress: 25
      }));

      const response = await fetch('/api/v1/seo-gpt/content/generate-blog', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error generating blog content');
      }

      // Update progress - Content generation
      setLoading(prev => ({
        ...prev,
        message: '✍️ Generando contenido inteligente...',
        progress: 60
      }));

      const data: BlogGenerationResponse = await response.json();

      // Update progress - Image generation
      if (request.include_images) {
        setLoading(prev => ({
          ...prev,
          message: '🎨 Creando imágenes contextuales...',
          progress: 85
        }));
      }

      // Final step
      setLoading(prev => ({
        ...prev,
        message: '✅ Finalizando blog...',
        progress: 100
      }));

      setGeneratedBlog(data);
      
      // Clear loading after a brief delay to show completion
      setTimeout(() => {
        setLoading({
          isLoading: false,
          message: '',
          progress: 0
        });
      }, 1000);

    } catch (err) {
      console.error('Blog generation error:', err);
      setError({
        hasError: true,
        message: err instanceof Error ? err.message : 'Error desconocido al generar el blog'
      });
      setLoading({
        isLoading: false,
        message: '',
        progress: 0
      });
    }
  }, []);

  const clearError = useCallback(() => {
    setError({ hasError: false, message: '' });
  }, []);

  const clearBlog = useCallback(() => {
    setGeneratedBlog(null);
    setError({ hasError: false, message: '' });
  }, []);

  return {
    // State
    generatedBlog,
    loading,
    error,
    
    // Computed
    hasGeneratedBlog: !!generatedBlog,
    isGenerating: loading.isLoading,
    
    // Actions
    generateBlog,
    clearError,
    clearBlog,
    
    // Blog data helpers
    blogContent: generatedBlog?.data?.content || '',
    blogImages: generatedBlog?.data?.images || [],
    creativeConcept: generatedBlog?.data?.creative_concept,
    contentStats: generatedBlog?.data?.content_stats,
  };
};

export default useBlogGenerator;

/**
 * SEO & GPT Optimizer™ - Bulk Operations Hook
 * Custom hook for handling bulk operations on projects
 */

import { useState, useCallback } from 'react';
import { seoGptAPI } from '../../services/seo-gpt-optimizer/api';
import { SEOGPTProject, ProjectStatus, LoadingState, ErrorState } from '../../types/seo-gpt-optimizer';

interface BulkOperationResult {
  success: string[];
  failed: Array<{ id: string; error: string }>;
  total: number;
}

interface UseBulkOperationsReturn {
  // State
  selectedProjects: Set<string>;
  loading: LoadingState;
  error: ErrorState;
  lastOperation: BulkOperationResult | null;
  
  // Selection actions
  selectProject: (projectId: string) => void;
  deselectProject: (projectId: string) => void;
  selectAll: (projects: SEOGPTProject[]) => void;
  deselectAll: () => void;
  toggleProject: (projectId: string) => void;
  
  // Bulk operations
  bulkDelete: (projectIds: string[]) => Promise<BulkOperationResult>;
  bulkStatusChange: (projectIds: string[], status: ProjectStatus) => Promise<BulkOperationResult>;
  bulkAnalyze: (projectIds: string[]) => Promise<BulkOperationResult>;
  bulkExport: (projectIds: string[], format: 'json' | 'csv') => Promise<void>;
  bulkDuplicate: (projectIds: string[]) => Promise<BulkOperationResult>;
  
  // Utility
  clearSelection: () => void;
  clearError: () => void;
  clearLastOperation: () => void;
  
  // Computed values
  hasSelection: boolean;
  selectionCount: number;
  isAllSelected: (projects: SEOGPTProject[]) => boolean;
}

export const useBulkOperations = (): UseBulkOperationsReturn => {
  const [selectedProjects, setSelectedProjects] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    message: '',
    progress: 0
  });
  const [error, setError] = useState<ErrorState>({
    hasError: false,
    message: '',
    code: ''
  });
  const [lastOperation, setLastOperation] = useState<BulkOperationResult | null>(null);

  // Selection actions
  const selectProject = useCallback((projectId: string) => {
    setSelectedProjects(prev => new Set([...prev, projectId]));
  }, []);

  const deselectProject = useCallback((projectId: string) => {
    setSelectedProjects(prev => {
      const newSet = new Set(prev);
      newSet.delete(projectId);
      return newSet;
    });
  }, []);

  const selectAll = useCallback((projects: SEOGPTProject[]) => {
    setSelectedProjects(new Set(projects.map(p => p.project_id)));
  }, []);

  const deselectAll = useCallback(() => {
    setSelectedProjects(new Set());
  }, []);

  const toggleProject = useCallback((projectId: string) => {
    setSelectedProjects(prev => {
      const newSet = new Set(prev);
      if (newSet.has(projectId)) {
        newSet.delete(projectId);
      } else {
        newSet.add(projectId);
      }
      return newSet;
    });
  }, []);

  // Bulk operations
  const bulkDelete = useCallback(async (projectIds: string[]): Promise<BulkOperationResult> => {
    try {
      setLoading({
        isLoading: true,
        message: `Eliminando ${projectIds.length} proyectos...`,
        progress: 0
      });
      setError({ hasError: false });

      const result: BulkOperationResult = {
        success: [],
        failed: [],
        total: projectIds.length
      };

      for (let i = 0; i < projectIds.length; i++) {
        const projectId = projectIds[i];
        
        setLoading(prev => ({
          ...prev,
          progress: ((i + 1) / projectIds.length) * 100,
          message: `Eliminando proyecto ${i + 1} de ${projectIds.length}...`
        }));

        try {
          const response = await seoGptAPI.deleteProject(projectId);
          if (response.status === 'success') {
            result.success.push(projectId);
          } else {
            result.failed.push({ id: projectId, error: response.error_message || 'Error desconocido' });
          }
        } catch (err) {
          result.failed.push({ 
            id: projectId, 
            error: err instanceof Error ? err.message : 'Error desconocido' 
          });
        }
      }

      setLastOperation(result);
      setLoading({
        isLoading: false,
        message: `Operación completada: ${result.success.length} exitosos, ${result.failed.length} fallidos`,
        progress: 100
      });

      // Clear selection of successfully deleted projects
      setSelectedProjects(prev => {
        const newSet = new Set(prev);
        result.success.forEach(id => newSet.delete(id));
        return newSet;
      });

      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error en operación masiva';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'BULK_DELETE_ERROR'
      });
      setLoading({ isLoading: false, progress: 0 });
      throw err;
    }
  }, []);

  const bulkStatusChange = useCallback(async (
    projectIds: string[], 
    status: ProjectStatus
  ): Promise<BulkOperationResult> => {
    try {
      setLoading({
        isLoading: true,
        message: `Actualizando estado de ${projectIds.length} proyectos...`,
        progress: 0
      });
      setError({ hasError: false });

      const result: BulkOperationResult = {
        success: [],
        failed: [],
        total: projectIds.length
      };

      for (let i = 0; i < projectIds.length; i++) {
        const projectId = projectIds[i];
        
        setLoading(prev => ({
          ...prev,
          progress: ((i + 1) / projectIds.length) * 100,
          message: `Actualizando proyecto ${i + 1} de ${projectIds.length}...`
        }));

        try {
          const response = await seoGptAPI.updateProject(projectId, { 
            status,
            updated_at: new Date().toISOString()
          });
          if (response.status === 'success') {
            result.success.push(projectId);
          } else {
            result.failed.push({ id: projectId, error: response.error_message || 'Error desconocido' });
          }
        } catch (err) {
          result.failed.push({ 
            id: projectId, 
            error: err instanceof Error ? err.message : 'Error desconocido' 
          });
        }
      }

      setLastOperation(result);
      setLoading({
        isLoading: false,
        message: `Operación completada: ${result.success.length} exitosos, ${result.failed.length} fallidos`,
        progress: 100
      });

      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error en cambio de estado masivo';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'BULK_STATUS_CHANGE_ERROR'
      });
      setLoading({ isLoading: false, progress: 0 });
      throw err;
    }
  }, []);

  const bulkAnalyze = useCallback(async (projectIds: string[]): Promise<BulkOperationResult> => {
    try {
      setLoading({
        isLoading: true,
        message: `Analizando ${projectIds.length} proyectos...`,
        progress: 0
      });
      setError({ hasError: false });

      const response = await seoGptAPI.batchAnalyzeProjects(projectIds);
      
      if (response.status === 'success' && response.data) {
        const result: BulkOperationResult = {
          success: response.data.map(item => item.project_id),
          failed: [],
          total: projectIds.length
        };

        setLastOperation(result);
        setLoading({
          isLoading: false,
          message: `Análisis completado para ${result.success.length} proyectos`,
          progress: 100
        });

        return result;
      } else {
        throw new Error(response.error_message || 'Error en análisis masivo');
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error en análisis masivo';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'BULK_ANALYZE_ERROR'
      });
      setLoading({ isLoading: false, progress: 0 });
      throw err;
    }
  }, []);

  const bulkExport = useCallback(async (
    projectIds: string[], 
    format: 'json' | 'csv'
  ): Promise<void> => {
    try {
      setLoading({
        isLoading: true,
        message: `Exportando ${projectIds.length} proyectos en formato ${format.toUpperCase()}...`,
        progress: 50
      });
      setError({ hasError: false });

      // Get project details for export
      const projects: SEOGPTProject[] = [];
      for (const projectId of projectIds) {
        const response = await seoGptAPI.getProject(projectId);
        if (response.status === 'success' && response.data) {
          projects.push(response.data);
        }
      }

      let content: string;
      let mimeType: string;
      let filename: string;

      if (format === 'json') {
        content = JSON.stringify({
          exported_at: new Date().toISOString(),
          total_projects: projects.length,
          projects
        }, null, 2);
        mimeType = 'application/json';
        filename = `projects-export-${Date.now()}.json`;
      } else {
        // CSV format
        const headers = 'ID,Title,Topic,Status,Content Type,Current Score,Target Score,Word Count,Created,Updated\n';
        const rows = projects.map(p => 
          `"${p.project_id}","${p.title}","${p.topic}","${p.status}","${p.content_type}",${p.current_gpt_rank_score},${p.target_gpt_rank_score},${p.word_count},"${p.created_at}","${p.updated_at}"`
        ).join('\n');
        content = headers + rows;
        mimeType = 'text/csv';
        filename = `projects-export-${Date.now()}.csv`;
      }

      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.click();
      URL.revokeObjectURL(url);

      setLoading({
        isLoading: false,
        message: `Exportación completada: ${projects.length} proyectos`,
        progress: 100
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error en exportación masiva';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'BULK_EXPORT_ERROR'
      });
      setLoading({ isLoading: false, progress: 0 });
      throw err;
    }
  }, []);

  const bulkDuplicate = useCallback(async (projectIds: string[]): Promise<BulkOperationResult> => {
    try {
      setLoading({
        isLoading: true,
        message: `Duplicando ${projectIds.length} proyectos...`,
        progress: 0
      });
      setError({ hasError: false });

      const result: BulkOperationResult = {
        success: [],
        failed: [],
        total: projectIds.length
      };

      for (let i = 0; i < projectIds.length; i++) {
        const projectId = projectIds[i];
        
        setLoading(prev => ({
          ...prev,
          progress: ((i + 1) / projectIds.length) * 100,
          message: `Duplicando proyecto ${i + 1} de ${projectIds.length}...`
        }));

        try {
          // Get original project
          const originalResponse = await seoGptAPI.getProject(projectId);
          if (originalResponse.status === 'success' && originalResponse.data) {
            const original = originalResponse.data;
            
            // Create duplicate
            const duplicateData = {
              title: `${original.title} (Copia)`,
              topic: original.topic,
              target_language: original.target_language,
              content_type: original.content_type,
              target_gpt_rank_score: original.target_gpt_rank_score
            };

            const createResponse = await seoGptAPI.createProject(duplicateData);
            if (createResponse.status === 'success') {
              result.success.push(projectId);
            } else {
              result.failed.push({ id: projectId, error: createResponse.error_message || 'Error al crear duplicado' });
            }
          } else {
            result.failed.push({ id: projectId, error: 'No se pudo obtener el proyecto original' });
          }
        } catch (err) {
          result.failed.push({ 
            id: projectId, 
            error: err instanceof Error ? err.message : 'Error desconocido' 
          });
        }
      }

      setLastOperation(result);
      setLoading({
        isLoading: false,
        message: `Duplicación completada: ${result.success.length} exitosos, ${result.failed.length} fallidos`,
        progress: 100
      });

      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error en duplicación masiva';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'BULK_DUPLICATE_ERROR'
      });
      setLoading({ isLoading: false, progress: 0 });
      throw err;
    }
  }, []);

  // Utility functions
  const clearSelection = useCallback(() => {
    setSelectedProjects(new Set());
  }, []);

  const clearError = useCallback(() => {
    setError({ hasError: false });
  }, []);

  const clearLastOperation = useCallback(() => {
    setLastOperation(null);
  }, []);

  // Computed values
  const hasSelection = selectedProjects.size > 0;
  const selectionCount = selectedProjects.size;
  const isAllSelected = useCallback((projects: SEOGPTProject[]) => {
    return projects.length > 0 && projects.every(p => selectedProjects.has(p.project_id));
  }, [selectedProjects]);

  return {
    // State
    selectedProjects,
    loading,
    error,
    lastOperation,
    
    // Selection actions
    selectProject,
    deselectProject,
    selectAll,
    deselectAll,
    toggleProject,
    
    // Bulk operations
    bulkDelete,
    bulkStatusChange,
    bulkAnalyze,
    bulkExport,
    bulkDuplicate,
    
    // Utility
    clearSelection,
    clearError,
    clearLastOperation,
    
    // Computed values
    hasSelection,
    selectionCount,
    isAllSelected
  };
};

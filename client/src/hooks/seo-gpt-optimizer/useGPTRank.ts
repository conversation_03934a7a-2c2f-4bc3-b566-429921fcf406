/**
 * SEO & GPT Optimizer™ - GPT Rank Hook
 * Custom hook for managing GPT Rank analysis
 */

import { useState, useCallback, useEffect, useRef } from 'react';
import { seoGptAPI } from '../../services/seo-gpt-optimizer/api';
import {
  GPTRankAnalysis,
  ContentAnalysisRequest,
  LoadingState,
  ErrorState
} from '../../types/seo-gpt-optimizer';

interface UseGPTRankReturn {
  // State
  analysis: GPTRankAnalysis | null;
  loading: LoadingState;
  error: ErrorState;
  
  // Actions
  analyzeContent: (request: ContentAnalysisRequest) => Promise<void>;
  analyzeContentRealtime: (content: string, topic: string) => void;
  clearAnalysis: () => void;
  clearError: () => void;
  
  // Computed values
  hasAnalysis: boolean;
  gptRankScore: number;
  scoreGrade: string;
  confidenceLevel: string;
  needsImprovement: boolean;
}

export const useGPTRank = (): UseGPTRankReturn => {
  const [analysis, setAnalysis] = useState<GPTRankAnalysis | null>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    message: '',
    progress: 0
  });
  const [error, setError] = useState<ErrorState>({
    hasError: false,
    message: '',
    code: ''
  });

  // Refs for debouncing
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const lastAnalysisRef = useRef<string>('');

  const analyzeContent = useCallback(async (request: ContentAnalysisRequest) => {
    try {
      setLoading({
        isLoading: true,
        message: 'Analizando contenido...',
        progress: 0
      });
      setError({ hasError: false });

      // Simulate progress for different analysis components
      const progressSteps = [
        { progress: 15, message: 'Analizando similitud semántica...' },
        { progress: 30, message: 'Evaluando coherencia lógica...' },
        { progress: 45, message: 'Verificando señales de autoridad...' },
        { progress: 60, message: 'Calculando citabilidad...' },
        { progress: 75, message: 'Analizando claridad...' },
        { progress: 90, message: 'Evaluando completitud...' }
      ];

      // Update progress every 1 second
      const progressInterval = setInterval(() => {
        setLoading(prev => {
          const nextStep = progressSteps.find(step => step.progress > prev.progress);
          if (nextStep) {
            return {
              isLoading: true,
              message: nextStep.message,
              progress: nextStep.progress
            };
          }
          return prev;
        });
      }, 1000);

      const response = await seoGptAPI.analyzeContent(request);
      
      clearInterval(progressInterval);

      if (response.status === 'success' && response.data) {
        setAnalysis(response.data);
        setLoading({
          isLoading: false,
          message: 'Análisis completado',
          progress: 100
        });
      } else {
        throw new Error(response.error_message || 'Error en el análisis');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'ANALYSIS_ERROR'
      });
      setLoading({
        isLoading: false,
        message: 'Error en el análisis',
        progress: 0
      });
    }
  }, []);

  const analyzeContentRealtime = useCallback((content: string, topic: string) => {
    // Skip if content is too short
    if (content.length < 50) {
      return;
    }

    // Clear previous timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Don't analyze if content hasn't changed significantly
    const contentKey = `${content.slice(0, 100)}-${topic}`;
    if (contentKey === lastAnalysisRef.current) {
      return;
    }

    // Set simple loading state (no progress)
    setLoading({
      isLoading: true,
      message: 'Analizando...',
      progress: 0
    });

    // Longer debounce to reduce API calls
    debounceTimeoutRef.current = setTimeout(async () => {
      try {
        lastAnalysisRef.current = contentKey;

        const response = await seoGptAPI.analyzeContentRealtime(content, topic, 0);

        if (response.status === 'success' && response.data) {
          setAnalysis(response.data);
          setError({ hasError: false });
        } else {
          throw new Error(response.error_message || 'Error en análisis');
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
        setError({
          hasError: true,
          message: errorMessage,
          code: 'REALTIME_ANALYSIS_ERROR'
        });
      } finally {
        setLoading({
          isLoading: false,
          message: '',
          progress: 0
        });
      }
    }, 1500); // Balanced debounce - not too fast, not too slow
  }, []);

  const clearAnalysis = useCallback(() => {
    setAnalysis(null);
    setLoading({ isLoading: false, progress: 0 });
    setError({ hasError: false });
    lastAnalysisRef.current = '';
    
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
  }, []);

  const clearError = useCallback(() => {
    setError({ hasError: false });
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Computed values
  const hasAnalysis = analysis !== null;
  const gptRankScore = analysis?.gpt_rank_score || 0;
  const scoreGrade = analysis?.score_grade || 'N/A';
  const confidenceLevel = analysis?.confidence_level || 'unknown';
  const needsImprovement = gptRankScore < 70;

  return {
    // State
    analysis,
    loading,
    error,
    
    // Actions
    analyzeContent,
    analyzeContentRealtime,
    clearAnalysis,
    clearError,
    
    // Computed values
    hasAnalysis,
    gptRankScore,
    scoreGrade,
    confidenceLevel,
    needsImprovement
  };
};

/**
 * SEO & GPT Optimizer™ - Keyboard Shortcuts Hook
 * Custom hook for handling keyboard shortcuts in the content builder
 */

import { useEffect, useCallback } from 'react';

interface KeyboardShortcuts {
  onSave?: () => void;
  onTogglePanel?: () => void;
  onToggleFullscreen?: () => void;
  onTogglePreview?: () => void;
  onNewDocument?: () => void;
  onExport?: () => void;
}

export const useKeyboardShortcuts = (shortcuts: KeyboardShortcuts) => {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Check if user is typing in an input field
    const target = event.target as HTMLElement;
    const isInputField = target.tagName === 'INPUT' || 
                        target.tagName === 'TEXTAREA' || 
                        target.contentEditable === 'true';

    // Ctrl/Cmd + S - Save
    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
      event.preventDefault();
      shortcuts.onSave?.();
      return;
    }

    // Ctrl/Cmd + / - Toggle Panel
    if ((event.ctrlKey || event.metaKey) && event.key === '/') {
      event.preventDefault();
      shortcuts.onTogglePanel?.();
      return;
    }

    // F11 - Toggle Fullscreen
    if (event.key === 'F11') {
      event.preventDefault();
      shortcuts.onToggleFullscreen?.();
      return;
    }

    // Ctrl/Cmd + Shift + P - Toggle Preview
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'P') {
      event.preventDefault();
      shortcuts.onTogglePreview?.();
      return;
    }

    // Ctrl/Cmd + N - New Document (only if not in input field)
    if ((event.ctrlKey || event.metaKey) && event.key === 'n' && !isInputField) {
      event.preventDefault();
      shortcuts.onNewDocument?.();
      return;
    }

    // Ctrl/Cmd + E - Export (only if not in input field)
    if ((event.ctrlKey || event.metaKey) && event.key === 'e' && !isInputField) {
      event.preventDefault();
      shortcuts.onExport?.();
      return;
    }

    // Escape - Close modals or exit fullscreen
    if (event.key === 'Escape') {
      if (document.fullscreenElement) {
        document.exitFullscreen?.();
      }
      return;
    }
  }, [shortcuts]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // Return available shortcuts for display
  return {
    shortcuts: [
      { key: 'Ctrl+S', description: 'Guardar contenido', action: shortcuts.onSave },
      { key: 'Ctrl+/', description: 'Mostrar/ocultar panel', action: shortcuts.onTogglePanel },
      { key: 'F11', description: 'Pantalla completa', action: shortcuts.onToggleFullscreen },
      { key: 'Ctrl+Shift+P', description: 'Vista previa', action: shortcuts.onTogglePreview },
      { key: 'Ctrl+N', description: 'Nuevo documento', action: shortcuts.onNewDocument },
      { key: 'Ctrl+E', description: 'Exportar', action: shortcuts.onExport },
      { key: 'Esc', description: 'Salir de pantalla completa', action: () => {} }
    ].filter(shortcut => shortcut.action)
  };
};

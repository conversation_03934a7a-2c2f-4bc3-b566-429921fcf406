/**
 * SEO & GPT Optimizer™ - Navigation Hook
 * Custom hook for managing navigation state and actions
 */

import { useState, useCallback, useEffect } from 'react';
import { useLocation } from 'wouter';
import { toast } from 'react-hot-toast';

interface NavigationState {
  currentPath: string;
  previousPath: string | null;
  isLoading: boolean;
  breadcrumbs: BreadcrumbItem[];
}

interface BreadcrumbItem {
  label: string;
  path: string;
  isActive: boolean;
}

interface UseNavigationReturn {
  // State
  navigationState: NavigationState;
  
  // Actions
  navigateTo: (path: string, options?: NavigationOptions) => void;
  goBack: () => void;
  goForward: () => void;
  refresh: () => void;
  
  // Utilities
  isCurrentPath: (path: string) => boolean;
  isParentPath: (path: string) => boolean;
  generateBreadcrumbs: () => BreadcrumbItem[];
  
  // Quick navigation
  goToDashboard: () => void;
  goToResearch: () => void;
  goToContentBuilder: (projectId?: string) => void;
  goToAnalytics: () => void;
  goToProjects: () => void;
  createNewProject: () => void;
}

interface NavigationOptions {
  replace?: boolean;
  showToast?: boolean;
  toastMessage?: string;
}

const pathLabels: Record<string, string> = {
  '/dashboard': 'Dashboard',
  '/dashboard/herramientas/seo-gpt-optimizer': 'SEO & GPT Optimizer™',
  '/dashboard/herramientas/seo-gpt-optimizer/research': 'Research Engine',
  '/dashboard/herramientas/seo-gpt-optimizer/content-builder': 'Content Builder',
  '/dashboard/herramientas/seo-gpt-optimizer/analytics': 'Analytics',
  '/dashboard/herramientas/seo-gpt-optimizer/projects': 'Proyectos'
};

export const useNavigation = (): UseNavigationReturn => {
  const [location, setLocation] = useLocation();

  const [navigationState, setNavigationState] = useState<NavigationState>({
    currentPath: location,
    previousPath: null,
    isLoading: false,
    breadcrumbs: []
  });

  // Update navigation state when location changes
  useEffect(() => {
    setNavigationState(prev => ({
      ...prev,
      previousPath: prev.currentPath,
      currentPath: location,
      breadcrumbs: generateBreadcrumbs()
    }));
  }, [location]);

  // Generate breadcrumbs based on current path
  const generateBreadcrumbs = useCallback((): BreadcrumbItem[] => {
    const pathSegments = location.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];
    let currentPath = '';

    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === pathSegments.length - 1;

      // Get label for this path
      let label = pathLabels[currentPath] || segment;

      // Handle dynamic segments (like project IDs)
      if (!pathLabels[currentPath] && segment.length > 10) {
        // Likely a UUID or ID, use previous segment's context
        const parentPath = currentPath.substring(0, currentPath.lastIndexOf('/'));
        if (pathLabels[parentPath]) {
          label = 'Proyecto'; // or fetch actual project name
        }
      }

      breadcrumbs.push({
        label: label.charAt(0).toUpperCase() + label.slice(1),
        path: currentPath,
        isActive: isLast
      });
    });

    return breadcrumbs;
  }, [location]);

  // Main navigation function
  const navigateTo = useCallback((
    path: string,
    options: NavigationOptions = {}
  ) => {
    const { replace = false, showToast = false, toastMessage } = options;

    setNavigationState(prev => ({ ...prev, isLoading: true }));

    try {
      setLocation(path);

      if (showToast && toastMessage) {
        toast.success(toastMessage);
      }
    } catch (error) {
      console.error('Navigation error:', error);
      toast.error('Error al navegar');
    } finally {
      setNavigationState(prev => ({ ...prev, isLoading: false }));
    }
  }, [setLocation]);

  // Browser navigation
  const goBack = useCallback(() => {
    window.history.back();
  }, []);

  const goForward = useCallback(() => {
    window.history.forward();
  }, []);

  const refresh = useCallback(() => {
    window.location.reload();
  }, []);

  // Utility functions
  const isCurrentPath = useCallback((path: string): boolean => {
    return location === path;
  }, [location]);

  const isParentPath = useCallback((path: string): boolean => {
    return location.startsWith(path) && location !== path;
  }, [location]);

  // Quick navigation functions
  const goToDashboard = useCallback(() => {
    navigateTo('/dashboard/herramientas/seo-gpt-optimizer', {
      showToast: true,
      toastMessage: 'Navegando al dashboard'
    });
  }, [navigateTo]);

  const goToResearch = useCallback(() => {
    navigateTo('/dashboard/herramientas/seo-gpt-optimizer/research', {
      showToast: true,
      toastMessage: 'Abriendo Research Engine'
    });
  }, [navigateTo]);

  const goToContentBuilder = useCallback((projectId?: string) => {
    const path = projectId
      ? `/dashboard/herramientas/seo-gpt-optimizer/content-builder/${projectId}`
      : '/dashboard/herramientas/seo-gpt-optimizer/content-builder';

    navigateTo(path, {
      showToast: true,
      toastMessage: projectId ? 'Abriendo proyecto' : 'Abriendo Content Builder'
    });
  }, [navigateTo]);

  const goToAnalytics = useCallback(() => {
    navigateTo('/dashboard/herramientas/seo-gpt-optimizer/analytics', {
      showToast: true,
      toastMessage: 'Cargando analytics'
    });
  }, [navigateTo]);

  const goToProjects = useCallback(() => {
    navigateTo('/dashboard/herramientas/seo-gpt-optimizer/projects', {
      showToast: true,
      toastMessage: 'Cargando proyectos'
    });
  }, [navigateTo]);

  const createNewProject = useCallback(() => {
    navigateTo('/dashboard/herramientas/seo-gpt-optimizer/projects?create=true', {
      showToast: true,
      toastMessage: 'Creando nuevo proyecto'
    });
  }, [navigateTo]);

  return {
    // State
    navigationState: {
      ...navigationState,
      breadcrumbs: generateBreadcrumbs()
    },
    
    // Actions
    navigateTo,
    goBack,
    goForward,
    refresh,
    
    // Utilities
    isCurrentPath,
    isParentPath,
    generateBreadcrumbs,
    
    // Quick navigation
    goToDashboard,
    goToResearch,
    goToContentBuilder,
    goToAnalytics,
    goToProjects,
    createNewProject
  };
};

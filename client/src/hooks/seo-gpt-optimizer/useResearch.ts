/**
 * SEO & GPT Optimizer™ - Research Hook
 * Custom hook for managing research operations
 */

import { useState, useCallback } from 'react';
import { seoGptAPI } from '../../services/seo-gpt-optimizer/api';
import {
  ResearchRequest,
  ResearchResults,
  LoadingState,
  ErrorState
} from '../../types/seo-gpt-optimizer';

interface UseResearchReturn {
  // State
  researchResults: ResearchResults | null;
  loading: LoadingState;
  error: ErrorState;
  
  // Actions
  conductResearch: (request: ResearchRequest) => Promise<void>;
  clearResults: () => void;
  clearError: () => void;
  
  // Computed values
  hasResults: boolean;
  researchConfidence: number;
  processingTime: number;
}

export const useResearch = (): UseResearchReturn => {
  const [researchResults, setResearchResults] = useState<ResearchResults | null>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    message: '',
    progress: 0
  });
  const [error, setError] = useState<ErrorState>({
    hasError: false,
    message: '',
    code: ''
  });

  const conductResearch = useCallback(async (request: ResearchRequest) => {
    try {
      setLoading({
        isLoading: true,
        message: 'Iniciando investigación...',
        progress: 0
      });
      setError({ hasError: false });
      setResearchResults(null);

      // Simulate progress updates
      const progressSteps = [
        { progress: 10, message: 'Analizando intención de búsqueda...' },
        { progress: 25, message: 'Obteniendo resultados de Google...' },
        { progress: 45, message: 'Extrayendo insights sociales...' },
        { progress: 65, message: 'Generando respuesta de referencia GPT...' },
        { progress: 80, message: 'Extrayendo entidades y preguntas...' },
        { progress: 95, message: 'Analizando oportunidades de contenido...' }
      ];

      // Update progress every 2 seconds
      const progressInterval = setInterval(() => {
        setLoading(prev => {
          const nextStep = progressSteps.find(step => step.progress > prev.progress);
          if (nextStep) {
            return {
              isLoading: true,
              message: nextStep.message,
              progress: nextStep.progress
            };
          }
          return prev;
        });
      }, 2000);

      const response = await seoGptAPI.conductResearch(request);
      
      clearInterval(progressInterval);

      if (response.status === 'success' && response.data) {
        setResearchResults(response.data);
        setLoading({
          isLoading: false,
          message: 'Investigación completada',
          progress: 100
        });
      } else {
        throw new Error(response.error_message || 'Error en la investigación');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'RESEARCH_ERROR'
      });
      setLoading({
        isLoading: false,
        message: 'Error en la investigación',
        progress: 0
      });
    }
  }, []);

  const clearResults = useCallback(() => {
    setResearchResults(null);
    setLoading({ isLoading: false, progress: 0 });
    setError({ hasError: false });
  }, []);

  const clearError = useCallback(() => {
    setError({ hasError: false });
  }, []);

  // Computed values
  const hasResults = researchResults !== null;
  const researchConfidence = researchResults?.research_summary?.research_confidence || 0;
  const processingTime = researchResults?.processing_time || 0;

  return {
    // State
    researchResults,
    loading,
    error,
    
    // Actions
    conductResearch,
    clearResults,
    clearError,
    
    // Computed values
    hasResults,
    researchConfidence,
    processingTime
  };
};

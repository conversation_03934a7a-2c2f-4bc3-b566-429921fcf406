/**
 * SAIO Agent Hook - World's First Real SAIO Optimizer
 * Based on comprehensive research of 500+ AI search queries
 */

import { useState, useCallback } from 'react';

interface SAIOAnalysisRequest {
  project_id: string;
  content: string;
  analysis_type?: string;
}

interface SAIOContentRequest {
  project_id: string;
  topic: string;
  content_type?: string;
  optimize_for?: string[];
}

interface SAIOScore {
  saio_score: number;
  citation_probability: string;
  component_scores: {
    bing_seo_signals: number;
    content_structure: number;
    authority_signals: number;
    multimedia_content: number;
    content_freshness: number;
    schema_markup: number;
  };
  recommendations: Array<{
    priority: string;
    category: string;
    title: string;
    description: string;
    impact: string;
    research_basis: string;
  }>;
  ai_readiness: {
    google_sge: {
      ready: boolean;
      key_factors: string[];
      recommendation: string;
    };
    chatgpt: {
      ready: boolean;
      key_factors: string[];
      recommendation: string;
    };
    perplexity: {
      ready: boolean;
      key_factors: string[];
      recommendation: string;
    };
  };
}

interface SAIOAnalysisResponse {
  status: string;
  project_id: string;
  message: string;
  saio_results: SAIOScore;
  analysis_type: string;
  world_first: boolean;
}

interface SAIOContentResponse {
  status: string;
  project_id: string;
  message: string;
  data: {
    content: string;
    saio_analysis: SAIOScore;
    template_used: string;
    optimization_targets: string[];
    world_first: boolean;
    research_based: boolean;
  };
}

interface LoadingState {
  isLoading: boolean;
  message: string;
  progress: number;
}

interface ErrorState {
  hasError: boolean;
  message: string;
}

export const useSAIOAgent = () => {
  const [saioAnalysis, setSAIOAnalysis] = useState<SAIOScore | null>(null);
  const [generatedContent, setGeneratedContent] = useState<string | null>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    message: '',
    progress: 0
  });
  const [error, setError] = useState<ErrorState>({
    hasError: false,
    message: ''
  });

  const analyzeSAIOContent = useCallback(async (request: SAIOAnalysisRequest) => {
    try {
      setLoading({
        isLoading: true,
        message: '🧠 SAIO Agent analizando contenido...',
        progress: 25
      });
      setError({ hasError: false, message: '' });

      const response = await fetch('/api/v1/seo-gpt/content/analyze-saio', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error analyzing SAIO content');
      }

      setLoading(prev => ({
        ...prev,
        message: '📊 Calculando score SAIO...',
        progress: 75
      }));

      const data: SAIOAnalysisResponse = await response.json();

      setSAIOAnalysis(data.saio_results);
      
      setLoading({
        isLoading: false,
        message: '✅ Análisis SAIO completado',
        progress: 100
      });

      // Clear loading after brief delay
      setTimeout(() => {
        setLoading({
          isLoading: false,
          message: '',
          progress: 0
        });
      }, 1000);

    } catch (err) {
      console.error('SAIO analysis error:', err);
      setError({
        hasError: true,
        message: err instanceof Error ? err.message : 'Error desconocido en análisis SAIO'
      });
      setLoading({
        isLoading: false,
        message: '',
        progress: 0
      });
    }
  }, []);

  const generateSAIOContent = useCallback(async (request: SAIOContentRequest) => {
    try {
      setLoading({
        isLoading: true,
        message: '🚀 Generando contenido SAIO optimizado...',
        progress: 20
      });
      setError({ hasError: false, message: '' });

      setLoading(prev => ({
        ...prev,
        message: '🧠 SAIO Agent creando contenido...',
        progress: 50
      }));

      const response = await fetch('/api/v1/seo-gpt/content/generate-saio', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error generating SAIO content');
      }

      setLoading(prev => ({
        ...prev,
        message: '📊 Analizando contenido generado...',
        progress: 85
      }));

      const data: SAIOContentResponse = await response.json();

      setGeneratedContent(data.data.content);
      setSAIOAnalysis(data.data.saio_analysis);
      
      setLoading({
        isLoading: false,
        message: '✅ Contenido SAIO generado',
        progress: 100
      });

      // Clear loading after brief delay
      setTimeout(() => {
        setLoading({
          isLoading: false,
          message: '',
          progress: 0
        });
      }, 1000);

      return data.data;

    } catch (err) {
      console.error('SAIO content generation error:', err);
      setError({
        hasError: true,
        message: err instanceof Error ? err.message : 'Error desconocido generando contenido SAIO'
      });
      setLoading({
        isLoading: false,
        message: '',
        progress: 0
      });
      return null;
    }
  }, []);

  const clearError = useCallback(() => {
    setError({ hasError: false, message: '' });
  }, []);

  const clearAnalysis = useCallback(() => {
    setSAIOAnalysis(null);
    setGeneratedContent(null);
    setError({ hasError: false, message: '' });
  }, []);

  // Helper functions
  const getSAIOGrade = useCallback((score: number): string => {
    if (score >= 90) return 'A+';
    if (score >= 85) return 'A';
    if (score >= 80) return 'A-';
    if (score >= 75) return 'B+';
    if (score >= 70) return 'B';
    if (score >= 65) return 'B-';
    if (score >= 60) return 'C+';
    if (score >= 55) return 'C';
    if (score >= 50) return 'C-';
    if (score >= 40) return 'D';
    return 'F';
  }, []);

  const getCitationProbabilityText = useCallback((probability: string): string => {
    const texts = {
      'muy_alta': 'Muy Alta (85%+)',
      'alta': 'Alta (70-84%)',
      'media': 'Media (55-69%)',
      'baja': 'Baja (40-54%)',
      'muy_baja': 'Muy Baja (<40%)'
    };
    return texts[probability as keyof typeof texts] || 'Desconocida';
  }, []);

  const getHighPriorityRecommendations = useCallback(() => {
    if (!saioAnalysis?.recommendations) return [];
    return saioAnalysis.recommendations.filter(rec => rec.priority === 'high');
  }, [saioAnalysis]);

  return {
    // State
    saioAnalysis,
    generatedContent,
    loading,
    error,
    
    // Actions
    analyzeSAIOContent,
    generateSAIOContent,
    clearError,
    clearAnalysis,
    
    // Computed values
    hasSAIOAnalysis: !!saioAnalysis,
    saioScore: saioAnalysis?.saio_score || 0,
    saioGrade: saioAnalysis ? getSAIOGrade(saioAnalysis.saio_score) : 'N/A',
    citationProbability: saioAnalysis ? getCitationProbabilityText(saioAnalysis.citation_probability) : 'N/A',
    isAnalyzing: loading.isLoading,
    
    // Helper functions
    getSAIOGrade,
    getCitationProbabilityText,
    getHighPriorityRecommendations,
    
    // AI Readiness
    googleReady: saioAnalysis?.ai_readiness?.google_sge?.ready || false,
    chatgptReady: saioAnalysis?.ai_readiness?.chatgpt?.ready || false,
    perplexityReady: saioAnalysis?.ai_readiness?.perplexity?.ready || false,
  };
};

export default useSAIOAgent;

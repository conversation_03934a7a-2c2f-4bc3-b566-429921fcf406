/**
 * Hook for managing saved research investigations
 */

import { useState, useEffect, useCallback } from 'react';

export interface SavedResearch {
  id: string;
  topic: string;
  results: any;
  confidence: number;
  processingTime: number;
  savedAt: string;
}

const STORAGE_KEY = 'saved_research';
const MAX_SAVED_RESEARCH = 10;

export const useSavedResearch = () => {
  const [savedResearch, setSavedResearch] = useState<SavedResearch[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load saved research from localStorage
  const loadSavedResearch = useCallback(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        setSavedResearch(Array.isArray(parsed) ? parsed : []);
      }
    } catch (error) {
      console.error('Error loading saved research:', error);
      setSavedResearch([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save research to localStorage
  const saveResearch = useCallback((researchData: {
    topic: string;
    results: any;
    confidence: number;
    processingTime: number;
  }) => {
    try {
      const newResearch: SavedResearch = {
        id: `research_${Date.now()}`,
        topic: researchData.topic,
        results: researchData.results,
        confidence: researchData.confidence,
        processingTime: researchData.processingTime,
        savedAt: new Date().toISOString()
      };

      const updated = [newResearch, ...savedResearch].slice(0, MAX_SAVED_RESEARCH);
      setSavedResearch(updated);
      localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
      
      return newResearch.id;
    } catch (error) {
      console.error('Error saving research:', error);
      throw new Error('No se pudo guardar la investigación');
    }
  }, [savedResearch]);

  // Delete research
  const deleteResearch = useCallback((id: string) => {
    try {
      const updated = savedResearch.filter(research => research.id !== id);
      setSavedResearch(updated);
      localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
      return true;
    } catch (error) {
      console.error('Error deleting research:', error);
      return false;
    }
  }, [savedResearch]);

  // Get research by ID
  const getResearchById = useCallback((id: string) => {
    return savedResearch.find(research => research.id === id) || null;
  }, [savedResearch]);

  // Search research
  const searchResearch = useCallback((query: string) => {
    if (!query.trim()) return savedResearch;
    
    const lowercaseQuery = query.toLowerCase();
    return savedResearch.filter(research =>
      research.topic.toLowerCase().includes(lowercaseQuery)
    );
  }, [savedResearch]);

  // Clear all research
  const clearAllResearch = useCallback(() => {
    try {
      setSavedResearch([]);
      localStorage.removeItem(STORAGE_KEY);
      return true;
    } catch (error) {
      console.error('Error clearing research:', error);
      return false;
    }
  }, []);

  // Check if research exists
  const hasResearch = useCallback((topic: string) => {
    return savedResearch.some(research => 
      research.topic.toLowerCase() === topic.toLowerCase()
    );
  }, [savedResearch]);

  // Get research count
  const getResearchCount = useCallback(() => {
    return savedResearch.length;
  }, [savedResearch]);

  // Get recent research (last 5)
  const getRecentResearch = useCallback(() => {
    return savedResearch.slice(0, 5);
  }, [savedResearch]);

  // Export research as JSON
  const exportResearch = useCallback((research: SavedResearch) => {
    try {
      const dataStr = JSON.stringify(research, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `research-${research.topic}-${Date.now()}.json`;
      link.click();
      URL.revokeObjectURL(url);
      return true;
    } catch (error) {
      console.error('Error exporting research:', error);
      return false;
    }
  }, []);

  // Load data on mount
  useEffect(() => {
    loadSavedResearch();
  }, [loadSavedResearch]);

  return {
    // State
    savedResearch,
    isLoading,
    
    // Actions
    saveResearch,
    deleteResearch,
    clearAllResearch,
    exportResearch,
    
    // Queries
    getResearchById,
    searchResearch,
    hasResearch,
    getResearchCount,
    getRecentResearch,
    
    // Utils
    reload: loadSavedResearch
  };
};

export default useSavedResearch;

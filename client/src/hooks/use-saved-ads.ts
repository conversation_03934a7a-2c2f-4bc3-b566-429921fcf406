import { useState, useEffect, useCallback } from 'react';
import { SavedAd } from '@/types/ad-creator-types';

const SAVED_ADS_KEY = 'emma_saved_ads';

export function useSavedAds() {
  const [savedAds, setSavedAds] = useState<SavedAd[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Cargar anuncios guardados del localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(SAVED_ADS_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        setSavedAds(Array.isArray(parsed) ? parsed : []);
      }
    } catch (error) {
      console.error('Error loading saved ads:', error);
      setSavedAds([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Guardar en localStorage cuando cambie el estado
  const saveToStorage = useCallback((ads: SavedAd[]) => {
    try {
      localStorage.setItem(SAVED_ADS_KEY, JSON.stringify(ads));
    } catch (error) {
      console.error('Error saving ads to localStorage:', error);
    }
  }, []);

  // Guardar un nuevo anuncio
  const saveAd = useCallback((ad: Omit<SavedAd, 'id' | 'timestamp'>) => {
    const newAd: SavedAd = {
      ...ad,
      id: `ad_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      isFavorite: true,
    };

    setSavedAds(prev => {
      // Verificar si ya existe
      if (prev.some(saved => saved.image_url === ad.image_url)) {
        return prev;
      }
      const updated = [newAd, ...prev];
      saveToStorage(updated);
      return updated;
    });

    return newAd;
  }, [saveToStorage]);

  // Eliminar un anuncio
  const removeAd = useCallback((id: string) => {
    setSavedAds(prev => {
      const updated = prev.filter(ad => ad.id !== id);
      saveToStorage(updated);
      return updated;
    });
  }, [saveToStorage]);

  // Verificar si un anuncio está guardado
  const isAdSaved = useCallback((imageUrl: string) => {
    return savedAds.some(ad => ad.image_url === imageUrl);
  }, [savedAds]);

  // Alternar favorito
  const toggleFavorite = useCallback((id: string) => {
    setSavedAds(prev => {
      const updated = prev.map(ad => 
        ad.id === id ? { ...ad, isFavorite: !ad.isFavorite } : ad
      );
      saveToStorage(updated);
      return updated;
    });
  }, [saveToStorage]);

  // Limpiar todos los anuncios
  const clearAllAds = useCallback(() => {
    setSavedAds([]);
    saveToStorage([]);
  }, [saveToStorage]);

  // Obtener estadísticas
  const getStats = useCallback(() => {
    const total = savedAds.length;
    const favorites = savedAds.filter(ad => ad.isFavorite).length;
    const platforms = [...new Set(savedAds.map(ad => ad.platform).filter(Boolean))];
    
    return {
      total,
      favorites,
      platforms: platforms.length,
      platformList: platforms,
    };
  }, [savedAds]);

  return {
    savedAds,
    isLoading,
    saveAd,
    removeAd,
    isAdSaved,
    toggleFavorite,
    clearAllAds,
    getStats,
  };
}

import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuth } from '@/hooks/use-auth'
import { designAnalysisService } from '@/services/designAnalysisService'
import { useToast } from '@/hooks/use-toast'
import type { DesignAnalysis, CreateDesignAnalysisData, UpdateDesignAnalysisData } from '@/lib/supabase'

/**
 * Hook for managing design analysis operations
 */
export function useDesignAnalysis() {
  const { user } = useAuth()
  const { toast } = useToast()
  const queryClient = useQueryClient()

  // Query for getting user's analyses
  const {
    data: analyses = [],
    isLoading: isLoadingAnalyses,
    error: analysesError,
    refetch: refetchAnalyses
  } = useQuery({
    queryKey: ['design-analyses', user?.id],
    queryFn: () => designAnalysisService.getUserAnalyses(user?.id || '', {
      limit: 100,
      orderBy: 'created_at',
      orderDirection: 'desc'
    }),
    enabled: !!user?.id && user.id !== 'anonymous',
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Query for getting user stats
  const {
    data: userStats,
    isLoading: isLoadingStats
  } = useQuery({
    queryKey: ['design-analysis-stats', user?.id],
    queryFn: () => designAnalysisService.getUserStats(user?.id || ''),
    enabled: !!user?.id && user.id !== 'anonymous',
    staleTime: 10 * 60 * 1000, // 10 minutes
  })

  // Mutation for saving analysis
  const saveAnalysisMutation = useMutation({
    mutationFn: (analysisData: CreateDesignAnalysisData) => 
      designAnalysisService.saveAnalysis(analysisData),
    onSuccess: (savedAnalysis) => {
      queryClient.invalidateQueries({ queryKey: ['design-analyses'] })
      queryClient.invalidateQueries({ queryKey: ['design-analysis-stats'] })
      toast({
        title: "Análisis guardado",
        description: "Tu análisis de diseño ha sido guardado exitosamente",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error al guardar",
        description: error.message,
        variant: "destructive",
      })
    }
  })

  // Mutation for updating analysis
  const updateAnalysisMutation = useMutation({
    mutationFn: (updateData: UpdateDesignAnalysisData) => 
      designAnalysisService.updateAnalysis(updateData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['design-analyses'] })
      toast({
        title: "Análisis actualizado",
        description: "Los cambios han sido guardados",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error al actualizar",
        description: error.message,
        variant: "destructive",
      })
    }
  })

  // Mutation for toggling favorite
  const toggleFavoriteMutation = useMutation({
    mutationFn: ({ id, isFavorite }: { id: string; isFavorite: boolean }) => 
      designAnalysisService.toggleFavorite(id, isFavorite),
    onSuccess: (updatedAnalysis) => {
      queryClient.invalidateQueries({ queryKey: ['design-analyses'] })
      queryClient.invalidateQueries({ queryKey: ['design-analysis-stats'] })
      toast({
        title: updatedAnalysis.is_favorite ? "Agregado a favoritos" : "Removido de favoritos",
        description: updatedAnalysis.is_favorite 
          ? "El análisis ha sido agregado a tus favoritos" 
          : "El análisis ha sido removido de tus favoritos",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    }
  })

  // Mutation for deleting analysis
  const deleteAnalysisMutation = useMutation({
    mutationFn: (id: string) => designAnalysisService.deleteAnalysis(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['design-analyses'] })
      queryClient.invalidateQueries({ queryKey: ['design-analysis-stats'] })
      toast({
        title: "Análisis eliminado",
        description: "El análisis ha sido eliminado exitosamente",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error al eliminar",
        description: error.message,
        variant: "destructive",
      })
    }
  })

  // Mutation for updating tags
  const updateTagsMutation = useMutation({
    mutationFn: ({ id, tags }: { id: string; tags: string[] }) => 
      designAnalysisService.updateTags(id, tags),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['design-analyses'] })
      toast({
        title: "Etiquetas actualizadas",
        description: "Las etiquetas han sido actualizadas",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error al actualizar etiquetas",
        description: error.message,
        variant: "destructive",
      })
    }
  })

  // Mutation for updating notes
  const updateNotesMutation = useMutation({
    mutationFn: ({ id, notes }: { id: string; notes: string }) => 
      designAnalysisService.updateNotes(id, notes),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['design-analyses'] })
      toast({
        title: "Notas actualizadas",
        description: "Las notas han sido actualizadas",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error al actualizar notas",
        description: error.message,
        variant: "destructive",
      })
    }
  })

  // Helper functions
  const getFavoriteAnalyses = () => analyses.filter(analysis => analysis.is_favorite)
  
  const getAnalysesByTool = (toolType: string) => 
    analyses.filter(analysis => analysis.tool_type === toolType)
  
  const getRecentAnalyses = (days: number = 7) => {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - days)
    return analyses.filter(analysis => 
      new Date(analysis.created_at) > cutoffDate
    )
  }

  const getAnalysisByScore = (minScore: number, maxScore: number = 100) =>
    analyses.filter(analysis => 
      analysis.overall_score >= minScore && analysis.overall_score <= maxScore
    )

  return {
    // Data
    analyses,
    userStats,
    
    // Loading states
    isLoadingAnalyses,
    isLoadingStats,
    
    // Error states
    analysesError,
    
    // Mutations
    saveAnalysis: saveAnalysisMutation.mutate,
    updateAnalysis: updateAnalysisMutation.mutate,
    toggleFavorite: toggleFavoriteMutation.mutate,
    deleteAnalysis: deleteAnalysisMutation.mutate,
    updateTags: updateTagsMutation.mutate,
    updateNotes: updateNotesMutation.mutate,
    
    // Mutation states
    isSaving: saveAnalysisMutation.isPending,
    isUpdating: updateAnalysisMutation.isPending,
    isTogglingFavorite: toggleFavoriteMutation.isPending,
    isDeleting: deleteAnalysisMutation.isPending,
    isUpdatingTags: updateTagsMutation.isPending,
    isUpdatingNotes: updateNotesMutation.isPending,
    
    // Helper functions
    getFavoriteAnalyses,
    getAnalysesByTool,
    getRecentAnalyses,
    getAnalysisByScore,
    refetchAnalyses,
    
    // User authentication status
    isAuthenticated: !!user?.id && user.id !== 'anonymous'
  }
}

/**
 * Hook for getting a specific analysis by ID
 */
export function useDesignAnalysisById(analysisId: string) {
  const { user } = useAuth()
  
  return useQuery({
    queryKey: ['design-analysis', analysisId],
    queryFn: () => designAnalysisService.getAnalysisById(analysisId),
    enabled: !!analysisId && !!user?.id && user.id !== 'anonymous',
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook for managing analysis history and pagination
 */
export function useDesignAnalysisHistory(options?: {
  limit?: number
  toolType?: string
  isFavorite?: boolean
}) {
  const { user } = useAuth()
  const [page, setPage] = useState(0)
  const limit = options?.limit || 20

  const {
    data,
    isLoading,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage
  } = useQuery({
    queryKey: ['design-analyses-history', user?.id, options, page],
    queryFn: () => designAnalysisService.getUserAnalyses(user?.id || '', {
      limit,
      offset: page * limit,
      toolType: options?.toolType,
      isFavorite: options?.isFavorite,
      orderBy: 'created_at',
      orderDirection: 'desc'
    }),
    enabled: !!user?.id && user.id !== 'anonymous',
    staleTime: 5 * 60 * 1000,
  })

  const loadMore = () => {
    if (data && data.length === limit) {
      setPage(prev => prev + 1)
    }
  }

  return {
    analyses: data || [],
    isLoading,
    error,
    loadMore,
    hasMore: data && data.length === limit,
    page,
    setPage
  }
}

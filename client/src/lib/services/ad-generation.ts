/**
 * Ad Generation Service - Handles AI-powered ad creation
 */

export interface AdGenerationOptions {
  prompt: string;
  size: string;
  headline?: string;
  punchline?: string;
  cta?: string;
  referenceImage?: File;
  style?: string;
  model?: string;
}

export interface AdGenerationResult {
  success: boolean;
  image_url?: string;
  revised_prompt?: string;
  response_id?: string;
  metadata?: any;
  error?: string;
}

export interface EditWithReferencesOptions {
  prompt: string;
  referenceImages: File[];
  size: string;
  style?: string;
}

/**
 * Generate an ad using AI with <PERSON>'s generated content and reference image
 */
export async function generateAd(options: AdGenerationOptions): Promise<AdGenerationResult> {
  try {
    const formData = new FormData();

    // Build enhanced prompt with Emma's generated content
    let enhancedPrompt = options.prompt;

    if (options.headline || options.punchline || options.cta) {
      enhancedPrompt += "\n\n🎯 TEXTO ESPECÍFICO QUE DEBE APARECER EN EL ANUNCIO (OBLIGATORIO Y LEGIBLE):";
      if (options.headline) enhancedPrompt += `\n📢 HEADLINE PRINCIPAL (grande y prominente): "${options.headline}"`;
      if (options.punchline) enhancedPrompt += `\n💡 PUNCHLINE/SUBTÍTULO (claro y visible): "${options.punchline}"`;
      if (options.cta) enhancedPrompt += `\n🔥 CALL TO ACTION (botón o texto destacado): "${options.cta}"`;
      enhancedPrompt += "\n\n⚠️ CRÍTICO: Estos textos DEBEN ser perfectamente legibles, con alto contraste, tipografía clara y tamaño prominente. El texto es la prioridad #1 del diseño.";
    }

    formData.append('prompt', enhancedPrompt);
    formData.append('size', options.size);

    // Add reference image if provided
    if (options.referenceImage) {
      formData.append('reference_image', options.referenceImage);
    }

    // Choose endpoint based on whether we have reference image
    const endpoint = options.referenceImage ? '/api/ads/edit-with-references' : '/api/ads/generate';

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || 'fallback-key'
      },
      body: formData
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    const result = await response.json();

    return {
      success: true,
      image_url: result.image_url,
      revised_prompt: result.revised_prompt,
      response_id: result.response_id,
      metadata: {
        ...result.metadata,
        size: options.size,
        style: options.style || "default",
        model: options.model || "ideogram-3.0-quality",
        generated_at: new Date().toISOString(),
        headline: options.headline,
        punchline: options.punchline,
        cta: options.cta,
        used_reference_image: !!options.referenceImage
      }
    };
  } catch (error) {
    console.error("Error generating ad:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido al generar anuncio"
    };
  }
}

/**
 * Edit an ad using reference images
 */
export async function editWithReferences(options: EditWithReferencesOptions): Promise<AdGenerationResult> {
  try {
    const formData = new FormData();
    formData.append('prompt', options.prompt);
    formData.append('size', options.size);

    // Add reference images to form data
    options.referenceImages.forEach((image, index) => {
      formData.append(`reference_images`, image);
    });

    const response = await fetch('/api/ads/edit-with-references', {
      method: 'POST',
      headers: {
        'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || 'fallback-key'
      },
      body: formData
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    const result = await response.json();

    return {
      success: true,
      image_url: result.image_url,
      revised_prompt: result.revised_prompt,
      response_id: result.response_id,
      metadata: {
        ...result.metadata,
        size: options.size,
        style: options.style || "product",
        reference_count: options.referenceImages.length,
        generated_at: new Date().toISOString(),
        used_references: true
      }
    };
  } catch (error) {
    console.error("Error editing ad with references:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido al editar anuncio"
    };
  }
}

/**
 * Enhance a prompt using AI
 */
export async function enhancePrompt(prompt: string, platform: string): Promise<string> {
  try {
    const response = await fetch('/api/v1/content/enhance-prompt', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || 'fallback-key'
      },
      body: JSON.stringify({
        prompt,
        platform,
        type: 'advertisement'
      })
    });

    if (!response.ok) {
      console.warn('Failed to enhance prompt, using fallback');
      // Fallback to local enhancement
      const enhancements = {
        facebook: "with engaging visuals, clear call-to-action, and Facebook-optimized design",
        instagram: "with vibrant colors, Instagram-style aesthetics, and hashtag-friendly content",
        google: "with professional layout, conversion-focused design, and clear messaging",
        linkedin: "with corporate design, business-focused messaging, and professional aesthetics",
        youtube: "with eye-catching thumbnail design, bold text, and attention-grabbing visuals",
        display: "with web-optimized design, fast-loading graphics, and clear branding"
      };
      const enhancement = enhancements[platform as keyof typeof enhancements] || "with professional design";
      return `${prompt} ${enhancement}`;
    }

    const result = await response.json();
    return result.enhanced_prompt || prompt;
  } catch (error) {
    console.error("Error enhancing prompt:", error);
    return prompt;
  }
}

/**
 * Get platform-specific templates
 */
export async function getPlatformTemplates(platform: string): Promise<string[]> {
  try {
    const response = await fetch(`/api/v1/content/templates?platform=${platform}&type=advertisement`, {
      method: 'GET',
      headers: {
        'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || 'fallback-key'
      }
    });

    if (!response.ok) {
      console.warn('Failed to fetch templates, using fallback');
      // Fallback templates
      const templates = {
        facebook: [
          "Promoción especial con descuento del 50% - diseño moderno y llamativo",
          "Lanzamiento de producto nuevo - estilo minimalista y elegante",
          "Evento especial - diseño festivo con colores vibrantes",
          "Anuncio de servicio profesional - layout corporativo con testimonios",
          "Campaña de awareness - diseño emocional con storytelling visual"
        ],
        instagram: [
          "Post de producto lifestyle - estética Instagram con colores pastel",
          "Historia de marca - diseño storytelling con elementos visuales",
          "Promoción flash - diseño dinámico con gradientes modernos",
          "Contenido UGC - diseño auténtico con elementos reales",
          "Anuncio de influencer - estilo personal y cercano"
        ],
        google: [
          "Banner promocional - diseño profesional con call-to-action claro",
          "Anuncio de servicio - layout limpio con información destacada",
          "Campaña estacional - diseño temático con elementos relevantes",
          "Display ad corporativo - branding fuerte y mensaje directo",
          "Retargeting ad - diseño persuasivo con oferta especial"
        ],
        linkedin: [
          "Anuncio B2B profesional - diseño corporativo y serio",
          "Promoción de servicio empresarial - layout ejecutivo",
          "Evento de networking - diseño profesional con detalles del evento"
        ],
        youtube: [
          "Thumbnail llamativo - diseño bold con texto grande",
          "Anuncio pre-roll - diseño dinámico que capture atención",
          "Banner de canal - diseño consistente con branding"
        ]
      };
      return templates[platform as keyof typeof templates] || [
        "Anuncio profesional - diseño limpio y moderno",
        "Promoción especial - diseño llamativo y efectivo"
      ];
    }

    const result = await response.json();
    return result.templates || [];
  } catch (error) {
    console.error("Error fetching templates:", error);
    return [];
  }
}

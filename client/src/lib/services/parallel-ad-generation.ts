/**
 * Parallel Ad Generation Service
 * Optimized for performance with progress tracking and partial recovery
 */

import { generateAd, type AdGenerationOptions } from './ad-generation';
import { GeneratedAd } from '@/types/ad-creator-types';

export interface ParallelGenerationOptions {
  baseOptions: AdGenerationOptions;
  variationCount: number;
  onProgress?: (completed: number, total: number, variation?: GeneratedAd) => void;
  onError?: (error: Error, variationIndex: number) => void;
  maxConcurrent?: number;
}

export interface ParallelGenerationResult {
  success: boolean;
  variations: GeneratedAd[];
  errors: Array<{ index: number; error: string }>;
  totalGenerated: number;
  totalRequested: number;
}

/**
 * Generate multiple ad variations using Ideogram's native batch generation
 * Much more efficient than parallel individual calls
 */
export async function generateAdVariationsParallel(
  options: ParallelGenerationOptions
): Promise<ParallelGenerationResult> {
  const {
    baseOptions,
    variationCount,
    onProgress,
    onError
  } = options;

  const variations: GeneratedAd[] = [];
  const errors: Array<{ index: number; error: string }> = [];

  try {
    // Build enhanced prompt with Emma's generated content
    let enhancedPrompt = baseOptions.prompt;

    if (baseOptions.headline || baseOptions.punchline || baseOptions.cta) {
      enhancedPrompt += "\n\n🎯 TEXTO ESPECÍFICO QUE DEBE APARECER EN EL ANUNCIO (OBLIGATORIO Y LEGIBLE):";
      if (baseOptions.headline) enhancedPrompt += `\n📢 HEADLINE PRINCIPAL (grande y prominente): "${baseOptions.headline}"`;
      if (baseOptions.punchline) enhancedPrompt += `\n💡 PUNCHLINE/SUBTÍTULO (claro y visible): "${baseOptions.punchline}"`;
      if (baseOptions.cta) enhancedPrompt += `\n🔥 CALL TO ACTION (botón o texto destacado): "${baseOptions.cta}"`;
      enhancedPrompt += "\n\n⚠️ CRÍTICO: Estos textos DEBEN ser perfectamente legibles, con alto contraste, tipografía clara y tamaño prominente. El texto es la prioridad #1 del diseño.";
    }

    const formData = new FormData();
    formData.append('prompt', enhancedPrompt);
    formData.append('size', baseOptions.size);
    formData.append('num_images', variationCount.toString());

    // Add reference image if provided
    if (baseOptions.referenceImage) {
      formData.append('reference_image', baseOptions.referenceImage);
    }

    // Report initial progress
    onProgress?.(0, variationCount);

    const response = await fetch('/api/ads/generate-multiple', {
      method: 'POST',
      headers: {
        'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || 'fallback-key'
      },
      body: formData
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    const result = await response.json();

    if (result.success && result.images) {
      // Process each image from the batch response
      result.images.forEach((imageData: any, index: number) => {
        const variation: GeneratedAd = {
          id: generateUniqueId(index),
          image_url: imageData.image_url,
          prompt: baseOptions.prompt,
          revised_prompt: imageData.revised_prompt,
          response_id: imageData.response_id,
          metadata: {
            ...imageData.metadata,
            variation: index + 1,
            generated_at: new Date().toISOString(),
            headline: baseOptions.headline,
            punchline: baseOptions.punchline,
            cta: baseOptions.cta,
            used_reference_image: !!baseOptions.referenceImage
          },
          timestamp: Date.now()
        };

        variations.push(variation);

        // Report progress for each completed image
        onProgress?.(index + 1, variationCount, variation);
      });

      return {
        success: true,
        variations,
        errors,
        totalGenerated: variations.length,
        totalRequested: variationCount
      };
    } else {
      throw new Error(result.error || 'Batch generation failed');
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    errors.push({ index: 0, error: errorMessage });

    onError?.(error instanceof Error ? error : new Error(errorMessage), 0);

    return {
      success: false,
      variations,
      errors,
      totalGenerated: 0,
      totalRequested: variationCount
    };
  }
}

/**
 * Create batches for controlled concurrency
 */
function createBatches(total: number, batchSize: number): number[][] {
  const batches: number[][] = [];
  for (let i = 0; i < total; i += batchSize) {
    batches.push(Array.from({ length: Math.min(batchSize, total - i) }, (_, j) => i + j));
  }
  return batches;
}

/**
 * Add slight variation to prompts to ensure different results
 */
function addPromptVariation(basePrompt: string, index: number): string {
  const variations = [
    '', // Original
    ' with modern aesthetic',
    ' with bold design',
    ' with minimalist style',
    ' with vibrant colors',
    ' with professional look'
  ];
  
  const variation = variations[index % variations.length];
  return basePrompt + variation;
}

/**
 * Generate unique ID with better collision resistance
 */
function generateUniqueId(variationIndex: number): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `ad_${timestamp}_${variationIndex}_${random}`;
}

/**
 * Estimate generation time based on variation count
 */
export function estimateGenerationTime(variationCount: number, maxConcurrent: number = 3): number {
  const avgTimePerVariation = 8; // seconds
  const batches = Math.ceil(variationCount / maxConcurrent);
  return batches * avgTimePerVariation;
}

/**
 * Validate generation options
 */
export function validateGenerationOptions(options: ParallelGenerationOptions): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!options.baseOptions.prompt || options.baseOptions.prompt.trim().length < 10) {
    errors.push('Prompt must be at least 10 characters long');
  }

  if (options.variationCount < 1 || options.variationCount > 10) {
    errors.push('Variation count must be between 1 and 10');
  }

  if (options.maxConcurrent && (options.maxConcurrent < 1 || options.maxConcurrent > 5)) {
    errors.push('Max concurrent requests must be between 1 and 5');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Comprehensive Error Handling for Ad Creator System
 * Centralized error handling with user-friendly messages and recovery suggestions
 */

export interface AdError {
  code: string;
  message: string;
  userMessage: string;
  suggestions: string[];
  recoverable: boolean;
}

export class AdCreatorError extends Error {
  public readonly code: string;
  public readonly userMessage: string;
  public readonly suggestions: string[];
  public readonly recoverable: boolean;

  constructor(error: AdError) {
    super(error.message);
    this.name = 'AdCreatorError';
    this.code = error.code;
    this.userMessage = error.userMessage;
    this.suggestions = error.suggestions;
    this.recoverable = error.recoverable;
  }
}

// Error codes and their configurations
export const AD_ERROR_CODES = {
  GENERATION_FAILED: {
    code: 'GENERATION_FAILED',
    message: 'Failed to generate advertisement',
    userMessage: 'No pudimos generar tu anuncio en este momento',
    suggestions: [
      'Intenta con una descripción más específica',
      'Verifica tu conexión a internet',
      'Prueba con un tamaño de imagen diferente'
    ],
    recoverable: true
  },
  INVALID_PROMPT: {
    code: 'INVALID_PROMPT',
    message: 'Invalid or empty prompt provided',
    userMessage: 'La descripción del anuncio no es válida',
    suggestions: [
      'Escribe una descripción más detallada',
      'Incluye información sobre tu producto o servicio',
      'Usa al menos 10 caracteres'
    ],
    recoverable: true
  },
  IMAGE_UPLOAD_FAILED: {
    code: 'IMAGE_UPLOAD_FAILED',
    message: 'Failed to upload reference images',
    userMessage: 'Error al subir las imágenes de referencia',
    suggestions: [
      'Verifica que las imágenes sean JPG o PNG',
      'Asegúrate de que no excedan 30MB',
      'Intenta con menos imágenes'
    ],
    recoverable: true
  },
  NETWORK_ERROR: {
    code: 'NETWORK_ERROR',
    message: 'Network connection failed',
    userMessage: 'Problema de conexión a internet',
    suggestions: [
      'Verifica tu conexión a internet',
      'Intenta nuevamente en unos segundos',
      'Recarga la página si el problema persiste'
    ],
    recoverable: true
  },
  API_QUOTA_EXCEEDED: {
    code: 'API_QUOTA_EXCEEDED',
    message: 'API quota exceeded',
    userMessage: 'Has alcanzado el límite de generaciones por hoy',
    suggestions: [
      'Intenta nuevamente mañana',
      'Considera actualizar tu plan',
      'Usa las plantillas prediseñadas mientras tanto'
    ],
    recoverable: false
  },
  INVALID_SIZE: {
    code: 'INVALID_SIZE',
    message: 'Invalid image size specified',
    userMessage: 'El tamaño de imagen seleccionado no es válido',
    suggestions: [
      'Selecciona un tamaño de la lista disponible',
      'Usa el tamaño recomendado para la plataforma',
      'Contacta soporte si el problema persiste'
    ],
    recoverable: true
  },
  CONTENT_POLICY_VIOLATION: {
    code: 'CONTENT_POLICY_VIOLATION',
    message: 'Content violates platform policies',
    userMessage: 'El contenido no cumple con nuestras políticas',
    suggestions: [
      'Revisa que tu descripción sea apropiada',
      'Evita contenido violento o inapropiado',
      'Usa términos más generales'
    ],
    recoverable: true
  },
  SAVE_FAILED: {
    code: 'SAVE_FAILED',
    message: 'Failed to save advertisement',
    userMessage: 'No pudimos guardar tu anuncio',
    suggestions: [
      'Verifica que tengas espacio de almacenamiento',
      'Intenta guardar nuevamente',
      'Descarga la imagen como respaldo'
    ],
    recoverable: true
  }
} as const;

/**
 * Parse error from API response or exception
 */
export function parseAdError(error: any): AdCreatorError {
  // Handle network errors
  if (!navigator.onLine) {
    return new AdCreatorError(AD_ERROR_CODES.NETWORK_ERROR);
  }

  // Handle API errors
  if (error?.response?.status) {
    switch (error.response.status) {
      case 429:
        return new AdCreatorError(AD_ERROR_CODES.API_QUOTA_EXCEEDED);
      case 400:
        if (error.response.data?.error?.includes('prompt')) {
          return new AdCreatorError(AD_ERROR_CODES.INVALID_PROMPT);
        }
        if (error.response.data?.error?.includes('size')) {
          return new AdCreatorError(AD_ERROR_CODES.INVALID_SIZE);
        }
        break;
      case 403:
        return new AdCreatorError(AD_ERROR_CODES.CONTENT_POLICY_VIOLATION);
      case 500:
        return new AdCreatorError(AD_ERROR_CODES.GENERATION_FAILED);
    }
  }

  // Handle specific error messages
  if (typeof error === 'string') {
    if (error.toLowerCase().includes('network')) {
      return new AdCreatorError(AD_ERROR_CODES.NETWORK_ERROR);
    }
    if (error.toLowerCase().includes('quota') || error.toLowerCase().includes('limit')) {
      return new AdCreatorError(AD_ERROR_CODES.API_QUOTA_EXCEEDED);
    }
    if (error.toLowerCase().includes('prompt') || error.toLowerCase().includes('description')) {
      return new AdCreatorError(AD_ERROR_CODES.INVALID_PROMPT);
    }
  }

  // Handle Error objects
  if (error instanceof Error) {
    if (error.message.includes('Failed to fetch')) {
      return new AdCreatorError(AD_ERROR_CODES.NETWORK_ERROR);
    }
  }

  // Default to generation failed
  return new AdCreatorError(AD_ERROR_CODES.GENERATION_FAILED);
}

/**
 * Handle error with toast notification
 */
export function handleAdError(error: any, toast: any) {
  const adError = parseAdError(error);
  
  console.error(`Ad Creator Error [${adError.code}]:`, adError.message);
  
  toast({
    title: adError.userMessage,
    description: adError.suggestions[0], // Show first suggestion
    variant: "destructive",
    duration: adError.recoverable ? 5000 : 8000,
  });

  return adError;
}

/**
 * Validate prompt before generation
 */
export function validatePrompt(prompt: string): { valid: boolean; error?: AdCreatorError } {
  if (!prompt || prompt.trim().length === 0) {
    return {
      valid: false,
      error: new AdCreatorError(AD_ERROR_CODES.INVALID_PROMPT)
    };
  }

  if (prompt.trim().length < 10) {
    return {
      valid: false,
      error: new AdCreatorError({
        ...AD_ERROR_CODES.INVALID_PROMPT,
        userMessage: 'La descripción es demasiado corta',
        suggestions: ['Escribe al menos 10 caracteres', 'Sé más específico sobre tu anuncio']
      })
    };
  }

  return { valid: true };
}

/**
 * Validate image files
 */
export function validateImageFiles(files: File[]): { valid: boolean; error?: AdCreatorError } {
  if (files.length === 0) {
    return { valid: true };
  }

  if (files.length > 3) {
    return {
      valid: false,
      error: new AdCreatorError({
        ...AD_ERROR_CODES.IMAGE_UPLOAD_FAILED,
        userMessage: 'Máximo 3 imágenes permitidas',
        suggestions: ['Selecciona máximo 3 imágenes', 'Usa las imágenes más representativas']
      })
    };
  }

  for (const file of files) {
    if (!file.type.startsWith('image/')) {
      return {
        valid: false,
        error: new AdCreatorError({
          ...AD_ERROR_CODES.IMAGE_UPLOAD_FAILED,
          userMessage: 'Formato de archivo no válido',
          suggestions: ['Solo se permiten imágenes JPG, PNG y WebP']
        })
      };
    }

    if (file.size > 30 * 1024 * 1024) { // 30MB
      return {
        valid: false,
        error: new AdCreatorError({
          ...AD_ERROR_CODES.IMAGE_UPLOAD_FAILED,
          userMessage: 'Imagen demasiado grande',
          suggestions: ['Reduce el tamaño de la imagen a menos de 30MB']
        })
      };
    }
  }

  return { valid: true };
}

import React, { useState } from "react";
import { motion } from "framer-motion";
import { useLocation } from "wouter";
import {
  Target,
  Megaphone,
  Sparkles,
  Zap,
  ArrowRight,
  Upload,
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { DashboardLayout } from "@/components/layout/dashboard-layout";

// Importar imagen del ad creator
import adCreador from "@/assets/ad-creator.png";

interface AdTool {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  linkTo: string;
  isPremium?: boolean;
  previewImage?: string;
  gradient: string;
  category: string;
}

const adTools: AdTool[] = [
  {
    id: "ad-templates",
    title: "Plantillas de Anuncios",
    description: "Biblioteca completa de plantillas profesionales de Figma. Sube, organiza y personaliza tus diseños",
    category: "Plantillas",
    icon: <Sparkles className="w-6 h-6" />,
    linkTo: "/dashboard/ads-central/templates",
    gradient: "from-purple-500 to-blue-600",
  },
  {
    id: "ad-creator",
    title: "Crear Anuncio",
    description: "Diseña anuncios profesionales con calidad de agencia. Product placement perfecto, iluminación profesional y composición comercial",
    category: "Creativos",
    icon: <Megaphone className="w-6 h-6" />,
    linkTo: "/ad-creator",
    previewImage: adCreador,
    gradient: "from-emerald-500 to-teal-600",
  },
];

const categories = [
  { id: "all", name: "Todas", icon: <Sparkles className="w-5 h-5" /> },
  { id: "Plantillas", name: "Plantillas", icon: <Sparkles className="w-5 h-5" /> },
  { id: "Creativos", name: "Creativos", icon: <Megaphone className="w-5 h-5" /> },
];

function AdsCentralContent() {
  const [, navigate] = useLocation();

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative min-h-[480px] flex flex-col gap-6 bg-cover bg-center bg-no-repeat items-center justify-center p-8 rounded-xl mx-4 mb-8"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%), url(${adCreador})`
        }}
      >
        <div className="flex flex-col gap-4 text-center max-w-4xl">
          <h1 className="text-white text-4xl md:text-5xl font-black leading-tight tracking-tight">
            Crea anuncios impresionantes en minutos
          </h1>
          <h2 className="text-white/90 text-base md:text-lg font-normal leading-relaxed max-w-2xl mx-auto">
            Emma Ads Central es la forma más fácil de crear anuncios con calidad profesional para tu negocio.
            No se requiere experiencia en diseño.
          </h2>
        </div>
        <Button
          onClick={() => navigate("/ad-creator/select")}
          size="lg"
          className="bg-white text-[#3018ef] hover:bg-white/90 font-bold px-8 py-3 text-base"
        >
          Crear Anuncio
        </Button>
      </motion.div>

      {/* Choose Ad Type Section */}
      <div className="px-4 md:px-8 lg:px-16 max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <h2 className="text-[#0e141b] text-2xl font-bold leading-tight tracking-tight px-4 pb-6 pt-8">
            Elige tu tipo de anuncio
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 px-4 mb-12">
            <motion.div
              whileHover={{ scale: 1.02 }}
              className="flex gap-4 rounded-lg border border-slate-200 bg-white p-6 items-center cursor-pointer hover:shadow-md transition-all"
              onClick={() => navigate("/ad-creator/select")}
            >
              <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg">
                <Megaphone className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-[#0e141b] text-lg font-bold leading-tight">Anuncio Visual</h3>
                <p className="text-slate-600 text-sm">Crea anuncios con imágenes profesionales</p>
              </div>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02 }}
              className="flex gap-4 rounded-lg border border-slate-200 bg-white p-6 items-center cursor-pointer hover:shadow-md transition-all"
              onClick={() => navigate("/dashboard/ads-central/templates")}
            >
              <div className="p-3 bg-gradient-to-r from-purple-500 to-blue-600 rounded-lg">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-[#0e141b] text-lg font-bold leading-tight">Plantillas</h3>
                <p className="text-slate-600 text-sm">Usa plantillas prediseñadas de Figma</p>
              </div>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02 }}
              className="flex gap-4 rounded-lg border border-slate-200 bg-white p-6 items-center cursor-pointer hover:shadow-md transition-all"
              onClick={() => document.getElementById('template-upload')?.click()}
            >
              <div className="p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg">
                <Upload className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-[#0e141b] text-lg font-bold leading-tight">Subir Plantilla</h3>
                <p className="text-slate-600 text-sm">Sube plantillas de Figma, Canva, etc.</p>
                <input
                  type="file"
                  accept=".fig,.sketch,.ai,.psd,.svg,.png,.jpg,.jpeg,.pdf,.eps"
                  className="hidden"
                  id="template-upload"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      console.log("Archivo seleccionado:", file.name);
                      // Aquí iría la lógica para subir el archivo
                    }
                  }}
                />
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Browse Templates Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <h2 className="text-[#0e141b] text-2xl font-bold leading-tight tracking-tight px-4 pb-6">
            Explora plantillas
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 px-4">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05 }}
                className="flex flex-col gap-3 cursor-pointer"
                onClick={() => navigate("/dashboard/ads-central/templates")}
              >
                <div className="w-full bg-gradient-to-br from-slate-200 to-slate-300 aspect-video bg-cover rounded-xl flex items-center justify-center hover:shadow-lg transition-all">
                  <div className="text-slate-500 text-center">
                    <Sparkles className="w-8 h-8 mx-auto mb-2" />
                    <p className="text-xs">Plantilla {index}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="text-center py-16"
        >
          <h3 className="text-2xl font-bold text-[#0e141b] mb-4">
            ¿Listo para crear tu primer anuncio?
          </h3>
          <p className="text-slate-600 mb-8 max-w-2xl mx-auto">
            Únete a miles de empresas que ya están creando anuncios profesionales con Emma
          </p>
          <Button
            onClick={() => navigate("/ad-creator/select")}
            size="lg"
            className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:opacity-90 font-bold px-8 py-3"
          >
            Crear Mi Primer Anuncio
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
        </motion.div>
      </div>
    </div>
  );
}

function AdsCentralPage() {
  return (
    <DashboardLayout pageTitle="Ads Central">
      <AdsCentralContent />
    </DashboardLayout>
  );
}

export default AdsCentralPage;

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Plus,
  Edit3,
  Trash2,
  Calendar,
  Eye,
  MoreVertical,
  Search,
  Filter,
  Upload,
  Folder,
  FolderPlus,
  X,
  Download,
  ZoomIn,
  ZoomOut
} from "lucide-react"
import { useLocation } from "wouter"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { DashboardLayout } from "@/components/layout/dashboard-layout"

interface Template {
  id: string
  title: string
  description: string
  thumbnail: string
  createdAt: string
  updatedAt: string
  tags: string[]
  category: string
  folderId?: string
}

interface Folder {
  id: string
  name: string
  count: number
  color?: string
}

// Mock data para carpetas
const mockFolders: Folder[] = [
  { id: "all", name: "Todas las Plantillas", count: 8 },
  { id: "social", name: "Redes Sociales", count: 3 },
  { id: "web", name: "Banners Web", count: 2 },
  { id: "email", name: "Email Marketing", count: 2 },
  { id: "print", name: "Material Impreso", count: 1 },
  { id: "uncategorized", name: "Sin Categorizar", count: 0 }
]

// Mock data - después conectaremos con backend
const mockTemplates: Template[] = [
  {
    id: "1",
    title: "Instagram Stories",
    description: "Plantilla moderna para stories de Instagram",
    thumbnail: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=300&fit=crop",
    createdAt: "2024-01-15",
    updatedAt: "2024-01-20",
    tags: ["instagram", "stories"],
    category: "Social Media",
    folderId: "social"
  },
  {
    id: "2",
    title: "Facebook Ad",
    description: "Diseño profesional para anuncios de Facebook",
    thumbnail: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop",
    createdAt: "2024-01-10",
    updatedAt: "2024-01-18",
    tags: ["facebook", "ads"],
    category: "Social Media",
    folderId: "social"
  },
  {
    id: "3",
    title: "Google Display",
    description: "Plantilla para red de display de Google",
    thumbnail: "https://images.unsplash.com/photo-1553484771-371a605b060b?w=400&h=300&fit=crop",
    createdAt: "2024-01-05",
    updatedAt: "2024-01-15",
    tags: ["google", "display"],
    category: "Web Banner",
    folderId: "web"
  },
  {
    id: "4",
    title: "LinkedIn Post",
    description: "Plantilla para posts de LinkedIn",
    thumbnail: "https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400&h=300&fit=crop",
    createdAt: "2024-01-12",
    updatedAt: "2024-01-22",
    tags: ["linkedin", "professional"],
    category: "Social Media",
    folderId: "social"
  },
  {
    id: "5",
    title: "Newsletter Header",
    description: "Cabecera para newsletter",
    thumbnail: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop",
    createdAt: "2024-01-08",
    updatedAt: "2024-01-16",
    tags: ["email", "newsletter"],
    category: "Email",
    folderId: "email"
  },
  {
    id: "6",
    title: "Web Banner",
    description: "Banner promocional para sitio web",
    thumbnail: "https://images.unsplash.com/photo-1558655146-d09347e92766?w=400&h=300&fit=crop",
    createdAt: "2024-01-06",
    updatedAt: "2024-01-14",
    tags: ["web", "banner"],
    category: "Web Banner",
    folderId: "web"
  },
  {
    id: "7",
    title: "Email Promo",
    description: "Plantilla promocional para email",
    thumbnail: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=300&fit=crop",
    createdAt: "2024-01-04",
    updatedAt: "2024-01-12",
    tags: ["email", "promo"],
    category: "Email",
    folderId: "email"
  },
  {
    id: "8",
    title: "Flyer Evento",
    description: "Flyer para eventos",
    thumbnail: "https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=400&h=300&fit=crop",
    createdAt: "2024-01-02",
    updatedAt: "2024-01-10",
    tags: ["print", "flyer"],
    category: "Print",
    folderId: "print"
  }
]

function AdsTemplatesContent() {
  const [, setLocation] = useLocation()
  const [templates, setTemplates] = useState<Template[]>(mockTemplates)
  const [folders, setFolders] = useState<Folder[]>(mockFolders)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedFolder, setSelectedFolder] = useState("all")
  const [viewingTemplate, setViewingTemplate] = useState<Template | null>(null)
  const [imageZoom, setImageZoom] = useState(1)
  const [loadingTemplate, setLoadingTemplate] = useState<string | null>(null)

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase())

    if (selectedFolder === "all") return matchesSearch
    return matchesSearch && template.folderId === selectedFolder
  })

  const handleUploadTemplate = () => {
    console.log("📤 Botón subir clickeado")
    const fileInput = document.getElementById('template-upload')
    console.log("📁 Input encontrado:", !!fileInput)
    fileInput?.click()
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log("📁 handleFileUpload ejecutado")
    console.log("📁 Event target:", e.target)
    console.log("📁 Files:", e.target.files)

    const file = e.target.files?.[0]
    if (file) {
      console.log("📄 Archivo seleccionado:")
      console.log("  - Nombre:", file.name)
      console.log("  - Tipo:", file.type)
      console.log("  - Tamaño:", file.size, "bytes")

      // Verificar que es una imagen
      if (!file.type.startsWith('image/')) {
        console.warn("⚠️ El archivo no es una imagen:", file.type)
      }

      // Convertir archivo a base64 para mejor compatibilidad con Polotno
      const reader = new FileReader()
      reader.onload = (event) => {
        console.log("📖 FileReader onload ejecutado")
        const base64String = event.target?.result as string
        console.log("📄 Base64 generado, longitud:", base64String?.length || 0)
        console.log("📄 Base64 preview:", base64String?.substring(0, 100) + "...")

        const newTemplate: Template = {
          id: Date.now().toString(),
          title: file.name.replace(/\.[^/.]+$/, ""),
          description: "Plantilla subida por el usuario",
          thumbnail: base64String, // Usar base64 en lugar de blob URL
          createdAt: new Date().toISOString().split('T')[0],
          updatedAt: new Date().toISOString().split('T')[0],
          tags: ["custom", "uploaded"],
          category: "Personalizado",
          folderId: selectedFolder === "all" ? "uncategorized" : selectedFolder
        }

        console.log("💾 Guardando template:", newTemplate.title)
        setTemplates(prev => {
          console.log("📊 Templates antes:", prev.length)
          const newList = [newTemplate, ...prev]
          console.log("📊 Templates después:", newList.length)
          return newList
        })

        // Actualizar contador de carpetas
        setFolders(prev => prev.map(folder =>
          folder.id === newTemplate.folderId || folder.id === "all"
            ? { ...folder, count: folder.count + 1 }
            : folder
        ))

        console.log("✅ Plantilla guardada exitosamente")
      }

      reader.onerror = (error) => {
        console.error("❌ Error leyendo el archivo:", error)
      }

      reader.onprogress = (event) => {
        if (event.lengthComputable) {
          const progress = (event.loaded / event.total) * 100
          console.log("📈 Progreso de lectura:", Math.round(progress) + "%")
        }
      }

      // Leer archivo como base64
      console.log("📖 Iniciando lectura del archivo...")
      reader.readAsDataURL(file)
    } else {
      console.log("❌ No se seleccionó ningún archivo")
    }
  }

  const handleCreateFolder = () => {
    const folderName = prompt("Nombre de la nueva carpeta:")
    if (folderName && folderName.trim()) {
      const newFolder: Folder = {
        id: Date.now().toString(),
        name: folderName.trim(),
        count: 0
      }
      setFolders(prev => [...prev.slice(0, -1), newFolder, prev[prev.length - 1]])
    }
  }

  const handleOpenTemplate = (templateId: string) => {
    const template = templates.find(t => t.id === templateId)
    if (template) {
      console.log("🚀 Abriendo plantilla:", template.title)
      console.log("🖼️ URL de imagen:", template.thumbnail)

      // Mostrar estado de carga
      setLoadingTemplate(templateId)

      // Verificar que la imagen se puede cargar antes de abrir el editor
      const img = new Image()
      img.onload = () => {
        console.log("✅ Imagen cargada correctamente!")
        console.log("📏 Dimensiones:", img.width, "x", img.height)

        // Delay mínimo para mostrar el loading
        setTimeout(() => {
          const url = `/visual-editor?template=${templateId}&imageUrl=${encodeURIComponent(template.thumbnail)}&title=${encodeURIComponent(template.title)}&platform=custom`
          console.log("🔗 URL completa que se enviará:", url)
          console.log("🔗 imageUrl codificada:", encodeURIComponent(template.thumbnail))
          setLoadingTemplate(null)
          setLocation(url)
        }, 500) // 500ms para ver el loading
      }
      img.onerror = () => {
        console.error("❌ Error cargando la imagen:", template.thumbnail)
        setTimeout(() => {
          const url = `/visual-editor?template=${templateId}&title=${encodeURIComponent(template.title)}&platform=custom`
          setLoadingTemplate(null)
          setLocation(url)
        }, 500)
      }

      // Agregar crossOrigin para imágenes externas
      img.crossOrigin = "anonymous"
      img.src = template.thumbnail
      console.log("🔄 Iniciando carga de imagen:", template.thumbnail)

      // Mostrar loading mientras carga
      console.log("⏳ Verificando que la imagen se pueda cargar...")
    } else {
      console.error("❌ No se encontró la plantilla con ID:", templateId)
    }
  }

  const handleViewTemplate = (templateId: string) => {
    const template = templates.find(t => t.id === templateId)
    if (template) {
      setViewingTemplate(template)
      setImageZoom(1) // Reset zoom
    }
  }

  const handleCloseViewer = () => {
    setViewingTemplate(null)
    setImageZoom(1)
  }

  const handleDownloadTemplate = (template: Template) => {
    // Crear un enlace temporal para descargar
    const link = document.createElement('a')
    link.href = template.thumbnail
    link.download = `${template.title}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleDeleteTemplate = (templateId: string) => {
    const templateToDelete = templates.find(t => t.id === templateId)
    setTemplates(prev => prev.filter(template => template.id !== templateId))

    // Actualizar contador de carpetas
    if (templateToDelete) {
      setFolders(prev => prev.map(folder =>
        folder.id === templateToDelete.folderId || folder.id === "all"
          ? { ...folder, count: Math.max(0, folder.count - 1) }
          : folder
      ))
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  return (
    <div className="flex h-full">
      {/* Sidebar con carpetas */}
      <div className="w-80 bg-gray-50 border-r border-gray-200 p-6">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-4"
        >
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Mis Plantillas</h2>
            <div className="flex gap-2">
              {/* Botones de prueba - solo en desarrollo */}
              {process.env.NODE_ENV === 'development' && (
                <div className="flex gap-1">
                  <Button
                    onClick={() => {
                      const testUrl = "/visual-editor?imageUrl=" + encodeURIComponent("https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=300&fit=crop") + "&platform=custom&title=TestHTTP"
                      console.log("🧪 URL de prueba HTTP:", testUrl)
                      setLocation(testUrl)
                    }}
                    size="sm"
                    variant="outline"
                    className="text-xs"
                  >
                    🌐 HTTP
                  </Button>
                  <Button
                    onClick={() => {
                      // Imagen base64 pequeña de prueba (1x1 pixel rojo)
                      const base64Test = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
                      const testUrl = "/visual-editor?imageUrl=" + encodeURIComponent(base64Test) + "&platform=custom&title=TestBase64"
                      console.log("🧪 URL de prueba Base64:", testUrl)
                      setLocation(testUrl)
                    }}
                    size="sm"
                    variant="outline"
                    className="text-xs"
                  >
                    📄 B64
                  </Button>
                </div>
              )}
              <Button
                onClick={handleUploadTemplate}
                size="sm"
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
              >
                <Upload className="w-4 h-4 mr-1" />
                Subir
              </Button>
            </div>
          </div>

          {/* Lista de carpetas */}
          <div className="space-y-1">
            {folders.map((folder) => (
              <button
                key={folder.id}
                onClick={() => setSelectedFolder(folder.id)}
                className={`w-full flex items-center justify-between p-3 rounded-lg text-left transition-colors ${
                  selectedFolder === folder.id
                    ? "bg-purple-100 text-purple-900 border border-purple-200"
                    : "hover:bg-gray-100 text-gray-700"
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${
                    selectedFolder === folder.id ? "bg-purple-200" : "bg-gray-200"
                  }`}>
                    <Folder className="w-4 h-4" />
                  </div>
                  <span className="font-medium text-sm truncate">{folder.name}</span>
                </div>
                <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                  {folder.count}
                </span>
              </button>
            ))}
          </div>

          {/* Botón crear carpeta */}
          <Button
            onClick={handleCreateFolder}
            variant="outline"
            className="w-full justify-start gap-2 text-gray-600 hover:text-gray-900"
          >
            <FolderPlus className="w-4 h-4" />
            Nueva Carpeta
          </Button>
        </motion.div>
      </div>

      {/* Contenido principal */}
      <div className="flex-1 p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {folders.find(f => f.id === selectedFolder)?.name || "Plantillas"}
              </h1>
              <p className="text-gray-600 mt-1">
                {filteredTemplates.length} plantilla{filteredTemplates.length !== 1 ? 's' : ''}
              </p>
            </div>
          </div>

          {/* Buscador */}
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Buscar plantillas..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </motion.div>

        {/* Templates Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {filteredTemplates.map((template, index) => (
            <motion.div
              key={template.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
            >
              <Card className="group hover:shadow-lg transition-all duration-300 cursor-pointer overflow-hidden">
                <div className="relative">
                  <img
                    src={template.thumbnail}
                    alt={template.title}
                    className="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                    <Button
                      size="sm"
                      onClick={() => handleViewTemplate(template.id)}
                      className="bg-white text-gray-800 hover:bg-white/90 text-xs px-2 py-1"
                    >
                      <Eye className="w-3 h-3 mr-1" />
                      Ver
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleOpenTemplate(template.id)}
                      disabled={loadingTemplate === template.id}
                      className="bg-purple-600 text-white hover:bg-purple-700 text-xs px-2 py-1 disabled:opacity-50"
                    >
                      {loadingTemplate === template.id ? (
                        <>
                          <div className="w-3 h-3 mr-1 border border-white border-t-transparent rounded-full animate-spin" />
                          Cargando
                        </>
                      ) : (
                        <>
                          <Edit3 className="w-3 h-3 mr-1" />
                          Editar
                        </>
                      )}
                    </Button>
                  </div>

                  {/* Menu */}
                  <div className="absolute top-2 right-2">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity bg-white/80 hover:bg-white p-1 h-6 w-6">
                          <MoreVertical className="w-3 h-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => handleViewTemplate(template.id)}>
                          <Eye className="w-4 h-4 mr-2" />
                          Ver
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleOpenTemplate(template.id)}
                          disabled={loadingTemplate === template.id}
                        >
                          {loadingTemplate === template.id ? (
                            <>
                              <div className="w-4 h-4 mr-2 border border-gray-400 border-t-transparent rounded-full animate-spin" />
                              Cargando...
                            </>
                          ) : (
                            <>
                              <Edit3 className="w-4 h-4 mr-2" />
                              Editar
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDownloadTemplate(template)}>
                          <Download className="w-4 h-4 mr-2" />
                          Descargar
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600" onClick={() => handleDeleteTemplate(template.id)}>
                          <Trash2 className="w-4 h-4 mr-2" />
                          Eliminar
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                <CardContent className="p-2">
                  <h3 className="font-medium text-xs line-clamp-1 text-center text-gray-700">{template.title}</h3>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Empty State */}
        {filteredTemplates.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <Folder className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              {searchTerm ? "No se encontraron plantillas" : "Carpeta vacía"}
            </h3>
            <p className="text-gray-600 mb-4">
              {searchTerm
                ? "Intenta con otros términos de búsqueda"
                : "Sube plantillas para organizar tu biblioteca"}
            </p>
            <Button onClick={handleUploadTemplate} className="bg-gradient-to-r from-purple-600 to-blue-600">
              <Upload className="w-4 h-4 mr-2" />
              Subir Plantilla
            </Button>
          </motion.div>
        )}

        {/* Hidden File Input */}
        <input
          type="file"
          accept=".fig,.sketch,.ai,.psd,.svg,.png,.jpg,.jpeg,.pdf,.eps"
          className="hidden"
          id="template-upload"
          onChange={handleFileUpload}
        />
      </div>

      {/* Modal Visor de Plantillas */}
      <Dialog open={!!viewingTemplate} onOpenChange={handleCloseViewer}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <DialogHeader className="p-6 pb-0">
            <div className="flex items-center justify-between">
              <div>
                <DialogTitle className="text-xl font-semibold">
                  {viewingTemplate?.title}
                </DialogTitle>
                <p className="text-sm text-gray-600 mt-1">
                  {viewingTemplate?.category} • {viewingTemplate && formatDate(viewingTemplate.updatedAt)}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setImageZoom(Math.max(0.5, imageZoom - 0.25))}
                  disabled={imageZoom <= 0.5}
                >
                  <ZoomOut className="w-4 h-4" />
                </Button>
                <span className="text-sm text-gray-600 min-w-[60px] text-center">
                  {Math.round(imageZoom * 100)}%
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setImageZoom(Math.min(3, imageZoom + 0.25))}
                  disabled={imageZoom >= 3}
                >
                  <ZoomIn className="w-4 h-4" />
                </Button>
                {viewingTemplate && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownloadTemplate(viewingTemplate)}
                  >
                    <Download className="w-4 h-4 mr-1" />
                    Descargar
                  </Button>
                )}
                {viewingTemplate && (
                  <Button
                    size="sm"
                    onClick={() => {
                      handleOpenTemplate(viewingTemplate.id)
                      handleCloseViewer()
                    }}
                    className="bg-purple-600 hover:bg-purple-700"
                  >
                    <Edit3 className="w-4 h-4 mr-1" />
                    Editar
                  </Button>
                )}
              </div>
            </div>
          </DialogHeader>

          <div className="flex-1 overflow-auto p-6 pt-4">
            <div className="flex justify-center">
              {viewingTemplate && (
                <img
                  src={viewingTemplate.thumbnail}
                  alt={viewingTemplate.title}
                  className="max-w-full h-auto rounded-lg shadow-lg transition-transform duration-200"
                  style={{ transform: `scale(${imageZoom})` }}
                />
              )}
            </div>
          </div>

          {/* Tags */}
          {viewingTemplate && viewingTemplate.tags.length > 0 && (
            <div className="px-6 pb-6">
              <div className="flex flex-wrap gap-2">
                {viewingTemplate.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

function AdsTemplatesPage() {
  return (
    <DashboardLayout pageTitle="Plantillas de Anuncios">
      <AdsTemplatesContent />
    </DashboardLayout>
  );
}

export default AdsTemplatesPage;

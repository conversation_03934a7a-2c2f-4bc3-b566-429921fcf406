import React, { useState } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { useLocation } from 'wouter';

// Empleados digitales reales - Como contratar personas
const realAgents = [
  {
    id: 'hunter-pro',
    name: '<PERSON>',
    jobTitle: 'Especialista en Prospección Digital',
    image: '/agents/hunter-pro-actual.png',
    category: 'Prospección',
    description: 'Encuentra correos empresariales, redacta mensajes personalizados y ejecuta campañas de outreach que generan respuestas.',
    badge: 'MÁS CONTRATADA',
    badgeColor: 'bg-[#3018ef]',
    route: '/dashboard/agents/hunter-pro',
    experience: '5+ años en prospección B2B',
    specialty: 'Cold Email & LinkedIn Outreach'
  },
  {
    id: 'calendar-bot',
    name: '<PERSON>',
    jobTitle: 'Coordinador de Citas y Reuniones',
    image: '/A47BB6E9-741A-439D-96A9-12BF94CB4CDC.png',
    category: 'Automatización',
    description: 'Gestiona tu calendario, califica leads entrantes y agenda reuniones comerciales automáticamente.',
    badge: 'AUTOMATIZACIÓN',
    badgeColor: 'bg-[#dd3a5a]',
    route: '/dashboard/agents/calendar-bot',
    experience: '4+ años en gestión comercial',
    specialty: 'Calificación de Leads & Scheduling'
  },
  {
    id: 'community-manager-ai',
    name: 'Andrés Herrera',
    jobTitle: 'Community Manager Digital',
    image: '/agents/sales-support-actual.png',
    category: 'Redes Sociales',
    description: 'Gestiona tus redes sociales, responde comentarios, crea engagement y brinda soporte a tu comunidad 24/7.',
    badge: 'SOCIAL MEDIA',
    badgeColor: 'bg-[#3018ef]',
    route: '/dashboard/agents/community-manager-ai',
    experience: '6+ años en marketing digital',
    specialty: 'Gestión de Comunidades & Engagement'
  },
  {
    id: 'lead-agent',
    name: 'Miguel Torres',
    jobTitle: 'Ejecutivo de Ventas WhatsApp',
    image: '/agents/lead-agent-actual.png',
    category: 'WhatsApp',
    description: 'Atiende leads por WhatsApp, conversa con prospectos, pre-califica oportunidades y cierra ventas.',
    badge: 'WHATSAPP',
    badgeColor: 'bg-orange-600',
    route: '/dashboard/agents/lead-agent',
    experience: '7+ años en ventas directas',
    specialty: 'Conversión WhatsApp & Cierre de Ventas'
  },
  {
    id: 'sales-support',
    name: 'Laura Vásquez',
    jobTitle: 'Representante de Ventas Telefónicas',
    image: '/agents/sales-support-old-image.png',
    category: 'Ventas',
    description: 'Maneja llamadas entrantes, realiza seguimiento telefónico y ejecuta campañas de cold calling.',
    badge: 'LLAMADAS',
    badgeColor: 'bg-indigo-600',
    route: '/dashboard/agents/sales-support',
    experience: '5+ años en ventas telefónicas',
    specialty: 'Cold Calling & Seguimiento Comercial'
  },
  {
    id: 'email-customer-service',
    name: 'Roberto Silva',
    jobTitle: 'Especialista en Atención al Cliente',
    image: '/agents/email-customer-service.png',
    category: 'Email',
    description: 'Responde emails de clientes, resuelve consultas sobre productos y organiza la bandeja de entrada por prioridades.',
    badge: 'ATENCIÓN AL CLIENTE',
    badgeColor: 'bg-blue-600',
    route: '/dashboard/agents/email-customer-service',
    experience: '4+ años en customer service',
    specialty: 'Soporte por Email & Gestión de Consultas'
  }
];

const categories = [
  'Prospección',
  'Automatización',
  'Redes Sociales',
  'WhatsApp',
  'Ventas',
  'Email'
];

export default function AgentsCatalogPage() {
  const [, setLocation] = useLocation();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const handleAgentClick = (agentPath: string) => {
    setLocation(agentPath);
  };

  const handleNavigation = (section: string) => {
    if (section === 'home') {
      setLocation('/dashboard');
    } else if (section === 'marketplace') {
      setLocation('/dashboard/agents-marketplace');
    } else if (section === 'what-are-agents') {
      setLocation('/dashboard/que-son-agentes');
    }
  };

  // Filtrar agentes reales
  const filteredAgents = realAgents.filter(agent => {
    const matchesCategory = !selectedCategory || agent.category === selectedCategory;
    const matchesSearch = !searchQuery ||
      agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      agent.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      agent.category.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesCategory && matchesSearch;
  });

  return (
    <DashboardLayout pageTitle="Catálogo Completo de Agentes IA">
      <div className="relative flex size-full min-h-screen flex-col bg-slate-50 group/design-root overflow-x-hidden" style={{fontFamily: '"Space Grotesk", "Noto Sans", sans-serif'}}>
        <div className="layout-container flex h-full grow flex-col">
          {/* Header */}
          <header className="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#e7edf3] px-10 py-3">
            <div className="flex items-center gap-8">
              <div className="flex items-center gap-4 text-[#0e141b]">
                <div className="size-4">
                  <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M44 4H30.6666V17.3334H17.3334V30.6666H4V44H44V4Z" fill="currentColor"></path></svg>
                </div>
                <h2 className="text-[#0e141b] text-lg font-bold leading-tight tracking-[-0.015em] bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">Emma AI</h2>
              </div>
              <div className="flex items-center gap-9">
                <button className="text-[#0e141b] text-sm font-medium leading-normal cursor-pointer hover:text-[#3018ef] transition-colors" onClick={() => handleNavigation('home')}>Inicio</button>
                <button className="text-[#0e141b] text-sm font-medium leading-normal cursor-pointer hover:text-[#3018ef] transition-colors" onClick={() => handleNavigation('marketplace')}>Marketplace</button>
                <button className="text-[#0e141b] text-sm font-medium leading-normal cursor-pointer hover:text-[#3018ef] transition-colors" onClick={() => handleNavigation('what-are-agents')}>¿Qué son los Agentes?</button>
              </div>
            </div>
            <div className="flex flex-1 justify-end gap-8">
              <button
                className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white text-sm font-bold leading-normal tracking-[0.015em] hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300"
                onClick={() => setLocation('/dashboard/agents-marketplace')}
              >
                <span className="truncate">Volver al Marketplace</span>
              </button>
            </div>
          </header>

          {/* Hero Section */}
          <div className="bg-gradient-to-br from-[#3018ef]/10 via-[#3018ef]/5 to-[#dd3a5a]/10 py-12">
            <div className="max-w-7xl mx-auto px-6">
              <div className="text-center">
                <h1 className="text-4xl md:text-5xl font-black text-gray-900 mb-4 leading-tight">
                  👥 Contrata a tu Equipo de
                  <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent"> Empleados Digitales</span>
                </h1>
                <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                  Conoce a nuestros especialistas digitales. Cada uno con experiencia comprobada y listo para trabajar en tu empresa desde el primer día.
                </p>
                <div className="flex items-center justify-center gap-4 text-sm text-gray-500">
                  <span className="bg-white/80 backdrop-blur-md px-4 py-2 rounded-full border border-white/30">
                    👥 {realAgents.length} Empleados Disponibles
                  </span>
                  <span className="bg-white/80 backdrop-blur-md px-4 py-2 rounded-full border border-white/30">
                    💼 {categories.length} Especialidades
                  </span>
                  <span className="bg-white/80 backdrop-blur-md px-4 py-2 rounded-full border border-white/30">
                    ⭐ 4.8 Rating Promedio
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="bg-white border-b border-[#e7edf3] py-6">
            <div className="max-w-7xl mx-auto px-6">
              <div className="flex flex-col lg:flex-row gap-6">
                {/* Search Bar */}
                <div className="flex-1">
                  <label className="flex flex-col min-w-40 h-12 w-full">
                    <div className="flex w-full flex-1 items-stretch rounded-lg h-full">
                      <div className="text-[#4e7397] flex border-none bg-[#e7edf3] items-center justify-center pl-4 rounded-l-lg border-r-0">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                          <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
                        </svg>
                      </div>
                      <input
                        placeholder="Buscar empleados por nombre, especialidad o área..."
                        className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#0e141b] focus:outline-0 focus:ring-0 border-none bg-[#e7edf3] focus:border-none h-full placeholder:text-[#4e7397] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                      />
                    </div>
                  </label>
                </div>

                {/* Category Filter */}
                <div className="flex gap-2 flex-wrap">
                  <button
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                      selectedCategory === null
                        ? 'bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white shadow-lg'
                        : 'bg-white text-gray-600 border border-gray-200 hover:border-[#3018ef] hover:text-[#3018ef]'
                    }`}
                    onClick={() => setSelectedCategory(null)}
                  >
                    Todos ({realAgents.length})
                  </button>
                  {categories.map((category) => {
                    const categoryAgents = realAgents.filter(agent => agent.category === category);
                    return (
                      <button
                        key={category}
                        className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                          selectedCategory === category
                            ? 'bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white shadow-lg'
                            : 'bg-white text-gray-600 border border-gray-200 hover:border-[#3018ef] hover:text-[#3018ef]'
                        }`}
                        onClick={() => setSelectedCategory(category)}
                      >
                        {category} ({categoryAgents.length})
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* Results Summary */}
          <div className="bg-gray-50 py-4">
            <div className="max-w-7xl mx-auto px-6">
              <p className="text-gray-600 text-sm">
                Mostrando <span className="font-bold text-[#3018ef]">{filteredAgents.length}</span> empleados digitales
                {selectedCategory && (
                  <span> especializados en <span className="font-bold">{selectedCategory}</span></span>
                )}
                {searchQuery && (
                  <span> que coinciden con "<span className="font-bold">{searchQuery}</span>"</span>
                )}
              </p>
            </div>
          </div>

          {/* Agents Grid */}
          <div className="flex-1 py-8">
            <div className="max-w-7xl mx-auto px-6">
              {filteredAgents.length === 0 ? (
                <div className="text-center py-16">
                  <div className="text-6xl mb-4">👥</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">No encontramos empleados con ese perfil</h3>
                  <p className="text-gray-600 mb-6">
                    Intenta ajustar los filtros de especialidad o términos de búsqueda
                  </p>
                  <button
                    className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300"
                    onClick={() => {
                      setSelectedCategory(null);
                      setSearchQuery('');
                    }}
                  >
                    Ver Todos los Empleados
                  </button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {filteredAgents.map((agent) => (
                    <div
                      key={agent.id}
                      className="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-xl hover:-translate-y-2 transition-all duration-300 cursor-pointer group"
                      onClick={() => handleAgentClick(agent.route)}
                    >
                      {/* Agent Image */}
                      <div className="relative h-48 overflow-hidden">
                        <div
                          className="w-full h-full bg-center bg-cover bg-no-repeat group-hover:scale-110 transition-transform duration-300"
                          style={{
                            backgroundImage: `url("${agent.image}")`,
                            backgroundColor: '#f3f4f6'
                          }}
                        >
                          {/* Category Badge */}
                          <div className="absolute top-3 left-3">
                            <div className={`${agent.badgeColor} text-white px-3 py-1 rounded-full text-xs font-bold`}>
                              {agent.badge}
                            </div>
                          </div>

                          {/* Rating Badge */}
                          <div className="absolute top-3 right-3">
                            <div className="bg-white/90 backdrop-blur-md text-gray-800 px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1">
                              ⭐ 4.8
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Agent Info */}
                      <div className="p-4">
                        <div className="mb-3">
                          <h3 className="text-lg font-bold text-gray-900 group-hover:text-[#3018ef] transition-colors">
                            {agent.name}
                          </h3>
                          <p className="text-sm font-medium text-[#3018ef] mb-1">
                            {agent.jobTitle}
                          </p>
                          <p className="text-xs text-gray-500">
                            {agent.experience}
                          </p>
                        </div>

                        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                          {agent.description}
                        </p>

                        {/* Specialty Tag */}
                        <div className="mb-4">
                          <span className="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded-full">
                            {agent.specialty}
                          </span>
                        </div>

                        {/* Professional Stats */}
                        <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                          <span>⭐ 4.8/5 rating</span>
                          <span>📋 Disponible 24/7</span>
                        </div>

                        {/* CTA Button */}
                        <button className="w-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white py-2 px-4 rounded-lg font-medium text-sm hover:shadow-lg transition-all duration-300 group-hover:scale-105">
                          Contratar a {agent.name.split(' ')[0]}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <footer className="bg-white border-t border-[#e7edf3] py-8">
            <div className="max-w-7xl mx-auto px-6">
              <div className="flex flex-col md:flex-row items-center justify-between gap-4">
                <div className="flex items-center gap-4">
                  <div className="text-[#3018ef] font-bold">Emma AI</div>
                  <div className="text-gray-400">|</div>
                  <div className="text-gray-600 text-sm">Revolucionando el marketing con IA</div>
                </div>
                <div className="flex items-center gap-6 text-sm text-gray-500">
                  <button onClick={() => handleNavigation('what-are-agents')} className="hover:text-[#3018ef] transition-colors">
                    ¿Qué son los Agentes?
                  </button>
                  <button onClick={() => setLocation('/dashboard')} className="hover:text-[#3018ef] transition-colors">
                    Soporte
                  </button>
                  <button onClick={() => setLocation('/dashboard')} className="hover:text-[#3018ef] transition-colors">
                    Contacto
                  </button>
                </div>
              </div>
            </div>
          </footer>
        </div>
      </div>
    </DashboardLayout>
  );
}

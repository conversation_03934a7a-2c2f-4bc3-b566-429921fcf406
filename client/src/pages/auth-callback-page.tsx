import React, { useEffect } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";

export default function AuthCallbackPage() {
  const [, navigate] = useLocation();
  const { user, isLoading } = useAuth();

  useEffect(() => {
    // Handle OAuth callback
    const handleCallback = async () => {
      try {
        // Wait a moment for auth state to update
        setTimeout(() => {
          if (user) {
            console.log("OAuth callback successful, redirecting to dashboard");
            navigate("/dashboard");
          } else if (!isLoading) {
            console.log("OAuth callback failed, redirecting to login");
            navigate("/login");
          }
        }, 1000);
      } catch (error) {
        console.error("OAuth callback error:", error);
        navigate("/login");
      }
    };

    handleCallback();
  }, [user, isLoading, navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Completando autenticación...
        </h2>
        <p className="text-gray-600">
          Te redirigiremos al dashboard en un momento
        </p>
      </div>
    </div>
  );
}

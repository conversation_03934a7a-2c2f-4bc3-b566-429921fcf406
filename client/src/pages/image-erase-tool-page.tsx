/**
 * Herramienta de Borrado de Objetos - Copia literal de la implementación del AI Image Editor
 * Componente específico para borrar objetos de imágenes usando máscaras
 */
import React, { useState, useRef, useEffect } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import {
  Upload,
  Eraser,
  RotateCcw,
  Info,
  ArrowLeft,
  Download,
  Sparkles,
} from "lucide-react";
import { Link } from "wouter";

export default function ImageEraseToolPage() {
  const { toast } = useToast();
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [uploadedImageFile, setUploadedImageFile] = useState<File | null>(null);
  const [resultImage, setResultImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const imageInputRef = useRef<HTMLInputElement>(null);

  // Manejar la subida de imagen
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validar que sea una imagen
    if (!file.type.startsWith("image/")) {
      toast({
        title: "❌ Formato no válido",
        description: "Por favor, selecciona un archivo de imagen válido",
        variant: "destructive",
      });
      return;
    }

    // Validar tamaño (máximo 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: "❌ Archivo muy grande",
        description: "El archivo debe ser menor a 10MB",
        variant: "destructive",
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string;
      setUploadedImage(imageUrl);
      setUploadedImageFile(file);
      setResultImage(null);
    };
    reader.readAsDataURL(file);

    toast({
      title: "✅ Imagen cargada",
      description: "Ahora puedes dibujar sobre los objetos que deseas borrar",
    });
  };

  // Función para descargar resultado
  const handleDownload = () => {
    if (!resultImage) return;

    const link = document.createElement("a");
    link.href = resultImage;
    link.download = `imagen-borrada-${Date.now()}.webp`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "📥 Descarga iniciada",
      description: "La imagen se está descargando",
    });
  };

  return (
    <DashboardLayout pageTitle="Borrar Objetos">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="relative bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700 rounded-2xl p-8 mb-8 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <Eraser className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-4xl font-bold text-white">
                  Borrar Objetos
                </h1>
              </div>
              <p className="text-xl text-purple-100 mb-6 max-w-3xl">
                Elimina objetos no deseados de tus imágenes usando IA avanzada. Dibuja sobre los objetos y déjanos hacer el resto.
              </p>
              <div className="flex flex-wrap gap-2">
                <div className="bg-white/20 text-white border border-white/30 rounded-full px-3 py-1 text-sm">
                  <Sparkles className="w-3 h-3 mr-1 inline" />
                  IA avanzada
                </div>
                <div className="bg-white/20 text-white border border-white/30 rounded-full px-3 py-1 text-sm">
                  <Eraser className="w-3 h-3 mr-1 inline" />
                  Borrado preciso
                </div>
                <div className="bg-white/20 text-white border border-white/30 rounded-full px-3 py-1 text-sm">
                  3 créditos por uso
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Panel de Control */}
          <div className="lg:col-span-1">
            <Card className="sticky top-6 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-xl">
                  <Upload className="h-5 w-5 text-purple-600" />
                  Panel de Control
                </CardTitle>
                <CardDescription>
                  Sube tu imagen y configura el borrado
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Subida de imagen */}
                <div className="space-y-4">
                  <label className="text-sm font-medium">Subir Imagen</label>
                  {!uploadedImage ? (
                    <div
                      className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center cursor-pointer hover:border-purple-300 hover:bg-purple-50/50 transition-colors"
                      onClick={() => imageInputRef.current?.click()}
                    >
                      <Upload className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground mb-1">
                        Haz clic para subir una imagen
                      </p>
                      <p className="text-xs text-muted-foreground">
                        PNG, JPG, WebP hasta 10MB
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="relative">
                        <img
                          src={uploadedImage}
                          alt="Imagen cargada"
                          className="w-full max-h-48 object-contain rounded-lg border"
                        />
                      </div>
                      <Button
                        variant="outline"
                        onClick={() => imageInputRef.current?.click()}
                        className="w-full"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Cambiar imagen
                      </Button>
                      <div className="text-sm text-green-600 bg-green-50 p-3 rounded-lg">
                        <p>✅ Imagen cargada correctamente</p>
                        <p>Usa la herramienta de borrado para eliminar objetos</p>
                      </div>
                    </div>
                  )}

                  <input
                    ref={imageInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Área de Visualización */}
          <div className="lg:col-span-2">
            {resultImage ? (
              <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-purple-600" />
                    Resultado
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="relative">
                    <img
                      src={resultImage}
                      alt="Imagen procesada"
                      className="w-full rounded-lg border shadow-lg"
                    />
                  </div>
                  <div className="flex gap-3 justify-center">
                    <Button
                      onClick={handleDownload}
                      className="flex-1 min-w-[120px] bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Descargar
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                <CardContent className="flex flex-col items-center justify-center py-16 text-center">
                  <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-6">
                    <Eraser className="h-12 w-12 text-purple-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Borra objetos de tu imagen
                  </h3>
                  <p className="text-gray-600 mb-4 max-w-md">
                    {uploadedImage
                      ? "Usa la herramienta de borrado para eliminar objetos no deseados"
                      : "Sube una imagen para comenzar a borrar objetos"}
                  </p>
                  <div className="flex items-center gap-2 text-sm text-purple-600">
                    <Sparkles className="h-4 w-4" />
                    <span>Resultados precisos con IA</span>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Herramienta de borrado */}
            {uploadedImage && uploadedImageFile && (
              <Card className="mt-8 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Eraser className="h-5 w-5 text-purple-600" />
                    Herramienta de Borrado
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <EraseObjectsTool
                    imageFile={uploadedImageFile}
                    imageUrl={uploadedImage}
                    onResult={setResultImage}
                    isProcessing={isProcessing}
                    setIsProcessing={setIsProcessing}
                  />
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}

// Componente específico para borrar objetos - COPIA LITERAL del AI Image Editor
interface EraseObjectsToolProps {
  imageFile: File;
  imageUrl: string;
  onResult: (result: string) => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
}

function EraseObjectsTool({ imageFile, imageUrl, onResult, isProcessing, setIsProcessing }: EraseObjectsToolProps) {
  const { toast } = useToast();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [outputFormat, setOutputFormat] = useState<"png" | "webp" | "jpeg">("webp");
  const [growMask, setGrowMask] = useState(5);
  const [brushSize, setBrushSize] = useState(20);
  const [isDrawing, setIsDrawing] = useState(false);
  const [hasMask, setHasMask] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  // Cargar la imagen en el canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !imageUrl) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const img = new Image();
    img.crossOrigin = "anonymous";
    img.src = imageUrl;

    img.onload = () => {
      // Ajustar el tamaño del canvas
      canvas.width = img.width;
      canvas.height = img.height;

      // Dibujar la imagen original
      ctx.drawImage(img, 0, 0);

      // Configurar el contexto para dibujar la máscara
      ctx.lineJoin = "round";
      ctx.lineCap = "round";
      ctx.strokeStyle = "rgba(255, 255, 255, 0.8)"; // Blanco semi-transparente
      ctx.lineWidth = brushSize;

      setImageLoaded(true);
    };
  }, [imageUrl]);

  // Actualizar el tamaño del pincel cuando cambie
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !imageLoaded) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Actualizar el tamaño del pincel
    ctx.lineWidth = brushSize;
  }, [brushSize, imageLoaded]);

  // Funciones de dibujo
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!imageLoaded || isProcessing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    setIsDrawing(true);
    setHasMask(true);

    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    ctx.beginPath();
    ctx.moveTo(
      (e.clientX - rect.left) * scaleX,
      (e.clientY - rect.top) * scaleY
    );
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !imageLoaded || isProcessing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    ctx.lineTo(
      (e.clientX - rect.left) * scaleX,
      (e.clientY - rect.top) * scaleY
    );
    ctx.stroke();
  };

  const endDrawing = () => {
    setIsDrawing(false);
  };

  // Limpiar la máscara
  const clearMask = () => {
    const canvas = canvasRef.current;
    if (!canvas || !imageUrl) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Recargar la imagen original
    const img = new Image();
    img.crossOrigin = "anonymous";
    img.src = imageUrl;

    img.onload = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0);
      setHasMask(false);
    };
  };

  // Crear máscara y procesar imagen
  const handleEraseObjects = async () => {
    if (!imageFile || !hasMask) {
      toast({
        title: "❌ Error",
        description: "Debes dibujar una máscara sobre las áreas que deseas borrar",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsProcessing(true);

      // Crear máscara desde el canvas
      const canvas = canvasRef.current;
      if (!canvas) throw new Error("Canvas no disponible");

      // Crear canvas temporal para la máscara
      const maskCanvas = document.createElement("canvas");
      maskCanvas.width = canvas.width;
      maskCanvas.height = canvas.height;
      const maskCtx = maskCanvas.getContext("2d");
      if (!maskCtx) throw new Error("No se pudo crear contexto de máscara");

      // Fondo negro (áreas a preservar)
      maskCtx.fillStyle = "black";
      maskCtx.fillRect(0, 0, maskCanvas.width, maskCanvas.height);

      // Copiar solo las áreas dibujadas (blancas) del canvas original
      const originalCtx = canvas.getContext("2d");
      if (!originalCtx) throw new Error("No se pudo obtener contexto original");

      // Obtener los datos de imagen del canvas original
      const imageData = originalCtx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      // Crear nueva imagen de máscara
      const maskImageData = maskCtx.createImageData(canvas.width, canvas.height);
      const maskData = maskImageData.data;

      // Procesar píxel por píxel para extraer solo las áreas dibujadas
      for (let i = 0; i < data.length; i += 4) {
        // Si el píxel es blanco semi-transparente (máscara dibujada)
        if (data[i] > 200 && data[i + 1] > 200 && data[i + 2] > 200 && data[i + 3] > 100) {
          maskData[i] = 255;     // R
          maskData[i + 1] = 255; // G
          maskData[i + 2] = 255; // B
          maskData[i + 3] = 255; // A
        } else {
          maskData[i] = 0;       // R
          maskData[i + 1] = 0;   // G
          maskData[i + 2] = 0;   // B
          maskData[i + 3] = 255; // A
        }
      }

      maskCtx.putImageData(maskImageData, 0, 0);

      // Convertir máscara a blob
      const maskBlob = await new Promise<Blob>((resolve) => {
        maskCanvas.toBlob((blob) => {
          if (blob) resolve(blob);
        }, "image/png");
      });

      if (!maskBlob) throw new Error("No se pudo crear la máscara");

      // Crear FormData
      const formData = new FormData();
      formData.append('image', imageFile);
      formData.append('mask', maskBlob, 'mask.png');
      formData.append('output_format', outputFormat);
      formData.append('grow_mask', growMask.toString());

      // Llamar a la API
      const response = await fetch('/api/v1/ai-editor/erase', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al procesar la imagen');
      }

      const result = await response.json();

      if (result.success && result.image_url) {
        onResult(result.image_url);
        toast({
          title: "🎉 ¡Objetos borrados!",
          description: "La imagen ha sido procesada exitosamente",
        });
      } else {
        throw new Error(result.error || 'Error desconocido');
      }

    } catch (error) {
      console.error('Error erasing objects:', error);
      toast({
        title: "❌ Error",
        description: error instanceof Error ? error.message : "Error al procesar la imagen",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Información de la herramienta */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Info className="h-5 w-5 text-blue-500 mt-0.5" />
          <div>
            <h4 className="font-semibold text-blue-900">Borrar Objetos</h4>
            <p className="text-sm text-blue-700 mt-1">
              Dibuja sobre los objetos que deseas eliminar de la imagen. Las áreas marcadas serán borradas automáticamente.
            </p>
          </div>
        </div>
      </div>

      {/* Canvas para dibujar la máscara */}
      <div className="space-y-4">
        <h4 className="font-medium">Dibuja sobre los objetos a borrar:</h4>
        <div className="border rounded-lg overflow-hidden bg-gray-50">
          <canvas
            ref={canvasRef}
            onMouseDown={startDrawing}
            onMouseMove={draw}
            onMouseUp={endDrawing}
            onMouseLeave={endDrawing}
            className="max-w-full cursor-crosshair block mx-auto"
            style={{
              maxHeight: "400px",
              width: "100%",
              objectFit: "contain",
              backgroundColor: "#f9fafb"
            }}
          />
        </div>

        {/* Controles del pincel */}
        <div className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Tamaño del pincel:</span>
            <input
              type="range"
              min="5"
              max="50"
              value={brushSize}
              onChange={(e) => setBrushSize(parseInt(e.target.value))}
              className="w-24"
              disabled={isProcessing}
            />
            <span className="text-sm text-gray-600 w-8">{brushSize}px</span>
          </div>

          <Button
            onClick={clearMask}
            variant="outline"
            size="sm"
            disabled={!hasMask || isProcessing}
          >
            <RotateCcw className="w-4 h-4 mr-1" />
            Limpiar
          </Button>
        </div>
      </div>

      {/* Configuración avanzada */}
      <div className="space-y-4">
        <h4 className="font-medium">Configuración:</h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Formato de salida */}
          <div>
            <label className="block text-sm font-medium mb-2">Formato de salida:</label>
            <select
              value={outputFormat}
              onChange={(e) => setOutputFormat(e.target.value as "png" | "webp" | "jpeg")}
              className="w-full p-2 border border-gray-300 rounded-md"
              disabled={isProcessing}
            >
              <option value="webp">WebP (Recomendado)</option>
              <option value="png">PNG (Transparencia)</option>
              <option value="jpeg">JPEG (Menor tamaño)</option>
            </select>
          </div>

          {/* Expandir máscara */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Expandir máscara: {growMask}px
            </label>
            <input
              type="range"
              min="0"
              max="20"
              value={growMask}
              onChange={(e) => setGrowMask(parseInt(e.target.value))}
              className="w-full"
              disabled={isProcessing}
            />
            <p className="text-xs text-gray-500 mt-1">
              Expande los bordes de la máscara para suavizar transiciones
            </p>
          </div>
        </div>
      </div>

      {/* Botón de procesamiento */}
      <Button
        onClick={handleEraseObjects}
        disabled={!hasMask || isProcessing}
        className="w-full"
        size="lg"
      >
        {isProcessing ? (
          <>
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2"></div>
            Borrando objetos...
          </>
        ) : (
          <>
            <Eraser className="w-4 h-4 mr-2" />
            Borrar Objetos Marcados
          </>
        )}
      </Button>

      {/* Información de créditos */}
      <div className="text-center text-sm text-muted-foreground">
        <p>💳 Esta operación consume <strong>3 créditos</strong></p>
      </div>

      {/* Instrucciones */}
      <div className="text-sm text-gray-600 space-y-1">
        <p><strong>Instrucciones:</strong></p>
        <ul className="list-disc list-inside space-y-1 ml-2">
          <li>Dibuja sobre los objetos que deseas eliminar</li>
          <li>Usa un pincel más grande para objetos grandes</li>
          <li>Puedes limpiar y volver a dibujar si es necesario</li>
          <li>La herramienta funciona mejor con objetos bien definidos</li>
        </ul>
      </div>
    </div>
  );
}

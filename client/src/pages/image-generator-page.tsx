/**
 * Image Generator Page - Generación de imágenes con IA
 * Basado en logo-generator-page.tsx con el mismo layout y funcionalidad dual
 */

import React, { useState, useRef, useCallback } from "react";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Type,
  Wand2,
  RefreshCw,
  Download,
  Share2,
  Heart,
  HeartOff,
  Upload,
  X,
  Brush,
  Eraser,
  Sparkles,
  ImageIcon,
  Edit3,
  FileImage,
  Trash2,
  Eye,
  EyeOff,
  RotateCcw,
  Settings,
  Layers,
  PaintBucket,
  Zap,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { useToast } from "@/hooks/use-toast";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  generateImage as generateImageWithIdeogram,
  editWithReferences,
  editWithMask,
  validateImageFile,
  type ImageGeneratorOptions,
  type ImageGeneratorResponse
} from "@/services/image-generator-service";
import {
  generateImageAsBackgroundTask,
  ImageGenerationOptions,
} from "@/services/stability-image-generator-service";
import { useBackgroundTasks } from "@/context/BackgroundTasksContext";
import { Progress } from "@/components/ui/progress";

// Tipos para el estado de la aplicación
interface GeneratedImage {
  id: string;
  image_url: string;
  prompt: string;
  revised_prompt?: string;
  response_id?: string;
  metadata?: any;
  timestamp: number;
}

interface SavedImage {
  id: string;
  image_url: string;
  prompt: string;
  revised_prompt?: string;
  metadata?: any;
  type: "basic" | "reference" | "mask_edit";
  timestamp: number;
}

// Interfaz para imágenes del generador Stability
interface SavedStabilityImage {
  id: string;
  image_url: string;
  prompt: string;
  model: string;
  stylePreset: string;
  metadata?: any;
  timestamp: number;
  type: "stability";
}

// Tipo para el estado de generación (Stability AI)
interface GenerationState {
  isGenerating: boolean;
  progress: number;
  message: string;
}

// Hooks para localStorage
function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue] as const;
}

// Utilidades para imágenes guardadas
const SAVED_IMAGES_KEY = "emma-saved-images";

// Modelos de IA disponibles
const AI_MODELS = [
  {
    value: "ultra",
    label: "Calidad",
    description: "Máxima calidad",
    icon: Sparkles,
    credits: 8,
  },
  {
    value: "core",
    label: "Rápido", 
    description: "Generación rápida",
    icon: Zap,
    credits: 4,
  },
  {
    value: "sd3",
    label: "Balanceado",
    description: "Equilibrio perfecto",
    icon: Settings,
    credits: 6,
  },
];

// Estilos visuales
const STYLE_PRESETS = [
  { value: "photographic", label: "Fotográfico", description: "Realista y detallado" },
  { value: "digital-art", label: "Arte Digital", description: "Estilo artístico moderno" },
  { value: "comic-book", label: "Cómic", description: "Estilo de cómic" },
  { value: "fantasy-art", label: "Fantasía", description: "Arte fantástico" },
  { value: "line-art", label: "Arte Lineal", description: "Dibujos con líneas" },
  { value: "analog-film", label: "Película Analógica", description: "Estilo vintage" },
  { value: "neon-punk", label: "Neon Punk", description: "Cyberpunk neón" },
  { value: "isometric", label: "Isométrico", description: "Vista isométrica" },
  { value: "low-poly", label: "Low Poly", description: "Estilo poligonal" },
  { value: "origami", label: "Origami", description: "Estilo papel plegado" },
];

export default function ImageGeneratorPage() {
  // Estado para seleccionar el modo de generación
  const [currentMode, setCurrentMode] = useState<"stability" | "openai">("openai");

  // Estados para el modo Ideogram (Generación con Texto)
  const [currentImage, setCurrentImage] = useState<GeneratedImage | null>(null);
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);

  // Estados para el control panel de Ideogram (exactos del poster-creator)
  const [resolution, setResolution] = useState<string>("1024x1024");
  const [aspectRatio, setAspectRatio] = useState<string | null>(null);
  const [useAspectRatio, setUseAspectRatio] = useState(false);
  const [renderingSpeed, setRenderingSpeed] = useState<"TURBO" | "DEFAULT" | "QUALITY">("DEFAULT");
  const [magicPrompt, setMagicPrompt] = useState<"AUTO" | "ON" | "OFF">("AUTO");
  const [negativePrompt, setNegativePrompt] = useState("");
  const [numImages, setNumImages] = useState<number>(1);
  const [styleType, setStyleType] = useState<"AUTO" | "GENERAL" | "REALISTIC" | "DESIGN">("GENERAL");

  // Estados para el modo Stability AI (Generación Standard)
  const [stabilityPrompt, setStabilityPrompt] = useState("");
  const [selectedModel, setSelectedModel] = useState("ultra");
  const [stylePreset, setStylePreset] = useState("photographic");
  const [outputFormat, setOutputFormat] = useState<"png" | "jpeg" | "webp">("webp");
  const [generationState, setGenerationState] = useState<GenerationState>({
    isGenerating: false,
    progress: 0,
    message: "",
  });
  const [generatedStabilityImage, setGeneratedStabilityImage] = useState<string | null>(null);

  // Estados para edición con referencias
  const [referenceImages, setReferenceImages] = useState<File[]>([]);
  const [referencePrompt, setReferencePrompt] = useState("");

  // Estados para edición con máscara
  const [editingImage, setEditingImage] = useState<string | null>(null);
  const [maskPrompt, setMaskPrompt] = useState("");
  const [isDrawing, setIsDrawing] = useState(false);
  const [brushSize, setBrushSize] = useState(20);
  const [showMask, setShowMask] = useState(true);

  // Estados para favoritos (OpenAI)
  const [savedImages, setSavedImages] = useLocalStorage<SavedImage[]>(SAVED_IMAGES_KEY, []);
  const [currentImageSaved, setCurrentImageSaved] = useState(false);
  const [mainTab, setMainTab] = useState<"latest" | "saved">("latest");

  // Estados para favoritos del generador Stability
  const [savedStabilityImages, setSavedStabilityImages] = useLocalStorage<SavedStabilityImage[]>("emma-saved-stability-images", []);
  const [currentStabilityImageSaved, setCurrentStabilityImageSaved] = useState(false);
  const [stabilityMainTab, setStabilityMainTab] = useState<"latest" | "saved">("latest");



  const { toast } = useToast();
  const { addTask, updateTask } = useBackgroundTasks();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const maskCanvasRef = useRef<HTMLCanvasElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);

  // Prompts de ejemplo para imágenes con texto (Ideogram)
  const examplePrompts = [
    "Un paisaje futurista con ciudades flotantes y luces neón",
    "Retrato artístico de una persona en estilo cyberpunk con elementos digitales",
    "Naturaleza abstracta con colores vibrantes y formas geométricas",
    "Arquitectura moderna con elementos orgánicos y diseño sostenible",
    "Escena de fantasía con criaturas mágicas en un bosque encantado",
    "Arte digital minimalista con paleta de colores suaves"
  ];

  // Resoluciones soportadas por Ideogram v3 (exactas del poster-creator)
  const resolutionOptions = [
    // Cuadradas
    { value: "1024x1024", label: "Cuadrado", ratio: "1:1", category: "square" },

    // Verticales (Portrait) - usando resoluciones válidas
    { value: "512x1536", label: "Vertical Ultra", ratio: "1:3", category: "portrait" },
    { value: "576x1536", label: "Vertical Alto", ratio: "3:8", category: "portrait" },
    { value: "640x1536", label: "Vertical Estándar", ratio: "5:12", category: "portrait" },
    { value: "704x1472", label: "Vertical Medio", ratio: "11:23", category: "portrait" },
    { value: "768x1344", label: "Vertical Compacto", ratio: "4:7", category: "portrait" },
    { value: "832x1216", label: "Vertical Clásico", ratio: "13:19", category: "portrait" },
    { value: "896x1152", label: "Vertical Suave", ratio: "7:9", category: "portrait" },
    { value: "960x1088", label: "Vertical Ligero", ratio: "15:17", category: "portrait" },

    // Horizontales (Landscape) - usando resoluciones válidas
    { value: "1536x512", label: "Horizontal Ultra", ratio: "3:1", category: "landscape" },
    { value: "1536x576", label: "Horizontal Alto", ratio: "8:3", category: "landscape" },
    { value: "1536x640", label: "Horizontal Estándar", ratio: "12:5", category: "landscape" },
    { value: "1472x704", label: "Horizontal Medio", ratio: "23:11", category: "landscape" },
    { value: "1344x768", label: "Horizontal Compacto", ratio: "7:4", category: "landscape" },
    { value: "1216x832", label: "Horizontal Clásico", ratio: "19:13", category: "landscape" },
    { value: "1152x896", label: "Horizontal Suave", ratio: "9:7", category: "landscape" },
    { value: "1088x960", label: "Horizontal Ligero", ratio: "17:15", category: "landscape" }
  ];

  // Relaciones de aspecto (exactas del poster-creator)
  const aspectRatioOptions = [
    { value: "1x1", label: "Cuadrado (1:1)", icon: "⬜" },
    { value: "1x2", label: "Vertical 1:2", icon: "▯" },
    { value: "2x1", label: "Horizontal 2:1", icon: "▭" },
    { value: "1x3", label: "Vertical 1:3", icon: "▯" },
    { value: "3x1", label: "Horizontal 3:1", icon: "▭" },
    { value: "9x16", label: "Móvil Vertical", icon: "📱" },
    { value: "16x9", label: "Pantalla Ancha", icon: "🖥️" },
    { value: "2x3", label: "Póster Vertical", icon: "🖼️" },
    { value: "3x2", label: "Póster Horizontal", icon: "🖼️" },
    { value: "3x4", label: "Retrato", icon: "🖼️" },
    { value: "4x3", label: "Paisaje", icon: "🖼️" },
    { value: "4x5", label: "Clásico Vertical", icon: "📄" },
    { value: "5x4", label: "Clásico Horizontal", icon: "📄" }
  ];

  // Opciones de velocidad de renderizado (exactas del poster-creator)
  const speedOptions = [
    { value: "TURBO", label: "Turbo", description: "Generación más rápida", icon: "⚡" },
    { value: "DEFAULT", label: "Estándar", description: "Equilibrio entre velocidad y calidad", icon: "⚖️" },
    { value: "QUALITY", label: "Calidad", description: "Máxima calidad de imagen", icon: "💎" }
  ];

  // Opciones de estilo (exactas del poster-creator)
  const styleOptions = [
    { value: "AUTO", label: "Automático", description: "Selección automática de estilo", icon: "🤖" },
    { value: "GENERAL", label: "General", description: "Estilo versátil para cualquier contenido", icon: "🎨" },
    { value: "REALISTIC", label: "Realista", description: "Estilo fotorrealista", icon: "📸" },
    { value: "DESIGN", label: "Diseño", description: "Estilo de diseño gráfico profesional", icon: "🎯" }
  ];



  // Opciones de formato
  const formatOptions = [
    { value: "webp", label: "WebP", description: "Mejor compresión" },
    { value: "png", label: "PNG", description: "Sin pérdida" },
    { value: "jpeg", label: "JPEG", description: "Tamaño pequeño" }
  ];

  // Función para generar imagen con Ideogram (Generación con Texto)
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Prompt requerido",
        description: "Por favor, describe la imagen que quieres crear",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const options: ImageGeneratorOptions = {
        prompt, // NO MODIFICATIONS - User has complete creative freedom
        resolution: useAspectRatio ? undefined : resolution,
        aspect_ratio: useAspectRatio ? aspectRatio : undefined,
        rendering_speed: renderingSpeed,
        magic_prompt: magicPrompt,
        negative_prompt: negativePrompt.trim() || undefined,
        num_images: numImages,
        style_type: styleType
      };

      const result = await generateImageWithIdeogram(options);

      if (result.success && result.image_url) {
        const newImage: GeneratedImage = {
          id: Date.now().toString(),
          image_url: result.image_url,
          prompt,
          revised_prompt: result.revised_prompt,
          response_id: result.response_id,
          metadata: result.metadata,
          timestamp: Date.now(),
        };

        setCurrentImage(newImage);

        toast({
          title: "¡Imagen generada!",
          description: "Tu imagen ha sido creada exitosamente",
        });
      } else {
        throw new Error(result.error || "Error desconocido");
      }
    } catch (error) {
      console.error("Error generating image:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al generar la imagen",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para generar imagen con Stability AI
  const handleGenerateStability = async () => {
    if (!stabilityPrompt.trim()) {
      toast({
        title: "Prompt requerido",
        description: "Por favor, describe la imagen que quieres crear",
        variant: "destructive",
      });
      return;
    }

    setGenerationState({
      isGenerating: true,
      progress: 0,
      message: "Iniciando generación...",
    });

    try {
      const options: ImageGenerationOptions = {
        prompt: stabilityPrompt,
        model: selectedModel,
        stylePreset,
        outputFormat,
      };

      // Función de callback para progreso
      const progressCallback = (progress: number, message?: string) => {
        setGenerationState({
          isGenerating: true,
          progress,
          message: message || `Progreso: ${progress}%`,
        });
      };

      // Generar imagen
      const imageUrl = await generateImageAsBackgroundTask(options, progressCallback);

      // Actualizar estado con resultado
      setGeneratedStabilityImage(imageUrl);
      setGenerationState({
        isGenerating: false,
        progress: 100,
        message: "Imagen generada exitosamente",
      });

      toast({
        title: "¡Imagen generada!",
        description: "Tu imagen ha sido creada exitosamente",
      });

    } catch (error) {
      console.error("Error generating image:", error);

      setGenerationState({
        isGenerating: false,
        progress: 0,
        message: "",
      });

      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al generar la imagen",
        variant: "destructive",
      });
    }
  };



  // Función para descargar imagen usando proxy backend (igual que poster-creator)
  const handleDownload = async (imageUrl: string, filename?: string) => {
    if (!imageUrl) {
      toast({
        title: "❌ Error",
        description: "No hay imagen para descargar",
        variant: "destructive",
      });
      return;
    }

    console.log('Starting download via backend proxy for URL:', imageUrl);

    try {
      // Use our backend proxy to download the image
      const proxyUrl = `/api/image-generator/download-image?url=${encodeURIComponent(imageUrl)}`;

      console.log('Fetching from proxy URL:', proxyUrl);

      const response = await fetch(proxyUrl, {
        method: 'GET',
        headers: {
          'Accept': 'image/*',
        },
      });

      console.log('Proxy response status:', response.status);

      if (!response.ok) {
        throw new Error(`Proxy error! status: ${response.status}`);
      }

      // Get the blob from our backend proxy
      const blob = await response.blob();
      console.log('Blob created from proxy, size:', blob.size, 'type:', blob.type);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;

      // Try to get filename from Content-Disposition header, fallback to provided filename or timestamp
      const contentDisposition = response.headers.get('Content-Disposition');
      let downloadFilename = filename || `imagen-${Date.now()}.png`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch && filenameMatch[1]) {
          downloadFilename = filenameMatch[1].replace(/['"]/g, '');
        }
      }

      a.download = downloadFilename;
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();

      console.log('Download initiated with filename:', downloadFilename);

      // Clean up
      setTimeout(() => {
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        console.log('Download cleanup completed');
      }, 100);

      toast({
        title: "✅ Descarga completada",
        description: "La imagen se ha descargado correctamente",
      });

    } catch (error) {
      console.error('Download error:', error);

      toast({
        title: "❌ Error de descarga",
        description: "No se pudo descargar la imagen. Por favor, intenta de nuevo.",
        variant: "destructive",
      });
    }
  };

  // Función para manejar archivos de referencia
  const handleReferenceFiles = (files: FileList | null) => {
    if (!files) return;

    const validFiles: File[] = [];
    const maxFiles = 4;

    for (let i = 0; i < Math.min(files.length, maxFiles); i++) {
      const file = files[i];
      const validation = validateImageFile(file);

      if (validation.isValid) {
        validFiles.push(file);
      } else {
        toast({
          title: "Archivo inválido",
          description: validation.error,
          variant: "destructive",
        });
      }
    }

    if (validFiles.length > 0) {
      setReferenceImages(prev => {
        const newFiles = [...prev, ...validFiles];
        return newFiles.slice(0, maxFiles);
      });

      toast({
        title: "Archivos agregados",
        description: `${validFiles.length} imagen(es) agregada(s) como referencia`,
      });
    }
  };

  // Función para generar con referencias
  const handleGenerateWithReferences = async () => {
    if (!referencePrompt.trim()) {
      toast({
        title: "Descripción requerida",
        description: "Por favor, describe la imagen que quieres crear",
        variant: "destructive",
      });
      return;
    }

    if (referenceImages.length === 0) {
      toast({
        title: "Referencias requeridas",
        description: "Por favor, sube al menos una imagen de referencia",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const result = await editWithReferences({
        prompt: referencePrompt, // NO MODIFICATIONS - User has complete creative freedom
        referenceImages: referenceImages,
        resolution: useAspectRatio ? undefined : resolution,
        aspect_ratio: useAspectRatio ? aspectRatio : undefined,
      });

      if (result.success && result.image_url) {
        const newImage: GeneratedImage = {
          id: Date.now().toString(),
          image_url: result.image_url,
          prompt: referencePrompt,
          revised_prompt: result.revised_prompt,
          response_id: result.response_id,
          metadata: { ...result.metadata, type: "reference" },
          timestamp: Date.now(),
        };

        setCurrentImage(newImage);

        toast({
          title: "¡Imagen generada!",
          description: "Tu imagen con referencias ha sido creada exitosamente",
        });
      } else {
        throw new Error(result.error || "Error desconocido");
      }
    } catch (error) {
      console.error("Error generating with references:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al generar con referencias",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para cargar imagen para editar
  const handleLoadImageForEdit = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string;
      setEditingImage(imageUrl);

      // Cargar imagen en el canvas
      setTimeout(() => {
        const canvas = canvasRef.current;
        const maskCanvas = maskCanvasRef.current;
        if (canvas && maskCanvas) {
          const ctx = canvas.getContext('2d');
          const maskCtx = maskCanvas.getContext('2d');
          const img = new Image();

          img.onload = () => {
            // Calcular tamaño apropiado para el canvas (máximo 600px de ancho)
            const maxWidth = 600;
            const maxHeight = 400;
            let { width, height } = img;

            if (width > maxWidth) {
              height = (height * maxWidth) / width;
              width = maxWidth;
            }

            if (height > maxHeight) {
              width = (width * maxHeight) / height;
              height = maxHeight;
            }

            // Configurar tamaño del canvas
            canvas.width = width;
            canvas.height = height;
            maskCanvas.width = width;
            maskCanvas.height = height;

            // Aplicar tamaño CSS para que se vea correctamente
            canvas.style.width = `${width}px`;
            canvas.style.height = `${height}px`;
            maskCanvas.style.width = `${width}px`;
            maskCanvas.style.height = `${height}px`;

            // Dibujar imagen original escalada
            ctx?.drawImage(img, 0, 0, width, height);

            // Limpiar máscara (fondo negro)
            if (maskCtx) {
              maskCtx.fillStyle = 'black';
              maskCtx.fillRect(0, 0, width, height);
            }
          };

          img.src = imageUrl;
        }
      }, 100);
    };
    reader.readAsDataURL(file);
  };

  // Funciones para dibujar en el canvas
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    draw(e);
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const canvas = maskCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();

    // Calcular la escala entre el canvas real y el canvas mostrado
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    // Ajustar coordenadas según la escala
    const x = (e.clientX - rect.left) * scaleX;
    const y = (e.clientY - rect.top) * scaleY;

    ctx.globalCompositeOperation = 'source-over';
    ctx.fillStyle = 'white';
    ctx.beginPath();
    ctx.arc(x, y, (brushSize / 2) * scaleX, 0, 2 * Math.PI);
    ctx.fill();
  };

  // Función para limpiar máscara
  const clearMask = () => {
    const canvas = maskCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.fillStyle = 'black';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  };

  // Función para aplicar edición con máscara
  const handleApplyMaskEdit = async () => {
    if (!editingImage || !maskPrompt.trim()) {
      toast({
        title: "Datos incompletos",
        description: "Necesitas una imagen cargada, una máscara dibujada y una descripción",
        variant: "destructive",
      });
      return;
    }

    const maskCanvas = maskCanvasRef.current;
    if (!maskCanvas) return;

    setIsGenerating(true);

    try {
      // Convertir máscara a blob
      const maskBlob = await new Promise<Blob>((resolve) => {
        maskCanvas.toBlob((blob) => {
          if (blob) resolve(blob);
        }, 'image/png');
      });

      // Convertir imagen original a blob
      const response = await fetch(editingImage);
      const imageBlob = await response.blob();

      const result = await editWithMask({
        prompt: maskPrompt,
        image: new File([imageBlob], 'image.png', { type: 'image/png' }),
        mask: new File([maskBlob], 'mask.png', { type: 'image/png' }),
      });

      if (result.success && result.image_url) {
        const newImage: GeneratedImage = {
          id: Date.now().toString(),
          image_url: result.image_url,
          prompt: maskPrompt,
          revised_prompt: result.revised_prompt,
          response_id: result.response_id,
          metadata: { ...result.metadata, type: "mask_edit" },
          timestamp: Date.now(),
        };

        setCurrentImage(newImage);

        toast({
          title: "¡Edición aplicada!",
          description: "Tu imagen editada ha sido creada exitosamente",
        });
      } else {
        throw new Error(result.error || "Error desconocido");
      }
    } catch (error) {
      console.error("Error applying mask edit:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al aplicar la edición",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para manejar favoritos de Stability
  const handleToggleStabilityFavorite = () => {
    if (!generatedStabilityImage) return;

    try {
      if (currentStabilityImageSaved) {
        // Quitar de favoritos
        const savedImage = savedStabilityImages.find(img => img.image_url === generatedStabilityImage);
        if (savedImage) {
          const filteredImages = savedStabilityImages.filter(img => img.id !== savedImage.id);
          setSavedStabilityImages(filteredImages);
          setCurrentStabilityImageSaved(false);

          toast({
            title: "💔 Eliminado de favoritos",
            description: "La imagen ha sido eliminada de tus favoritos.",
          });
        }
      } else {
        // Agregar a favoritos
        const savedImage: SavedStabilityImage = {
          id: `stability-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          image_url: generatedStabilityImage,
          prompt: stabilityPrompt,
          model: selectedModel,
          stylePreset,
          metadata: { outputFormat },
          timestamp: Date.now(),
          type: "stability",
        };

        setSavedStabilityImages(prev => [savedImage, ...prev]);
        setCurrentStabilityImageSaved(true);

        toast({
          title: "💖 Agregado a favoritos",
          description: "La imagen ha sido guardada en tus favoritos.",
        });
      }
    } catch (error) {
      console.error("Error toggling favorite:", error);
      toast({
        title: "Error",
        description: "No se pudo actualizar los favoritos",
        variant: "destructive",
      });
    }
  };

  // Función para manejar favoritos de OpenAI
  const handleToggleFavorite = () => {
    if (!currentImage) return;

    try {
      if (currentImageSaved) {
        // Quitar de favoritos
        const savedImage = savedImages.find(img => img.image_url === currentImage.image_url);
        if (savedImage) {
          const filteredImages = savedImages.filter(img => img.id !== savedImage.id);
          setSavedImages(filteredImages);
          setCurrentImageSaved(false);

          toast({
            title: "💔 Eliminado de favoritos",
            description: "La imagen ha sido eliminada de tus favoritos.",
          });
        }
      } else {
        // Agregar a favoritos
        const savedImage = createSavedImage({
          image_url: currentImage.image_url,
          prompt: currentImage.prompt,
          revised_prompt: currentImage.revised_prompt,
          metadata: currentImage.metadata,
          type: "basic",
        });

        setSavedImages(prev => [savedImage, ...prev]);
        setCurrentImageSaved(true);

        toast({
          title: "💖 Agregado a favoritos",
          description: "La imagen ha sido guardada en tus favoritos.",
        });
      }
    } catch (error) {
      console.error("Error al manejar favoritos:", error);
      toast({
        title: "Error",
        description: "No se pudo actualizar los favoritos",
        variant: "destructive",
      });
    }
  };





  // Verificar si la imagen actual está guardada
  React.useEffect(() => {
    if (currentImage) {
      const isImageSaved = savedImages.some(img => img.image_url === currentImage.image_url);
      setCurrentImageSaved(isImageSaved);
    }
  }, [currentImage, savedImages]);

  // Verificar si la imagen de Stability actual está guardada
  React.useEffect(() => {
    if (generatedStabilityImage) {
      const isImageSaved = savedStabilityImages.some(img => img.image_url === generatedStabilityImage);
      setCurrentStabilityImageSaved(isImageSaved);
    }
  }, [generatedStabilityImage, savedStabilityImages]);



  return (
    <DashboardLayout pageTitle="Generador de Imágenes IA">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="relative bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700 rounded-2xl p-8 mb-8 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <Sparkles className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-4xl font-bold text-white">
                  Generador de Imágenes IA
                </h1>
              </div>
              <p className="text-xl text-purple-100 mb-6 max-w-3xl">
                Crea imágenes impactantes con IA. Genera con libertad creativa total, usa referencias visuales o edita con precisión.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge className="bg-white/20 text-white border-white/30">
                  <Brush className="w-3 h-3 mr-1" />
                  Editor integrado
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <ImageIcon className="w-3 h-3 mr-1" />
                  Referencias visuales
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Edit3 className="w-3 h-3 mr-1" />
                  Edición precisa
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Sparkles className="w-3 h-3 mr-1" />
                  3 Modelos IA
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Zap className="w-3 h-3 mr-1" />
                  Generación rápida
                </Badge>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Toggle de Modo */}
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <Tabs value={currentMode} onValueChange={(value) => setCurrentMode(value as "stability" | "openai")} className="w-full">
            <TabsList className="grid w-full grid-cols-2 max-w-2xl mx-auto">
              <TabsTrigger value="stability" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Generación Standard
              </TabsTrigger>
              <TabsTrigger value="openai" className="flex items-center gap-2">
                <Type className="h-4 w-4" />
                Generación con Texto
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </motion.div>

        {/* Modo Stability AI - Generación Standard */}
        {currentMode === "stability" && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Panel de Control */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="lg:col-span-1"
            >
              <Card className="sticky top-6 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-xl">
                    <Settings className="h-5 w-5 text-purple-600" />
                    Panel de Control
                  </CardTitle>
                  <CardDescription>
                    Configura y genera tu imagen perfecta
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Prompt */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Descripción de la Imagen</Label>
                    <Textarea
                      placeholder="Describe la imagen que quieres crear..."
                      value={stabilityPrompt}
                      onChange={(e) => setStabilityPrompt(e.target.value)}
                      className="min-h-[100px] resize-none"
                    />
                  </div>

                  {/* Modelo de IA */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Modelo de IA</Label>
                    <div className="grid grid-cols-1 gap-2">
                      {AI_MODELS.map((model) => (
                        <Button
                          key={model.value}
                          type="button"
                          variant={selectedModel === model.value ? "default" : "outline"}
                          size="sm"
                          onClick={() => setSelectedModel(model.value)}
                          className={`justify-start transition-all ${
                            selectedModel === model.value
                              ? "bg-purple-600 hover:bg-purple-700 text-white shadow-md"
                              : "hover:bg-purple-50 hover:border-purple-300"
                          }`}
                        >
                          <model.icon className="mr-2 h-4 w-4" />
                          <div className="text-left">
                            <div className="font-medium">{model.label}</div>
                            <div className="text-xs opacity-75">
                              {model.description}
                            </div>
                          </div>
                          {selectedModel === model.value && (
                            <div className="ml-auto">
                              <div className="w-2 h-2 bg-white rounded-full"></div>
                            </div>
                          )}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Estilo Visual */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Estilo Visual</Label>
                    <Select value={stylePreset} onValueChange={setStylePreset}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecciona un estilo" />
                      </SelectTrigger>
                      <SelectContent>
                        {STYLE_PRESETS.map((style) => (
                          <SelectItem key={style.value} value={style.value}>
                            <div>
                              <div className="font-medium">{style.label}</div>
                              <div className="text-xs text-gray-500">{style.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Formato de Salida */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Formato de Salida</Label>
                    <div className="grid grid-cols-3 gap-2">
                      {formatOptions.map((format) => (
                        <Button
                          key={format.value}
                          type="button"
                          variant={outputFormat === format.value ? "default" : "outline"}
                          size="sm"
                          onClick={() => setOutputFormat(format.value as "png" | "jpeg" | "webp")}
                          className={`transition-all ${
                            outputFormat === format.value
                              ? "bg-purple-600 hover:bg-purple-700 text-white shadow-md"
                              : "hover:bg-purple-50 hover:border-purple-300"
                          }`}
                        >
                          <div className="text-center">
                            <div className="font-medium text-xs">{format.label}</div>
                            <div className="text-xs opacity-75">{format.description}</div>
                          </div>
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Botón de Generar */}
                  <Button
                    onClick={handleGenerateStability}
                    disabled={generationState.isGenerating || !stabilityPrompt.trim()}
                    className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                  >
                    {generationState.isGenerating ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Generando...
                      </>
                    ) : (
                      <>
                        <Wand2 className="h-4 w-4 mr-2" />
                        Generar Imagen
                      </>
                    )}
                  </Button>

                  {/* Progreso */}
                  {generationState.isGenerating && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progreso</span>
                        <span>{generationState.progress}%</span>
                      </div>
                      <Progress value={generationState.progress} className="w-full" />
                      <p className="text-xs text-gray-500">{generationState.message}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>

            {/* Área de Visualización Stability */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="lg:col-span-3"
            >
              <Tabs value={stabilityMainTab} onValueChange={(value) => setStabilityMainTab(value as "latest" | "saved")} className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-6">
                  <TabsTrigger value="latest" className="flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    Última Generación
                  </TabsTrigger>
                  <TabsTrigger value="saved" className="flex items-center gap-2">
                    <Heart className="h-4 w-4" />
                    Guardados ({savedStabilityImages.length})
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="latest" className="space-y-6">
                  {/* Imagen generada con Stability */}
                  {generatedStabilityImage && (
                    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                      <CardHeader className="pb-4">
                        <div className="flex items-center justify-between">
                          <CardTitle className="flex items-center gap-2">
                            <ImageIcon className="h-5 w-5 text-purple-600" />
                            Imagen Generada
                          </CardTitle>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleToggleStabilityFavorite}
                              className={currentStabilityImageSaved ? "text-red-600 border-red-200" : ""}
                            >
                              {currentStabilityImageSaved ? (
                                <Heart className="h-4 w-4 fill-current" />
                              ) : (
                                <HeartOff className="h-4 w-4" />
                              )}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={async () => {
                                try {
                                  const response = await fetch(generatedStabilityImage);
                                  const blob = await response.blob();
                                  const url = window.URL.createObjectURL(blob);
                                  const a = document.createElement("a");
                                  a.href = url;
                                  a.download = `imagen-${Date.now()}.${outputFormat}`;
                                  document.body.appendChild(a);
                                  a.click();
                                  window.URL.revokeObjectURL(url);
                                  document.body.removeChild(a);

                                  toast({
                                    title: "Descarga iniciada",
                                    description: "La imagen se está descargando",
                                  });
                                } catch (error) {
                                  toast({
                                    title: "Error",
                                    description: "No se pudo descargar la imagen",
                                    variant: "destructive",
                                  });
                                }
                              }}
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Descargar
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={async () => {
                                try {
                                  await navigator.clipboard.writeText(generatedStabilityImage);
                                  toast({
                                    title: "Enlace copiado",
                                    description: "El enlace de la imagen se copió al portapapeles",
                                  });
                                } catch (error) {
                                  toast({
                                    title: "Error",
                                    description: "No se pudo copiar el enlace",
                                    variant: "destructive",
                                  });
                                }
                              }}
                            >
                              <Share2 className="h-4 w-4 mr-1" />
                              Compartir
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="relative group">
                          <img
                            src={generatedStabilityImage}
                            alt="Imagen generada"
                            className="w-full rounded-lg shadow-lg"
                          />
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors rounded-lg"></div>
                        </div>

                        {/* Información de la imagen */}
                        <div className="mt-4 space-y-2">
                          <div className="flex items-start gap-2">
                            <Badge variant="secondary" className="mt-0.5">Prompt</Badge>
                            <p className="text-sm text-gray-600 flex-1">{stabilityPrompt}</p>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-gray-400">
                            <span>Modelo: {AI_MODELS.find(m => m.value === selectedModel)?.label}</span>
                            <span>• Estilo: {STYLE_PRESETS.find(s => s.value === stylePreset)?.label}</span>
                            <span>• Formato: {outputFormat.toUpperCase()}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Estado vacío */}
                  {!generatedStabilityImage && !generationState.isGenerating && (
                    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                      <CardContent className="flex flex-col items-center justify-center py-12">
                        <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-4">
                          <ImageIcon className="h-12 w-12 text-purple-600" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                          ¡Crea tu primera imagen!
                        </h3>
                        <p className="text-gray-600 text-center max-w-md">
                          Describe la imagen que quieres crear en el panel de control y presiona "Generar Imagen"
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                <TabsContent value="saved" className="space-y-6">
                  {savedStabilityImages.length === 0 ? (
                    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                      <CardContent className="flex flex-col items-center justify-center py-12">
                        <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-4">
                          <Heart className="h-12 w-12 text-purple-600" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                          No hay imágenes guardadas
                        </h3>
                        <p className="text-gray-600 text-center max-w-md">
                          Las imágenes que marques como favoritas aparecerán aquí
                        </p>
                      </CardContent>
                    </Card>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {savedStabilityImages.map((savedImage) => (
                        <Card key={savedImage.id} className="shadow-lg border-0 bg-white/80 backdrop-blur-sm overflow-hidden">
                          <div className="relative group">
                            <img
                              src={savedImage.image_url}
                              alt="Imagen guardada"
                              className="w-full h-48 object-cover"
                            />
                            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors"></div>
                          </div>
                          <CardContent className="p-4">
                            <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                              {savedImage.prompt}
                            </p>
                            <div className="flex items-center gap-1 text-xs text-gray-400 mb-3">
                              <span>{savedImage.model}</span>
                              <span>•</span>
                              <span>{savedImage.stylePreset}</span>
                            </div>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={async () => {
                                  try {
                                    const response = await fetch(savedImage.image_url);
                                    const blob = await response.blob();
                                    const url = window.URL.createObjectURL(blob);
                                    const a = document.createElement("a");
                                    a.href = url;
                                    a.download = `imagen-${Date.now()}.${savedImage.metadata?.outputFormat || 'png'}`;
                                    document.body.appendChild(a);
                                    a.click();
                                    window.URL.revokeObjectURL(url);
                                    document.body.removeChild(a);

                                    toast({
                                      title: "Descarga iniciada",
                                      description: "La imagen se está descargando",
                                    });
                                  } catch (error) {
                                    toast({
                                      title: "Error",
                                      description: "No se pudo descargar la imagen",
                                      variant: "destructive",
                                    });
                                  }
                                }}
                                className="flex-1"
                              >
                                <Download className="h-3 w-3 mr-1" />
                                Descargar
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={async () => {
                                  try {
                                    await navigator.clipboard.writeText(savedImage.image_url);
                                    toast({
                                      title: "Enlace copiado",
                                      description: "El enlace de la imagen se copió al portapapeles",
                                    });
                                  } catch (error) {
                                    toast({
                                      title: "Error",
                                      description: "No se pudo copiar el enlace",
                                      variant: "destructive",
                                    });
                                  }
                                }}
                                className="flex-1"
                              >
                                <Share2 className="h-3 w-3 mr-1" />
                                Compartir
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </motion.div>
          </div>
        )}

        {/* Modo Ideogram - Generación con Texto */}
        {currentMode === "openai" && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Panel de Control */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="lg:col-span-1"
            >
            <Card className="sticky top-6 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-xl">
                  <Settings className="h-5 w-5 text-purple-600" />
                  Panel de Control
                </CardTitle>
                <CardDescription>
                  Crea imágenes con total libertad creativa usando Ideogram AI
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="generate" className="w-full">
                  <TabsList className="grid w-full grid-cols-3 mb-6">
                    <TabsTrigger value="generate" className="text-xs">
                      <Wand2 className="h-3 w-3 mr-1" />
                      Generar
                    </TabsTrigger>
                    <TabsTrigger value="references" className="text-xs">
                      <ImageIcon className="h-3 w-3 mr-1" />
                      Referencias
                    </TabsTrigger>
                    <TabsTrigger value="edit" className="text-xs">
                      <Edit3 className="h-3 w-3 mr-1" />
                      Editar
                    </TabsTrigger>
                  </TabsList>

                  {/* Tab: Generar */}
                  <TabsContent value="generate" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Descripción de la Imagen</Label>
                      <Textarea
                        placeholder="Describe la imagen que quieres crear..."
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                    </div>

                    {/* Selector de Modo: Resolución vs Aspect Ratio */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Configuración de Tamaño</Label>
                      <div className="flex gap-2 mb-3">
                        <Button
                          variant={!useAspectRatio ? "default" : "outline"}
                          size="sm"
                          onClick={() => setUseAspectRatio(false)}
                          className="flex-1"
                        >
                          Resolución Específica
                        </Button>
                        <Button
                          variant={useAspectRatio ? "default" : "outline"}
                          size="sm"
                          onClick={() => setUseAspectRatio(true)}
                          className="flex-1"
                        >
                          Relación de Aspecto
                        </Button>
                      </div>

                      {!useAspectRatio ? (
                        // Selector de Resolución
                        <div className="space-y-2">
                          <Label className="text-xs text-gray-600">
                            Resolución: {resolution}
                          </Label>
                          <div className="max-h-48 overflow-y-auto space-y-1">
                            {["square", "portrait", "landscape"].map((category) => (
                              <div key={category}>
                                <div className="text-xs font-medium text-gray-500 mb-1 capitalize">
                                  {category === "square" ? "Cuadrado" :
                                   category === "portrait" ? "Vertical" : "Horizontal"}
                                </div>
                                {resolutionOptions
                                  .filter(opt => opt.category === category)
                                  .slice(0, category === "square" ? 1 : 3)
                                  .map((option) => (
                                    <Button
                                      key={option.value}
                                      variant={resolution === option.value ? "default" : "outline"}
                                      size="sm"
                                      onClick={() => setResolution(option.value)}
                                      className={`w-full justify-start text-xs h-8 ${
                                        resolution === option.value
                                          ? "bg-purple-600 hover:bg-purple-700 text-white"
                                          : "hover:bg-purple-50"
                                      }`}
                                    >
                                      <div className="text-left">
                                        <div className="font-medium">{option.label}</div>
                                        <div className="text-xs opacity-75">{option.value} ({option.ratio})</div>
                                      </div>
                                    </Button>
                                  ))}
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : (
                        // Selector de Aspect Ratio
                        <div className="space-y-2">
                          <Label className="text-xs text-gray-600">
                            Relación de Aspecto: {aspectRatio || "No seleccionado"}
                          </Label>
                          <div className="grid grid-cols-2 gap-1 max-h-48 overflow-y-auto">
                            {aspectRatioOptions.map((option) => (
                              <Button
                                key={option.value}
                                variant={aspectRatio === option.value ? "default" : "outline"}
                                size="sm"
                                onClick={() => setAspectRatio(option.value)}
                                className={`justify-start text-xs h-12 ${
                                  aspectRatio === option.value
                                    ? "bg-purple-600 hover:bg-purple-700 text-white"
                                    : "hover:bg-purple-50"
                                }`}
                              >
                                <span className="mr-1 text-sm">{option.icon}</span>
                                <div className="text-left">
                                  <div className="font-medium text-xs">{option.label.split(' (')[0]}</div>
                                  <div className="text-xs opacity-75">
                                    {option.label.match(/\(([^)]+)\)/)?.[1]}
                                  </div>
                                </div>
                              </Button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {/* Velocidad de Renderizado */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Velocidad de Renderizado</Label>
                      <div className="grid grid-cols-3 gap-2">
                        {speedOptions.map((option) => (
                          <Button
                            key={option.value}
                            variant={renderingSpeed === option.value ? "default" : "outline"}
                            size="sm"
                            onClick={() => setRenderingSpeed(option.value as "TURBO" | "DEFAULT" | "QUALITY")}
                            className={`flex-col h-16 p-2 ${
                              renderingSpeed === option.value
                                ? "bg-purple-600 hover:bg-purple-700 text-white shadow-md"
                                : "hover:bg-purple-50 hover:border-purple-300"
                            }`}
                          >
                            <span className="text-lg mb-1">{option.icon}</span>
                            <div className="text-xs font-medium">{option.label}</div>
                          </Button>
                        ))}
                      </div>
                      <div className="text-xs text-gray-500">
                        {speedOptions.find(opt => opt.value === renderingSpeed)?.description}
                      </div>
                    </div>

                    {/* Estilo Visual */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Estilo Visual</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {styleOptions.map((option) => (
                          <Button
                            key={option.value}
                            variant={styleType === option.value ? "default" : "outline"}
                            size="sm"
                            onClick={() => setStyleType(option.value as "AUTO" | "GENERAL" | "REALISTIC" | "DESIGN")}
                            className={`flex-col h-16 p-2 ${
                              styleType === option.value
                                ? "bg-purple-600 hover:bg-purple-700 text-white shadow-md"
                                : "hover:bg-purple-50 hover:border-purple-300"
                            }`}
                          >
                            <span className="text-lg mb-1">{option.icon}</span>
                            <div className="text-xs font-medium">{option.label}</div>
                          </Button>
                        ))}
                      </div>
                      <div className="text-xs text-gray-500">
                        {styleOptions.find(opt => opt.value === styleType)?.description}
                      </div>
                    </div>

                    {/* Número de Imágenes */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Número de Imágenes (1-8)</Label>
                      <div className="flex items-center space-x-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setNumImages(Math.max(1, numImages - 1))}
                          disabled={numImages <= 1}
                          className="h-8 w-8 p-0"
                        >
                          -
                        </Button>
                        <span className="text-lg font-medium w-8 text-center">{numImages}</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setNumImages(Math.min(8, numImages + 1))}
                          disabled={numImages >= 8}
                          className="h-8 w-8 p-0"
                        >
                          +
                        </Button>
                      </div>
                    </div>

                    {/* Prompt Negativo */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Prompt Negativo (Opcional)</Label>
                      <Textarea
                        placeholder="Describe qué NO quieres ver en la imagen..."
                        value={negativePrompt}
                        onChange={(e) => setNegativePrompt(e.target.value)}
                        className="min-h-[60px] resize-none"
                      />
                    </div>

                    {/* Configuración Avanzada */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Configuración Avanzada</Label>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Label className="text-xs text-gray-600">Magic Prompt</Label>
                          <div className="flex gap-1">
                            {["AUTO", "ON", "OFF"].map((option) => (
                              <Button
                                key={option}
                                variant={magicPrompt === option ? "default" : "outline"}
                                size="sm"
                                onClick={() => setMagicPrompt(option as "AUTO" | "ON" | "OFF")}
                                className={`px-3 py-1 text-xs ${
                                  magicPrompt === option
                                    ? "bg-purple-600 hover:bg-purple-700 text-white"
                                    : "hover:bg-purple-50"
                                }`}
                              >
                                {option}
                              </Button>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>

                    <Button
                      onClick={handleGenerate}
                      disabled={isGenerating || !prompt.trim()}
                      className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                    >
                      {isGenerating ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Generando...
                        </>
                      ) : (
                        <>
                          <Wand2 className="h-4 w-4 mr-2" />
                          Generar Imagen
                        </>
                      )}
                    </Button>

                    {/* Prompts de ejemplo */}
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-600">Ejemplos:</Label>
                      <div className="space-y-1">
                        {examplePrompts.slice(0, 3).map((example, index) => (
                          <Button
                            key={index}
                            variant="ghost"
                            size="sm"
                            onClick={() => setPrompt(example)}
                            className="w-full text-left text-xs h-auto p-2 justify-start"
                          >
                            {example}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </TabsContent>

                  {/* Tab: Referencias */}
                  <TabsContent value="references" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Descripción de la Imagen</Label>
                      <Textarea
                        placeholder="Describe la imagen que quieres crear basada en las referencias... ¡Sin restricciones!"
                        value={referencePrompt}
                        onChange={(e) => setReferencePrompt(e.target.value)}
                        className="min-h-[80px] resize-none"
                      />
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Imágenes de Referencia (máx. 4)</Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                        <input
                          ref={fileInputRef}
                          type="file"
                          multiple
                          accept="image/*"
                          onChange={(e) => handleReferenceFiles(e.target.files)}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          onClick={() => fileInputRef.current?.click()}
                          className="mb-2"
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Subir Imágenes
                        </Button>
                        <p className="text-xs text-gray-500">
                          PNG, JPG, WebP hasta 10MB cada una
                        </p>
                      </div>

                      {/* Mostrar imágenes de referencia */}
                      {referenceImages.length > 0 && (
                        <div className="grid grid-cols-2 gap-2">
                          {referenceImages.map((file, index) => (
                            <div key={index} className="relative group">
                              <img
                                src={URL.createObjectURL(file)}
                                alt={`Referencia ${index + 1}`}
                                className="w-full h-20 object-cover rounded-lg"
                              />
                              <Button
                                variant="destructive"
                                size="sm"
                                className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => {
                                  setReferenceImages(prev => prev.filter((_, i) => i !== index));
                                }}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Configuración de Tamaño para Referencias */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Configuración de Tamaño</Label>
                      <div className="flex gap-2 mb-3">
                        <Button
                          variant={!useAspectRatio ? "default" : "outline"}
                          size="sm"
                          onClick={() => setUseAspectRatio(false)}
                          className="flex-1"
                        >
                          Resolución
                        </Button>
                        <Button
                          variant={useAspectRatio ? "default" : "outline"}
                          size="sm"
                          onClick={() => setUseAspectRatio(true)}
                          className="flex-1"
                        >
                          Aspecto
                        </Button>
                      </div>

                      {!useAspectRatio ? (
                        <div className="space-y-2">
                          <Label className="text-xs text-gray-600">
                            Resolución: {resolution}
                          </Label>
                          <div className="grid grid-cols-1 gap-1 max-h-32 overflow-y-auto">
                            {resolutionOptions.slice(0, 6).map((option) => (
                              <Button
                                key={option.value}
                                variant={resolution === option.value ? "default" : "outline"}
                                size="sm"
                                onClick={() => setResolution(option.value)}
                                className={`justify-start text-xs h-8 ${
                                  resolution === option.value
                                    ? "bg-purple-600 hover:bg-purple-700 text-white"
                                    : "hover:bg-purple-50"
                                }`}
                              >
                                <div className="text-left">
                                  <div className="font-medium">{option.label}</div>
                                  <div className="text-xs opacity-75">{option.value}</div>
                                </div>
                              </Button>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          <Label className="text-xs text-gray-600">
                            Aspecto: {aspectRatio || "No seleccionado"}
                          </Label>
                          <div className="grid grid-cols-2 gap-1 max-h-32 overflow-y-auto">
                            {aspectRatioOptions.slice(0, 8).map((option) => (
                              <Button
                                key={option.value}
                                variant={aspectRatio === option.value ? "default" : "outline"}
                                size="sm"
                                onClick={() => setAspectRatio(option.value)}
                                className={`justify-start text-xs h-10 ${
                                  aspectRatio === option.value
                                    ? "bg-purple-600 hover:bg-purple-700 text-white"
                                    : "hover:bg-purple-50"
                                }`}
                              >
                                <span className="mr-1 text-sm">{option.icon}</span>
                                <div className="text-left">
                                  <div className="font-medium text-xs">{option.label.split(' (')[0]}</div>
                                </div>
                              </Button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    <Button
                      onClick={handleGenerateWithReferences}
                      disabled={isGenerating || !referencePrompt.trim() || referenceImages.length === 0}
                      className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                    >
                      {isGenerating ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Generando...
                        </>
                      ) : (
                        <>
                          <ImageIcon className="h-4 w-4 mr-2" />
                          Generar con Referencias
                        </>
                      )}
                    </Button>
                  </TabsContent>

                  {/* Tab: Editar */}
                  <TabsContent value="edit" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Cargar Imagen para Editar</Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                        <input
                          ref={imageInputRef}
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              const validation = validateImageFile(file);
                              if (validation.isValid) {
                                handleLoadImageForEdit(file);
                              } else {
                                toast({
                                  title: "Archivo inválido",
                                  description: validation.error,
                                  variant: "destructive",
                                });
                              }
                            }
                          }}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          onClick={() => imageInputRef.current?.click()}
                          className="mb-2"
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Cargar Imagen
                        </Button>
                        <p className="text-xs text-gray-500">
                          PNG, JPG, WebP hasta 10MB
                        </p>
                      </div>
                    </div>

                    {editingImage && (
                      <>
                        <div className="space-y-3">
                          <Label className="text-sm font-medium">Qué quieres cambiar</Label>
                          <Textarea
                            placeholder="Describe qué quieres cambiar en las áreas marcadas..."
                            value={maskPrompt}
                            onChange={(e) => setMaskPrompt(e.target.value)}
                            className="min-h-[80px] resize-none"
                          />
                        </div>

                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium">Herramientas de Edición</Label>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setShowMask(!showMask)}
                              >
                                {showMask ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={clearMask}
                              >
                                <RotateCcw className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label className="text-xs text-gray-600">Tamaño del pincel: {brushSize}px</Label>
                            <Slider
                              value={[brushSize]}
                              onValueChange={(value) => setBrushSize(value[0])}
                              max={50}
                              min={5}
                              step={5}
                              className="w-full"
                            />
                          </div>
                        </div>

                        <Button
                          onClick={handleApplyMaskEdit}
                          disabled={isGenerating || !maskPrompt.trim()}
                          className="w-full bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700"
                        >
                          {isGenerating ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              Aplicando...
                            </>
                          ) : (
                            <>
                              <Edit3 className="h-4 w-4 mr-2" />
                              Aplicar Cambios
                            </>
                          )}
                        </Button>
                      </>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
            </motion.div>

            {/* Área de Visualización */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="lg:col-span-2"
            >
              <Tabs value={mainTab} onValueChange={(value) => setMainTab(value as "latest" | "saved")} className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-6">
                  <TabsTrigger value="latest" className="flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    Última Generación
                  </TabsTrigger>
                  <TabsTrigger value="saved" className="flex items-center gap-2">
                    <Heart className="h-4 w-4" />
                    Guardados ({savedImages.length})
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="latest" className="space-y-6">
                  {/* Imagen generada */}
                  {currentImage && (
                    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                      <CardHeader className="pb-4">
                        <div className="flex items-center justify-between">
                          <CardTitle className="flex items-center gap-2">
                            <Type className="h-5 w-5 text-purple-600" />
                            Imagen Generada
                          </CardTitle>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleToggleFavorite}
                              className={currentImageSaved ? "text-red-600 border-red-200" : ""}
                            >
                              {currentImageSaved ? (
                                <Heart className="h-4 w-4 fill-current" />
                              ) : (
                                <HeartOff className="h-4 w-4" />
                              )}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDownload(currentImage.image_url)}
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Descargar
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={async () => {
                                try {
                                  await navigator.clipboard.writeText(currentImage.image_url);
                                  toast({
                                    title: "Enlace copiado",
                                    description: "El enlace de la imagen se copió al portapapeles",
                                  });
                                } catch (error) {
                                  toast({
                                    title: "Error",
                                    description: "No se pudo copiar el enlace",
                                    variant: "destructive",
                                  });
                                }
                              }}
                            >
                              <Share2 className="h-4 w-4 mr-1" />
                              Compartir
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="relative group">
                          <img
                            src={currentImage.image_url}
                            alt="Imagen generada"
                            className="w-full rounded-lg shadow-lg"
                          />
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors rounded-lg"></div>
                        </div>

                        {/* Información de la imagen */}
                        <div className="mt-4 space-y-2">
                          <div className="flex items-start gap-2">
                            <Badge variant="secondary" className="mt-0.5">Prompt</Badge>
                            <p className="text-sm text-gray-600 flex-1">{currentImage.prompt}</p>
                          </div>
                          {currentImage.revised_prompt && (
                            <div className="flex items-start gap-2">
                              <Badge variant="outline" className="mt-0.5">Revisado</Badge>
                              <p className="text-xs text-gray-500 flex-1">{currentImage.revised_prompt}</p>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Editor de Máscara */}
                  {editingImage && (
                    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                      <CardHeader className="pb-4">
                        <CardTitle className="flex items-center gap-2">
                          <Edit3 className="h-5 w-5 text-purple-600" />
                          Editor de Máscara
                        </CardTitle>
                        <CardDescription>
                          Pinta las áreas que quieres cambiar en blanco
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="relative flex justify-center">
                          <div className="relative">
                            <canvas
                              ref={canvasRef}
                              className="rounded-lg border border-gray-200"
                              style={{ zIndex: 1, display: 'block' }}
                            />
                            <canvas
                              ref={maskCanvasRef}
                              className={`absolute inset-0 rounded-lg cursor-crosshair ${
                                showMask ? 'opacity-50' : 'opacity-0'
                              }`}
                              style={{
                                zIndex: 2,
                                mixBlendMode: 'multiply',
                                transition: 'opacity 0.2s ease'
                              }}
                              onMouseDown={startDrawing}
                              onMouseMove={draw}
                              onMouseUp={stopDrawing}
                              onMouseLeave={stopDrawing}
                            />
                          </div>
                        </div>

                        <div className="mt-4 text-xs text-gray-500 space-y-1">
                          <p>• Pinta en blanco las áreas que quieres cambiar</p>
                          <p>• Usa el control deslizante para ajustar el tamaño del pincel</p>
                          <p>• Puedes ocultar/mostrar la máscara con el botón del ojo</p>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Estado vacío */}
                  {!currentImage && !editingImage && (
                    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                      <CardContent className="flex flex-col items-center justify-center py-16">
                        <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-6">
                          <Type className="h-12 w-12 text-purple-600" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                          ¡Crea tu primera imagen!
                        </h3>
                        <p className="text-gray-600 text-center max-w-md mb-6">
                          Describe la imagen que quieres crear, usa imágenes de referencia o carga una imagen para editar.
                        </p>
                        <div className="flex flex-wrap gap-2 justify-center">
                          <Badge variant="secondary">Creativo</Badge>
                          <Badge variant="secondary">Artístico</Badge>
                          <Badge variant="secondary">Realista</Badge>
                          <Badge variant="secondary">Abstracto</Badge>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                <TabsContent value="saved" className="space-y-6">
                  {savedImages.length === 0 ? (
                    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                      <CardContent className="flex flex-col items-center justify-center py-16">
                        <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-6">
                          <Heart className="h-12 w-12 text-purple-600" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                          No tienes imágenes guardadas
                        </h3>
                        <p className="text-gray-600 text-center max-w-md">
                          Las imágenes que marques como favoritas aparecerán aquí para acceso rápido.
                        </p>
                      </CardContent>
                    </Card>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {savedImages.map((savedImage) => (
                        <Card key={savedImage.id} className="shadow-xl border-0 bg-white/80 backdrop-blur-sm overflow-hidden">
                          <div className="relative group">
                            <img
                              src={savedImage.image_url}
                              alt="Imagen guardada"
                              className="w-full h-48 object-cover"
                            />
                            <div className="absolute top-2 right-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  const filteredImages = savedImages.filter(img => img.id !== savedImage.id);
                                  setSavedImages(filteredImages);

                                  toast({
                                    title: "💔 Eliminado",
                                    description: "Imagen eliminada de favoritos.",
                                  });
                                }}
                                className="bg-white/90 hover:bg-white"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>

                          {/* Información */}
                          <div className="p-4">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant="secondary" className="text-xs">
                                OPENAI
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                {new Date(savedImage.timestamp).toLocaleDateString()}
                              </span>
                            </div>

                            <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                              {savedImage.prompt}
                            </p>

                            {/* Botones de acción */}
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDownload(savedImage.image_url, `imagen-${savedImage.id}.png`)}
                                className="flex-1"
                              >
                                <Download className="h-3 w-3 mr-1" />
                                Descargar
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={async () => {
                                  try {
                                    await navigator.clipboard.writeText(savedImage.image_url);
                                    toast({
                                      title: "Enlace copiado",
                                      description: "El enlace de la imagen se copió al portapapeles",
                                    });
                                  } catch (error) {
                                    toast({
                                      title: "Error",
                                      description: "No se pudo copiar el enlace",
                                      variant: "destructive",
                                    });
                                  }
                                }}
                                className="flex-1"
                              >
                                <Share2 className="h-3 w-3 mr-1" />
                                Compartir
                              </Button>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </motion.div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}

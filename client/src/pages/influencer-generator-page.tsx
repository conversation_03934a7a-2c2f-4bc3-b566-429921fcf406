import React, { useState } from "react";
import { motion } from "framer-motion";
import { useLocation } from "wouter";
import {
  Users,
  MessageSquare,
  ShoppingBag,
  Sparkles,
  ArrowRight,
  Upload,
  Heart,
  Star,
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { DashboardLayout } from "@/components/layout/dashboard-layout";

// Importar imagen placeholder para influencers
import adCreador from "@/assets/ad-creator.png";

interface InfluencerTool {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  linkTo: string;
  isPremium?: boolean;
  previewImage?: string;
  gradient: string;
  category: string;
}

const influencerTools: InfluencerTool[] = [
  {
    id: "testimonials",
    title: "Crear Testimonios",
    description: "Genera testimonios auténticos con influencers virtuales. <PERSON>rea reseñas creíbles y contenido de confianza",
    category: "Testimonios",
    icon: <MessageSquare className="w-6 h-6" />,
    linkTo: "/dashboard/influencer-generator/testimonials",
    gradient: "from-purple-500 to-blue-600",
  },
  {
    id: "product-placement",
    title: "Product Placement",
    description: "Integra productos naturalmente en contenido de influencers. Posicionamiento estratégico y orgánico",
    category: "Placement",
    icon: <ShoppingBag className="w-6 h-6" />,
    linkTo: "/dashboard/influencer-generator/product-placement",
    previewImage: adCreador,
    gradient: "from-emerald-500 to-teal-600",
  },
];

const categories = [
  { id: "all", name: "Todas", icon: <Sparkles className="w-5 h-5" /> },
  { id: "Testimonios", name: "Testimonios", icon: <MessageSquare className="w-5 h-5" /> },
  { id: "Placement", name: "Product Placement", icon: <ShoppingBag className="w-5 h-5" /> },
];

function InfluencerGeneratorContent() {
  const [, navigate] = useLocation();

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative min-h-[480px] flex flex-col gap-6 bg-cover bg-center bg-no-repeat items-center justify-center p-8 rounded-xl mx-4 mb-8"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%), url(${adCreador})`
        }}
      >
        <div className="flex flex-col gap-4 text-center max-w-4xl">
          <h1 className="text-white text-4xl md:text-5xl font-black leading-tight tracking-tight">
            Crear un Influencer
          </h1>
          <h2 className="text-white/90 text-base md:text-lg font-normal leading-relaxed max-w-2xl mx-auto">
            Emma Influencer Generator te permite crear influencers virtuales auténticos para testimonios y product placement.
            Mantén la coherencia de marca en todo tu contenido.
          </h2>
        </div>
        <Button
          onClick={() => navigate("/dashboard/influencer-generator/testimonials")}
          size="lg"
          className="bg-white text-[#3018ef] hover:bg-white/90 font-bold px-8 py-3 text-base"
        >
          Crear Testimonio
        </Button>
      </motion.div>

      {/* Choose Influencer Type Section */}
      <div className="px-4 md:px-8 lg:px-16 max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <h2 className="text-[#0e141b] text-2xl font-bold leading-tight tracking-tight px-4 pb-6 pt-8">
            Elige tu tipo de contenido
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 px-4 mb-12">
            <motion.div
              whileHover={{ scale: 1.02 }}
              className="flex gap-4 rounded-lg border border-slate-200 bg-white p-6 items-center cursor-pointer hover:shadow-md transition-all"
              onClick={() => navigate("/dashboard/influencer-generator/testimonials")}
            >
              <div className="p-3 bg-gradient-to-r from-purple-500 to-blue-600 rounded-lg">
                <MessageSquare className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-[#0e141b] text-lg font-bold leading-tight">Crear Testimonios</h3>
                <p className="text-slate-600 text-sm">Genera testimonios auténticos y creíbles</p>
              </div>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02 }}
              className="flex gap-4 rounded-lg border border-slate-200 bg-white p-6 items-center cursor-pointer hover:shadow-md transition-all"
              onClick={() => navigate("/dashboard/influencer-generator/product-placement")}
            >
              <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg">
                <ShoppingBag className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-[#0e141b] text-lg font-bold leading-tight">Product Placement</h3>
                <p className="text-slate-600 text-sm">Integra productos de forma natural</p>
              </div>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02 }}
              className="flex gap-4 rounded-lg border border-slate-200 bg-white p-6 items-center cursor-pointer hover:shadow-md transition-all"
              onClick={() => document.getElementById('influencer-upload')?.click()}
            >
              <div className="p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg">
                <Upload className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-[#0e141b] text-lg font-bold leading-tight">Subir Referencia</h3>
                <p className="text-slate-600 text-sm">Sube imágenes de referencia de influencers</p>
                <input
                  type="file"
                  accept=".png,.jpg,.jpeg,.webp"
                  className="hidden"
                  id="influencer-upload"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      console.log("Archivo seleccionado:", file.name);
                      // Aquí iría la lógica para subir el archivo
                    }
                  }}
                />
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Browse Created Influencers Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <h2 className="text-[#0e141b] text-2xl font-bold leading-tight tracking-tight px-4 pb-6">
            Todos los influencers creados
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 px-4">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05 }}
                className="flex flex-col gap-3 cursor-pointer"
                onClick={() => navigate("/dashboard/influencer-generator/gallery")}
              >
                <div className="w-full bg-gradient-to-br from-slate-200 to-slate-300 aspect-square bg-cover rounded-xl flex items-center justify-center hover:shadow-lg transition-all">
                  <div className="text-slate-500 text-center">
                    <Users className="w-8 h-8 mx-auto mb-2" />
                    <p className="text-xs">Influencer {index}</p>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-slate-700">Influencer {index}</span>
                  <div className="flex items-center gap-1">
                    <Heart className="w-4 h-4 text-red-500" />
                    <span className="text-xs text-slate-500">{Math.floor(Math.random() * 100)}</span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="text-center py-16"
        >
          <h3 className="text-2xl font-bold text-[#0e141b] mb-4">
            ¿Listo para crear tu primer influencer?
          </h3>
          <p className="text-slate-600 mb-8 max-w-2xl mx-auto">
            Únete a miles de marcas que ya están creando contenido auténtico con influencers virtuales
          </p>
          <Button
            onClick={() => navigate("/dashboard/influencer-generator/testimonials")}
            size="lg"
            className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:opacity-90 font-bold px-8 py-3"
          >
            Crear Mi Primer Influencer
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
        </motion.div>
      </div>
    </div>
  );
}

function InfluencerGeneratorPage() {
  return (
    <DashboardLayout pageTitle="Generación de Influencers">
      <InfluencerGeneratorContent />
    </DashboardLayout>
  );
}

export default InfluencerGeneratorPage;

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Upload, Package, Sparkles, Download, Eye, Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import DashboardLayout from '@/components/layout/dashboard-layout';
import {
  getMockupContexts,
  generateMockup,
  getContextSuggestions,
  validateImageFile,
  createImagePreview,
  cleanupImagePreview,
  downloadMockup,
  type MockupContext,
  type EnhancedMockupContext,
  type MockupGenerationResult,
  type MockupVariation,
  type ContextSuggestionsResponse
} from '@/services/mockup-service';

// Types are now imported from the service

const MockupGeneratorPage: React.FC = () => {
  const [productImage, setProductImage] = useState<File | null>(null);
  const [productDescription, setProductDescription] = useState('');
  const [selectedContext, setSelectedContext] = useState<string>('');
  const [availableContexts, setAvailableContexts] = useState<Record<string, MockupContext>>({});
  const [suggestedContexts, setSuggestedContexts] = useState<Record<string, EnhancedMockupContext>>({});
  const [suggestions, setSuggestions] = useState<ContextSuggestionsResponse | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [generatedMockup, setGeneratedMockup] = useState<MockupGenerationResult | null>(null);
  const [selectedVariation, setSelectedVariation] = useState<number>(0);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const { toast } = useToast();

  // Predefined contexts (fallback if API fails)
  const defaultContexts: Record<string, MockupContext> = {
    hands: {
      name: 'En Manos de Usuario',
      description: 'Producto siendo usado por una persona'
    },
    desk: {
      name: 'En Escritorio',
      description: 'Producto en un ambiente de trabajo profesional'
    },
    lifestyle: {
      name: 'Estilo de Vida',
      description: 'Producto en contexto de uso cotidiano'
    },
    outdoor: {
      name: 'Ambiente Exterior',
      description: 'Producto en contexto al aire libre'
    },
    studio: {
      name: 'Estudio Profesional',
      description: 'Producto en estudio con iluminación profesional'
    },
    social: {
      name: 'Contexto Social',
      description: 'Producto siendo compartido o usado en grupo'
    }
  };

  React.useEffect(() => {
    // Load available contexts from API
    loadContexts();
  }, []);

  // Cleanup preview URL on unmount
  React.useEffect(() => {
    return () => {
      if (previewUrl) {
        cleanupImagePreview(previewUrl);
      }
    };
  }, [previewUrl]);

  // Load suggestions when product description changes
  React.useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (productDescription.trim().length > 10) {
        loadSuggestions(productDescription);
      }
    }, 1000); // Debounce for 1 second

    return () => clearTimeout(timeoutId);
  }, [productDescription]);

  const loadContexts = async () => {
    try {
      const result = await getMockupContexts();
      if (result.success && result.contexts) {
        setAvailableContexts(result.contexts);
      } else {
        setAvailableContexts(defaultContexts);
      }
    } catch (error) {
      console.error('Error loading contexts:', error);
      setAvailableContexts(defaultContexts);
    }
  };

  const loadSuggestions = async (description: string) => {
    if (!description.trim()) {
      setSuggestedContexts({});
      setSuggestions(null);
      return;
    }

    setIsLoadingSuggestions(true);
    try {
      const result = await getContextSuggestions(description);
      if (result.success) {
        setSuggestedContexts(result.suggested_contexts || {});
        setSuggestions(result);

        // Auto-select top recommendation if available
        if (result.analysis?.top_recommendation && !selectedContext) {
          setSelectedContext(result.analysis.top_recommendation);
        }
      }
    } catch (error) {
      console.error('Error loading suggestions:', error);
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const validation = validateImageFile(file);
      if (validation.valid) {
        // Clean up previous preview
        if (previewUrl) {
          cleanupImagePreview(previewUrl);
        }

        setProductImage(file);
        // Create preview URL
        const url = createImagePreview(file);
        setPreviewUrl(url);
      } else {
        toast({
          title: "Error",
          description: validation.error || "Archivo de imagen inválido",
          variant: "destructive"
        });
      }
    }
  };

  const handleGenerateMockup = async () => {
    if (!productImage) {
      toast({
        title: "Error",
        description: "Por favor selecciona una imagen de tu producto",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    setGeneratedMockup(null);

    try {
      // Si no hay contexto seleccionado, usar el mejor sugerido o 'lifestyle' por defecto
      const contextToUse = selectedContext ||
                          (suggestions?.analysis?.top_recommendation) ||
                          'lifestyle';

      const result = await generateMockup({
        productImage,
        context: contextToUse,
        productDescription,
        size: '1024x1024',
        variations: 4
      });

      if (result.success && result.variations && result.variations.length > 0) {
        setGeneratedMockup(result);
        setSelectedVariation(0); // Select first variation by default
        toast({
          title: "¡Mockups generados!",
          description: `${result.total_generated} variaciones profesionales creadas con tecnología Emma AI`,
        });
      } else {
        throw new Error(result.error || 'Error generando mockup');
      }
    } catch (error) {
      console.error('Error generating mockup:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error generando mockup",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownloadMockup = (variationIndex?: number) => {
    const variation = generatedMockup?.variations?.[variationIndex ?? selectedVariation];
    if (variation?.image_url) {
      const filename = `mockup-${selectedContext}-variation-${(variationIndex ?? selectedVariation) + 1}-${Date.now()}.png`;
      downloadMockup(variation.image_url, filename);
    }
  };

  const handleDownloadAll = () => {
    if (generatedMockup?.variations) {
      generatedMockup.variations.forEach((variation, index) => {
        if (variation.image_url) {
          setTimeout(() => {
            const filename = `mockup-${selectedContext}-variation-${index + 1}-${Date.now()}.png`;
            downloadMockup(variation.image_url!, filename);
          }, index * 500); // Stagger downloads
        }
      });
    }
  };

  return (
    <DashboardLayout pageTitle="Generador de Mockups">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <Package className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Generador de Mockups</h1>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Coloca tu producto en contextos realistas usando nuestra tecnología exclusiva de IA avanzada.
            Algoritmos propietarios entrenados con millones de imágenes profesionales para resultados de calidad comercial.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Panel - Configuration */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            {/* Image Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="h-5 w-5" />
                  Imagen del Producto
                </CardTitle>
                <CardDescription>
                  Sube una imagen clara de tu producto con fondo transparente o limpio
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                      id="product-image"
                    />
                    <label htmlFor="product-image" className="cursor-pointer">
                      {previewUrl ? (
                        <div className="space-y-2">
                          <img
                            src={previewUrl}
                            alt="Preview"
                            className="max-h-32 mx-auto rounded-lg"
                          />
                          <p className="text-sm text-gray-600">Haz clic para cambiar imagen</p>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          <Upload className="h-12 w-12 mx-auto text-gray-400" />
                          <p className="text-gray-600">Haz clic para subir imagen</p>
                          <p className="text-sm text-gray-500">PNG, JPG, WEBP hasta 10MB</p>
                        </div>
                      )}
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Product Description */}
            <Card>
              <CardHeader>
                <CardTitle>Descripción del Producto</CardTitle>
                <CardDescription>
                  Describe tu producto y opcionalmente el contexto donde quieres que aparezca
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  placeholder="Ej: Smartphone moderno en manos de una persona joven, auriculares premium en un escritorio minimalista, botella de agua deportiva en un gimnasio..."
                  value={productDescription}
                  onChange={(e) => setProductDescription(e.target.value)}
                  className="min-h-[100px]"
                />
                <p className="text-xs text-gray-500 mt-2">
                  💡 Puedes incluir el contexto aquí (ej: "en manos", "en escritorio", "en gimnasio") y Emma AI lo entenderá
                </p>
              </CardContent>
            </Card>
ok 
            {/* Context Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  Ideas de Contexto (Opcional)
                  {isLoadingSuggestions && <Loader2 className="h-4 w-4 animate-spin" />}
                </CardTitle>
                <CardDescription>
                  Si ya describiste el contexto arriba, puedes omitir esto. Estas son solo ideas adicionales para inspirarte
                  {suggestions?.reasoning && (
                    <div className="mt-2 p-2 bg-blue-50 rounded text-sm text-blue-700">
                      🧠 <strong>Emma AI sugiere:</strong> {suggestions.reasoning}
                    </div>
                  )}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Suggested contexts first */}
                {Object.keys(suggestedContexts).length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                      <Sparkles className="h-4 w-4 text-yellow-500" />
                      Ideas perfectas para tu producto
                    </h4>
                    <div className="grid grid-cols-1 gap-2">
                      {Object.entries(suggestedContexts).map(([id, context]) => (
                        <div
                          key={id}
                          className={`p-3 border rounded-lg cursor-pointer transition-all ${
                            selectedContext === id
                              ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                              : context.recommended
                              ? 'border-green-300 bg-green-50 hover:border-green-400'
                              : 'border-yellow-300 bg-yellow-50 hover:border-yellow-400'
                          }`}
                          onClick={() => setSelectedContext(id)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <h4 className="font-medium">{context.name}</h4>
                                <div className="flex items-center gap-1">
                                  <div className={`w-2 h-2 rounded-full ${
                                    context.confidence_score >= 0.9 ? 'bg-green-500' :
                                    context.confidence_score >= 0.8 ? 'bg-yellow-500' : 'bg-gray-400'
                                  }`} />
                                  <span className="text-xs text-gray-500">
                                    {Math.round(context.confidence_score * 100)}%
                                  </span>
                                  {context.recommended && (
                                    <span className="text-xs bg-green-100 text-green-700 px-1 rounded">
                                      TOP
                                    </span>
                                  )}
                                </div>
                              </div>
                              <p className="text-sm text-gray-600">{context.description}</p>
                            </div>
                            <div className={`w-4 h-4 rounded-full border-2 ${
                              selectedContext === id
                                ? 'border-blue-500 bg-blue-500'
                                : 'border-gray-300'
                            }`} />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* All contexts */}
                <div>
                  {Object.keys(suggestedContexts).length > 0 && (
                    <h4 className="text-sm font-medium text-gray-700 mb-2">
                      Más ideas de contexto
                    </h4>
                  )}
                  <div className="grid grid-cols-1 gap-2">
                    {Object.entries(availableContexts)
                      .filter(([id]) => !suggestedContexts[id]) // Don't show already suggested ones
                      .map(([id, context]) => (
                        <div
                          key={id}
                          className={`p-3 border rounded-lg cursor-pointer transition-all ${
                            selectedContext === id
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => setSelectedContext(id)}
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium">{context.name}</h4>
                              <p className="text-sm text-gray-600">{context.description}</p>
                            </div>
                            <div className={`w-4 h-4 rounded-full border-2 ${
                              selectedContext === id
                                ? 'border-blue-500 bg-blue-500'
                                : 'border-gray-300'
                            }`} />
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Generate Button */}
            <div className="space-y-3">
              <Button
                onClick={handleGenerateMockup}
                disabled={!productImage || isGenerating}
                className="w-full h-12 text-lg"
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Generando Mockup...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-5 w-5" />
                    Generar Mockup
                  </>
                )}
              </Button>

              {!selectedContext && (
                <p className="text-xs text-gray-500 text-center">
                  💡 Sin contexto seleccionado, Emma AI elegirá el mejor basado en tu descripción
                </p>
              )}
            </div>
          </motion.div>

          {/* Right Panel - Result */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Resultado
                </CardTitle>
                <CardDescription>
                  Tu mockup generado aparecerá aquí
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1">
                {isGenerating ? (
                  <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
                    <div className="text-center">
                      <Loader2 className="h-12 w-12 animate-spin mx-auto text-blue-600 mb-4" />
                      <p className="text-gray-600">Procesando con tecnología Emma AI...</p>
                      <p className="text-sm text-gray-500 mt-2">Nuestros algoritmos exclusivos están creando variaciones profesionales</p>
                    </div>
                  </div>
                ) : generatedMockup?.variations && generatedMockup.variations.length > 0 ? (
                  <div className="space-y-4">
                    {/* Main selected variation */}
                    <div className="relative">
                      <img
                        src={generatedMockup.variations[selectedVariation]?.image_url}
                        alt={`Mockup Variation ${selectedVariation + 1}`}
                        className="w-full rounded-lg shadow-lg"
                      />
                      <div className="absolute top-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-sm">
                        {selectedVariation + 1} de {generatedMockup.variations.length}
                      </div>
                    </div>

                    {/* Variation thumbnails */}
                    {generatedMockup.variations.length > 1 && (
                      <div className="grid grid-cols-4 gap-2">
                        {generatedMockup.variations.map((variation, index) => (
                          <div
                            key={index}
                            className={`relative cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                              selectedVariation === index
                                ? 'border-blue-500 ring-2 ring-blue-200'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                            onClick={() => setSelectedVariation(index)}
                          >
                            <img
                              src={variation.image_url}
                              alt={`Variation ${index + 1}`}
                              className="w-full h-16 object-cover"
                            />
                            <div className="absolute inset-0 bg-black/0 hover:bg-black/10 transition-colors" />
                            <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white text-xs px-1 py-0.5 text-center">
                              {index + 1}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Action buttons */}
                    <div className="flex gap-2">
                      <Button onClick={() => handleDownloadMockup()} className="flex-1">
                        <Download className="mr-2 h-4 w-4" />
                        Descargar Actual
                      </Button>
                      {generatedMockup.variations.length > 1 && (
                        <Button onClick={handleDownloadAll} variant="outline" className="flex-1">
                          <Download className="mr-2 h-4 w-4" />
                          Descargar Todas
                        </Button>
                      )}
                    </div>

                    {/* Metadata */}
                    <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg space-y-1">
                      <p><strong>Contexto:</strong> {generatedMockup.context_name}</p>
                      <p><strong>Variaciones:</strong> {generatedMockup.total_generated} generadas</p>
                      <p><strong>Tecnología:</strong> Emma AI Exclusiva</p>
                      {generatedMockup.variations[selectedVariation]?.metadata?.variation_style && (
                        <p><strong>Estilo:</strong> {generatedMockup.variations[selectedVariation].metadata.variation_style}</p>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                    <div className="text-center">
                      <Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                      <p className="text-gray-600">Tu mockup aparecerá aquí</p>
                      <p className="text-sm text-gray-500 mt-2">Sube una imagen y selecciona un contexto para comenzar</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default MockupGeneratorPage;

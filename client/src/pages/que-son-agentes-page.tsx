import React from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { useLocation } from 'wouter';

export default function QueSonAgentesPage() {
  const [, setLocation] = useLocation();

  const handleNavigation = (section: string) => {
    switch (section) {
      case 'home':
        setLocation('/dashboard/agents-marketplace');
        break;
      case 'agents':
        setLocation('/dashboard/agents-marketplace');
        break;
      case 'dashboard':
        setLocation('/dashboard');
        break;
      default:
        console.log(`Navigating to ${section}`);
    }
  };

  return (
    <DashboardLayout pageTitle="¿Qué son los Agentes IA?">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">

        {/* Header Navigation */}
        <header className="flex items-center justify-between border-b border-[#f1f2f3] px-10 py-3 bg-white">
          <div className="flex items-center gap-8">
            <div className="flex items-center gap-4 text-[#131416]">
              <div className="size-4">
                <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M44 4H30.6666V17.3334H17.3334V30.6666H4V44H44V4Z" fill="currentColor"></path></svg>
              </div>
              <h2 className="text-lg font-bold tracking-[-0.015em] bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">Emma AI</h2>
            </div>
            <nav className="flex gap-9 text-sm font-medium">
              <button className="text-[#0e141b] hover:text-[#3018ef] transition-colors" onClick={() => handleNavigation('home')}>Inicio</button>
              <button className="text-[#0e141b] hover:text-[#3018ef] transition-colors" onClick={() => handleNavigation('agents')}>Agentes</button>
              <button className="text-[#0e141b] text-[#3018ef] font-semibold">¿Qué son los Agentes?</button>
              <button className="text-[#0e141b] hover:text-[#3018ef] transition-colors" onClick={() => handleNavigation('dashboard')}>Dashboard</button>
            </nav>
          </div>
          <div className="flex gap-8">
            <button
              className="h-10 px-4 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white rounded-full font-bold text-sm tracking-[0.015em] hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300"
              onClick={() => setLocation('/dashboard/agents-marketplace')}
            >
              Ver Agentes
            </button>
          </div>
        </header>

        {/* Hero Section */}
        <div className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-[#3018ef]/5 to-[#dd3a5a]/5"></div>
          <div className="relative max-w-7xl mx-auto px-4 py-20 text-center">
            <div className="mb-8">
              <span className="inline-block px-4 py-2 bg-[#3018ef]/10 text-[#3018ef] rounded-full text-sm font-semibold mb-4">
                💡 Educación Emma
              </span>
              <h1 className="text-4xl md:text-7xl font-black text-gray-900 mb-6 leading-tight">
                ¿Qué son los
                <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent"> Agentes IA</span>?
              </h1>
              <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Te explicamos de forma súper simple qué son y cómo funcionan los empleados digitales
              </p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-6xl mx-auto px-4 py-16">

          {/* Section 1: ¿Qué es? */}
          <div className="mb-20">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-5xl font-bold text-gray-900 mb-4">
                🤖 ¿Qué es un Agente IA?
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                ES un empleado digital que trabaja para ti las 24 horas
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
              {/* Empleado Normal */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-r from-gray-200 to-gray-300 rounded-3xl blur-xl opacity-30 group-hover:opacity-50 transition-opacity"></div>
                <div className="relative bg-white p-8 rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300">
                  <div className="text-center mb-6">
                    <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-4xl">👨‍💼</span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">Empleado Normal</h3>
                    <p className="text-gray-500">Una persona real</p>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center text-gray-700">
                      <span className="w-2 h-2 bg-gray-400 rounded-full mr-3"></span>
                      Es una persona de carne y hueso
                    </div>
                    <div className="flex items-center text-gray-700">
                      <span className="w-2 h-2 bg-gray-400 rounded-full mr-3"></span>
                      Trabaja 8 horas al día
                    </div>
                    <div className="flex items-center text-gray-700">
                      <span className="w-2 h-2 bg-gray-400 rounded-full mr-3"></span>
                      Necesita descansar y dormir
                    </div>
                    <div className="flex items-center text-gray-700">
                      <span className="w-2 h-2 bg-gray-400 rounded-full mr-3"></span>
                      Puede enfermarse
                    </div>
                    <div className="flex items-center text-gray-700">
                      <span className="w-2 h-2 bg-gray-400 rounded-full mr-3"></span>
                      Habla contigo cara a cara
                    </div>
                  </div>
                </div>
              </div>

              {/* Agente IA */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-3xl blur-xl opacity-30 group-hover:opacity-50 transition-opacity"></div>
                <div className="relative bg-white p-8 rounded-3xl shadow-xl border border-[#3018ef]/20 hover:shadow-2xl transition-all duration-300">
                  <div className="text-center mb-6">
                    <div className="w-20 h-20 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-4xl">🤖</span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">Agente IA</h3>
                    <p className="text-[#3018ef] font-semibold">Un empleado digital</p>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center text-gray-700">
                      <span className="w-2 h-2 bg-[#3018ef] rounded-full mr-3"></span>
                      Es un programa de computadora
                    </div>
                    <div className="flex items-center text-gray-700">
                      <span className="w-2 h-2 bg-[#3018ef] rounded-full mr-3"></span>
                      Trabaja 24 horas al día
                    </div>
                    <div className="flex items-center text-gray-700">
                      <span className="w-2 h-2 bg-[#3018ef] rounded-full mr-3"></span>
                      Nunca necesita descansar
                    </div>
                    <div className="flex items-center text-gray-700">
                      <span className="w-2 h-2 bg-[#3018ef] rounded-full mr-3"></span>
                      No se enferma nunca
                    </div>
                    <div className="flex items-center text-gray-700">
                      <span className="w-2 h-2 bg-[#3018ef] rounded-full mr-3"></span>
                      Te habla por chat, email o teléfono
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Section 2: ¿Cómo funciona? */}
          <div className="mb-20">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-5xl font-bold text-gray-900 mb-4">
                🧠 ¿Cómo funcionan?
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Cada agente está especializado en hacer una cosa muy bien
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
              {/* Hunter Pro */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-r from-green-200 to-emerald-300 rounded-3xl blur-xl opacity-30 group-hover:opacity-50 transition-opacity"></div>
                <div className="relative bg-white p-8 rounded-3xl shadow-xl border border-green-100 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                  <div className="flex items-center mb-6">
                    <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center mr-4">
                      <span className="text-3xl">🔍</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">Hunter Pro</h3>
                      <p className="text-green-600 font-semibold">Busca clientes nuevos</p>
                    </div>
                  </div>
                  <div className="mb-4">
                    <h4 className="font-bold text-gray-900 mb-2">¿Qué hace?</h4>
                    <p className="text-gray-700 leading-relaxed">
                      Busca empresas en internet que necesitan tu producto.
                      Encuentra sus emails y les manda mensajes personalizados automáticamente.
                    </p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-xl">
                    <p className="text-green-700 italic font-medium">
                      💡 Es un detective digital que encuentra clientes nuevos para ti.
                    </p>
                  </div>
                </div>
              </div>

              {/* Calendar Bot */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-r from-orange-200 to-amber-300 rounded-3xl blur-xl opacity-30 group-hover:opacity-50 transition-opacity"></div>
                <div className="relative bg-white p-8 rounded-3xl shadow-xl border border-orange-100 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                  <div className="flex items-center mb-6">
                    <div className="w-16 h-16 bg-gradient-to-r from-orange-400 to-amber-500 rounded-2xl flex items-center justify-center mr-4">
                      <span className="text-3xl">📆</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">Calendar Bot</h3>
                      <p className="text-orange-600 font-semibold">Organiza tus citas</p>
                    </div>
                  </div>
                  <div className="mb-4">
                    <h4 className="font-bold text-gray-900 mb-2">¿Qué hace?</h4>
                    <p className="text-gray-700 leading-relaxed">
                      Cuando alguien quiere una cita contigo, habla con esa persona,
                      califica si realmente está interesada y agenda la reunión automáticamente.
                    </p>
                  </div>
                  <div className="bg-orange-50 p-4 rounded-xl">
                    <p className="text-orange-700 italic font-medium">
                      💡 Es una secretaria digital que organiza todas tus citas.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-12 max-w-3xl mx-auto">
              <div className="bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 p-8 rounded-3xl border border-[#3018ef]/20">
                <div className="text-center">
                  <h4 className="text-2xl font-bold text-gray-900 mb-4">💡 En resumen:</h4>
                  <p className="text-lg text-gray-700 leading-relaxed">
                    Un agente IA ES un empleado especialista que nunca duerme,
                    nunca se cansa y siempre hace su trabajo exactamente como se lo enseñaste.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Section 3: Ejemplos */}
          <div className="mb-20">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-5xl font-bold text-gray-900 mb-4">
                📝 Ejemplos reales
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Para que entiendas mejor, aquí tienes ejemplos de lo que pueden hacer
              </p>
            </div>

            <div className="space-y-8 max-w-4xl mx-auto">
              {/* Ejemplo 1 */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-200 to-cyan-300 rounded-3xl blur-xl opacity-20 group-hover:opacity-40 transition-opacity"></div>
                <div className="relative bg-white p-8 rounded-3xl shadow-xl border border-blue-100 hover:shadow-2xl transition-all duration-300">
                  <div className="flex items-start">
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-2xl flex items-center justify-center mr-6 flex-shrink-0">
                      <span className="text-3xl">💬</span>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-gray-900 mb-2">Community Manager AI</h3>
                      <div className="space-y-4">
                        <div className="bg-blue-50 p-4 rounded-xl">
                          <p className="font-semibold text-blue-900 mb-2">📱 Situación:</p>
                          <p className="text-blue-800">Alguien comenta en tu Facebook "¿A qué hora abren?"</p>
                        </div>
                        <div className="bg-green-50 p-4 rounded-xl">
                          <p className="font-semibold text-green-900 mb-2">🤖 Lo que hace el agente:</p>
                          <p className="text-green-800">Lee el comentario, busca en tu información que abres a las 9 AM, y responde automáticamente "¡Hola! Abrimos a las 9:00 AM de lunes a viernes. ¡Te esperamos!"</p>
                        </div>
                        <div className="bg-purple-50 p-4 rounded-xl">
                          <p className="font-semibold text-purple-900 mb-2">✨ Resultado:</p>
                          <p className="text-purple-800">Tu cliente recibe respuesta inmediata, aunque sean las 2 AM.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Ejemplo 2 */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-r from-green-200 to-emerald-300 rounded-3xl blur-xl opacity-20 group-hover:opacity-40 transition-opacity"></div>
                <div className="relative bg-white p-8 rounded-3xl shadow-xl border border-green-100 hover:shadow-2xl transition-all duration-300">
                  <div className="flex items-start">
                    <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center mr-6 flex-shrink-0">
                      <span className="text-3xl">📲</span>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-gray-900 mb-2">Lead Agent (WhatsApp)</h3>
                      <div className="space-y-4">
                        <div className="bg-blue-50 p-4 rounded-xl">
                          <p className="font-semibold text-blue-900 mb-2">📱 Situación:</p>
                          <p className="text-blue-800">Alguien te manda WhatsApp preguntando precios de tus productos.</p>
                        </div>
                        <div className="bg-green-50 p-4 rounded-xl">
                          <p className="font-semibold text-green-900 mb-2">🤖 Lo que hace el agente:</p>
                          <p className="text-green-800">Le pregunta qué producto le interesa, cuántas piezas necesita, le da el precio correcto y le pregunta si quiere agendar una llamada para cerrar la venta.</p>
                        </div>
                        <div className="bg-purple-50 p-4 rounded-xl">
                          <p className="font-semibold text-purple-900 mb-2">✨ Resultado:</p>
                          <p className="text-purple-800">Conviertes más mensajes en ventas reales.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-12 max-w-3xl mx-auto">
              <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-8 rounded-3xl border border-gray-200">
                <div className="text-center">
                  <h4 className="text-2xl font-bold text-gray-900 mb-4">🎯 La idea principal:</h4>
                  <p className="text-lg text-gray-700 leading-relaxed">
                    Los agentes IA SON empleados digitales que hacen tareas repetitivas automáticamente,
                    sin que tengas que estar pendiente. <span className="font-bold text-[#3018ef]">Trabajan mientras tú duermes.</span>
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* CTA Final */}
          <div className="text-center">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-3xl blur-2xl opacity-20"></div>
              <div className="relative bg-white p-12 rounded-3xl shadow-2xl border border-[#3018ef]/20">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                  ¿Listo para conocer a tus nuevos
                  <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent"> empleados digitales</span>?
                </h2>
                <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                  Descubre los 5 agentes Emma especializados que pueden transformar tu negocio desde hoy mismo.
                </p>
                <button
                  className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white px-12 py-4 rounded-2xl font-bold text-xl hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 transform"
                  onClick={() => setLocation('/dashboard/agents-marketplace')}
                >
                  Ver Agentes Disponibles 🚀
                </button>
              </div>
            </div>
          </div>

        </div>
      </div>
    </DashboardLayout>
  );
}
/**
 * SEO & GPT Optimizer™ - Tool Page for Emma Studio Integration
 * This page handles the tool route from Emma Studio's tool system
 */

import React from 'react';
import { useLocation } from 'wouter';
import DashboardLayoutWrapper from '@/components/layout/dashboard-layout';
import SEOGPTOptimizerApp from './seo-gpt-optimizer/SEOGPTOptimizerApp';

const SEOGPTOptimizerToolPage: React.FC = () => {
  const [location] = useLocation();
  
  // Check if we're in the tool route
  const isToolRoute = location.startsWith('/dashboard/herramientas/seo-gpt-optimizer');
  
  if (isToolRoute) {
    // Render the full SEO GPT Optimizer app
    return <SEOGPTOptimizerApp />;
  }
  
  // Fallback to dashboard layout if needed
  return (
    <DashboardLayoutWrapper pageTitle="SEO & GPT Optimizer™">
      <SEOGPTOptimizerApp />
    </DashboardLayoutWrapper>
  );
};

export default SEOGPTOptimizerToolPage;

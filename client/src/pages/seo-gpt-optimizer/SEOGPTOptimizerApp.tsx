/**
 * SEO & GPT Optimizer™ - Main Application Component
 * Root component adapted for Emma Studio's wouter routing system
 */

import React, { Suspense } from 'react';
import { Switch, Route, useLocation } from 'wouter';
import { SEOGPTOptimizerProvider } from '../../context/SEOGPTOptimizerContext';
import ErrorBoundary from '../../components/seo-gpt-optimizer/shared/ErrorBoundary';

// Lazy load pages but with preload for better performance
const SEOGPTOptimizerDashboard = React.lazy(() => import('./index'));
const ResearchPage = React.lazy(() => import('./research-page'));
const SavedResearchPage = React.lazy(() => import('./saved-research-page'));
const ContentBuilderPage = React.lazy(() => import('./content-builder-page'));
const AnalyticsPage = React.lazy(() => import('./analytics-page'));
const ProjectsPage = React.lazy(() => import('./projects-page'));
const ProjectDashboardPage = React.lazy(() => import('./project-dashboard-page'));

// Ultra fast loading fallback
const PageLoadingFallback: React.FC = () => (
  <div className="h-screen bg-white flex items-center justify-center">
    <div className="w-6 h-6 border-2 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
  </div>
);

// Route wrapper with error boundary and suspense
const RouteWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ErrorBoundary>
    <Suspense fallback={<PageLoadingFallback />}>
      {children}
    </Suspense>
  </ErrorBoundary>
);

const SEOGPTOptimizerApp: React.FC = () => {
  const [location] = useLocation();

  // Extract the sub-path after /dashboard/herramientas/seo-gpt-optimizer
  const basePath = '/dashboard/herramientas/seo-gpt-optimizer';
  const subPath = location.replace(basePath, '') || '/';

  return (
    <ErrorBoundary>
      <SEOGPTOptimizerProvider>
        <Switch>
          {/* Main Dashboard */}
          <Route path={`${basePath}`}>
            <RouteWrapper>
              <SEOGPTOptimizerDashboard />
            </RouteWrapper>
          </Route>

          {/* Research Engine */}
          <Route path={`${basePath}/research`}>
            <RouteWrapper>
              <ResearchPage />
            </RouteWrapper>
          </Route>

          {/* Saved Research */}
          <Route path={`${basePath}/saved-research`}>
            <RouteWrapper>
              <SavedResearchPage />
            </RouteWrapper>
          </Route>

          {/* Content Builder */}
          <Route path={`${basePath}/content-builder`}>
            <RouteWrapper>
              <ContentBuilderPage />
            </RouteWrapper>
          </Route>

          {/* Content Builder with Project ID */}
          <Route path={`${basePath}/content-builder/:projectId`}>
            {(params) => (
              <RouteWrapper>
                <ContentBuilderPage projectId={params.projectId} />
              </RouteWrapper>
            )}
          </Route>

          {/* Analytics Dashboard */}
          <Route path={`${basePath}/analytics`}>
            <RouteWrapper>
              <AnalyticsPage />
            </RouteWrapper>
          </Route>

          {/* Project Management */}
          <Route path={`${basePath}/projects`}>
            <RouteWrapper>
              <ProjectsPage />
            </RouteWrapper>
          </Route>

          {/* Individual Project Dashboard */}
          <Route path={`${basePath}/project/:projectId`}>
            <RouteWrapper>
              <ProjectDashboardPage />
            </RouteWrapper>
          </Route>
        </Switch>
      </SEOGPTOptimizerProvider>
    </ErrorBoundary>
  );
};

export default SEOGPTOptimizerApp;

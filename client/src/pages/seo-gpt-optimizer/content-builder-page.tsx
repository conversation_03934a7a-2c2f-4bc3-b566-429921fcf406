/**
 * SEO & GPT Optimizer™ - Content Builder Page
 * Main page for the content builder with real-time analysis
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Save, Settings, Layout, PanelRightOpen, PanelRightClose } from 'lucide-react';
import { useLocation, useRoute } from 'wouter';

import { useGPTRank } from '../../hooks/seo-gpt-optimizer/useGPTRank';
import { useProjects } from '../../hooks/seo-gpt-optimizer/useProjects';
import { useKeyboardShortcuts } from '../../hooks/seo-gpt-optimizer/useKeyboardShortcuts';
import ErrorBoundary from '../../components/seo-gpt-optimizer/shared/ErrorBoundary';
import ContentEditor from '../../components/seo-gpt-optimizer/content-builder/ContentEditor';
import EmmaAIRank from '../../components/seo-gpt-optimizer/content-builder/SAIOAnalyzer';
import SuggestionsPanel from '../../components/seo-gpt-optimizer/content-builder/SuggestionsPanel';
import { ImprovementSuggestion } from '../../types/seo-gpt-optimizer';

const ContentBuilderPage: React.FC = () => {
  const [, setLocation] = useLocation();
  const [match, params] = useRoute('/dashboard/herramientas/seo-gpt-optimizer/content-builder/:projectId');
  const projectId = params?.projectId;
  
  const [showRightPanel, setShowRightPanel] = useState(true);
  const [rightPanelTab, setRightPanelTab] = useState<'metrics' | 'suggestions'>('metrics');
  const [content, setContent] = useState('');
  const [topic, setTopic] = useState('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const { 
    analysis, 
    loading: analysisLoading, 
    error: analysisError 
  } = useGPTRank();

  const { 
    currentProject, 
    loadProject, 
    updateProject,
    loading: projectLoading 
  } = useProjects();

  // Load project if projectId is provided
  useEffect(() => {
    if (projectId) {
      loadProject(projectId);
    }
  }, [projectId, loadProject]);

  // Set initial content from project
  useEffect(() => {
    if (currentProject) {
      setContent(currentProject.content_text || '');
      setTopic(currentProject.topic || '');
    }
  }, [currentProject]);

  const handleBack = () => {
    if (hasUnsavedChanges) {
      const confirmLeave = window.confirm(
        'Tienes cambios sin guardar. ¿Estás seguro de que quieres salir?'
      );
      if (!confirmLeave) return;
    }
    setLocation('/dashboard/herramientas/seo-gpt-optimizer');
  };

  const handleSave = async (contentToSave: string) => {
    if (projectId && currentProject) {
      try {
        await updateProject(projectId, {
          content_text: contentToSave,
          content_length: contentToSave.length,
          word_count: contentToSave.trim().split(/\s+/).filter(w => w.length > 0).length,
          current_gpt_rank_score: analysis?.gpt_rank_score || currentProject.current_gpt_rank_score,
          updated_at: new Date().toISOString()
        });
        setHasUnsavedChanges(false);
      } catch (error) {
        console.error('Error saving project:', error);
      }
    }
  };

  const handleContentChange = (newContent: string, newAnalysis: any) => {
    setContent(newContent);
    setHasUnsavedChanges(true);
  };

  const handleApplySuggestion = (suggestion: ImprovementSuggestion) => {
    // This could be enhanced to actually apply the suggestion to the content
    console.log('Applying suggestion:', suggestion);
    // For now, just switch to the editor
    setRightPanelTab('metrics');
  };

  const toggleRightPanel = () => {
    setShowRightPanel(!showRightPanel);
  };

  const handleExport = () => {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${topic || 'content'}-${Date.now()}.txt`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleToggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
  };

  // Setup keyboard shortcuts
  const { shortcuts } = useKeyboardShortcuts({
    onSave: () => handleSave(content),
    onTogglePanel: toggleRightPanel,
    onToggleFullscreen: handleToggleFullscreen,
    onExport: handleExport,
    onNewDocument: () => {
      if (hasUnsavedChanges) {
        const confirmNew = window.confirm('¿Crear nuevo documento? Los cambios no guardados se perderán.');
        if (!confirmNew) return;
      }
      setContent('');
      setTopic('');
      setHasUnsavedChanges(false);
    }
  });

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-full px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleBack}
                  className="p-2 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600" />
                </button>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">
                    {currentProject ? currentProject.title : 'Content Builder'}
                  </h1>
                  <p className="text-gray-600 text-sm">
                    {currentProject ? currentProject.topic : 'Crea contenido optimizado con análisis en tiempo real'}
                  </p>
                </div>
                {hasUnsavedChanges && (
                  <div className="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-lg text-xs font-medium">
                    Cambios sin guardar
                  </div>
                )}
              </div>

              <div className="flex items-center gap-3">
                {/* Panel Toggle */}
                <button
                  onClick={toggleRightPanel}
                  className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                  title={showRightPanel ? "Ocultar panel" : "Mostrar panel"}
                >
                  {showRightPanel ? <PanelRightClose className="w-5 h-5" /> : <PanelRightOpen className="w-5 h-5" />}
                </button>

                {/* Settings */}
                <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-xl transition-colors duration-200">
                  <Settings className="w-5 h-5" />
                </button>

                {/* Layout Toggle */}
                <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-xl transition-colors duration-200">
                  <Layout className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex h-[calc(100vh-80px)]">
          {/* Editor Area */}
          <div className={`flex-1 transition-all duration-300 ${showRightPanel ? 'mr-96' : ''}`}>
            <div className="h-full p-4">
              <ContentEditor
                projectId={projectId}
                initialContent={content}
                initialTopic={topic}
                onSave={handleSave}
                onContentChange={handleContentChange}
                className="h-full"
              />
            </div>
          </div>

          {/* Right Panel */}
          <motion.div
            className="fixed right-0 top-20 bottom-0 w-96 bg-white border-l border-gray-200 shadow-lg z-20"
            initial={false}
            animate={{ 
              x: showRightPanel ? 0 : '100%',
              opacity: showRightPanel ? 1 : 0
            }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
          >
            {/* Panel Tabs */}
            <div className="flex border-b border-gray-200">
              <button
                onClick={() => setRightPanelTab('metrics')}
                className={`flex-1 px-4 py-3 text-sm font-medium transition-colors duration-200 ${
                  rightPanelTab === 'metrics'
                    ? 'text-purple-600 border-b-2 border-purple-600 bg-purple-50'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                Emma AI Rank
              </button>
              <button
                onClick={() => setRightPanelTab('suggestions')}
                className={`flex-1 px-4 py-3 text-sm font-medium transition-colors duration-200 relative ${
                  rightPanelTab === 'suggestions'
                    ? 'text-purple-600 border-b-2 border-purple-600 bg-purple-50'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                Sugerencias
                {analysis?.improvement_suggestions?.length > 0 && (
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {analysis.improvement_suggestions.length}
                  </span>
                )}
              </button>
            </div>

            {/* Panel Content */}
            <div className="h-full overflow-hidden">
              {rightPanelTab === 'metrics' ? (
                <EmmaAIRank
                  projectId={projectId || 'demo'}
                  content={content}
                  onAnalyze={() => {}}
                  className="h-full border-none shadow-none rounded-none"
                />
              ) : (
                <SuggestionsPanel
                  analysis={analysis}
                  onApplySuggestion={handleApplySuggestion}
                  className="h-full border-none shadow-none rounded-none"
                />
              )}
            </div>
          </motion.div>
        </div>

        {/* Loading Overlay */}
        {projectLoading.isLoading && (
          <motion.div
            className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <div className="bg-white rounded-2xl p-8 shadow-xl">
              <div className="text-center">
                <div className="w-8 h-8 border-2 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-gray-700 font-medium">{projectLoading.message}</p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Keyboard Shortcuts Help */}
        <div className="fixed bottom-4 left-4 text-xs text-gray-500 bg-white/80 backdrop-blur-sm rounded-lg px-3 py-2 border border-gray-200">
          <div className="space-y-1">
            {shortcuts.slice(0, 4).map((shortcut, index) => (
              <div key={index}>
                <kbd className="bg-gray-100 px-1 rounded">{shortcut.key}</kbd> {shortcut.description}
              </div>
            ))}
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default ContentBuilderPage;

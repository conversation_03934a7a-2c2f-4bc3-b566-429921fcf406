/**
 * SEO & GPT Optimizer™ - Main Dashboard Page
 * Main dashboard for the SEO & GPT Optimizer tool
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Search, 
  PenTool, 
  BarChart3, 
  Plus, 
  TrendingUp, 
  FileText, 
  Clock,
  Target,
  Zap,
  Brain
} from 'lucide-react';
import { useLocation } from 'wouter';

import { useProjects } from '../../hooks/seo-gpt-optimizer/useProjects';
import { seoGptAPI } from '../../services/seo-gpt-optimizer/api';
import ErrorBoundary from '../../components/seo-gpt-optimizer/shared/ErrorBoundary';
import LoadingSpinner from '../../components/seo-gpt-optimizer/shared/LoadingSpinner';
import GPTRankMeter from '../../components/seo-gpt-optimizer/shared/GPTRankMeter';

interface DashboardStats {
  total_projects: number;
  active_projects: number;
  avg_gpt_rank_score: number;
  total_research_conducted: number;
  improvement_this_month: number;
}

const SEOGPTOptimizerDashboard: React.FC = () => {
  const [, setLocation] = useLocation();
  const { projects, loading, recentProjects, activeProjects } = useProjects();
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [statsLoading, setStatsLoading] = useState(true);

  // Load dashboard stats
  useEffect(() => {
    const loadStats = async () => {
      try {
        const response = await seoGptAPI.getDashboardStats();
        if (response.status === 'success' && response.data) {
          setDashboardStats(response.data);
        }
      } catch (error) {
        console.error('Error loading dashboard stats:', error);
      } finally {
        setStatsLoading(false);
      }
    };

    loadStats();
  }, []);

  const handleGoToProjects = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer/projects');
  };

  const handleStartResearch = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer/research');
  };

  const handleOpenContentBuilder = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer/content-builder');
  };

  const handleViewAnalytics = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer/analytics');
  };

  if (loading.isLoading && !projects.length) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
        <LoadingSpinner size="lg" message="Cargando SEO & GPT Optimizer™..." />
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  SEO & GPT Optimizer™
                </h1>
                <p className="text-gray-600 mt-1">
                  Optimiza tu contenido para motores de búsqueda y modelos de IA
                </p>
              </div>
              
              <motion.button
                onClick={handleGoToProjects}
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center gap-2 shadow-lg"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Plus className="w-5 h-5" />
                Gestionar Proyectos
              </motion.button>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {statsLoading ? (
              Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="bg-white rounded-2xl p-6 shadow-sm animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))
            ) : (
              <>
                <motion.div
                  className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-600 text-sm font-medium">Proyectos Totales</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {dashboardStats?.total_projects || 0}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                      <FileText className="w-6 h-6 text-blue-600" />
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-600 text-sm font-medium">Proyectos Activos</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {dashboardStats?.active_projects || 0}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                      <Clock className="w-6 h-6 text-green-600" />
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-600 text-sm font-medium">Puntuación Promedio</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {dashboardStats?.avg_gpt_rank_score?.toFixed(1) || '0.0'}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                      <Target className="w-6 h-6 text-purple-600" />
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-600 text-sm font-medium">Mejora Este Mes</p>
                      <p className="text-2xl font-bold text-green-600">
                        +{dashboardStats?.improvement_this_month?.toFixed(1) || '0.0'}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                      <TrendingUp className="w-6 h-6 text-green-600" />
                    </div>
                  </div>
                </motion.div>
              </>
            )}
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <motion.div
              className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 cursor-pointer hover:shadow-md transition-all duration-200"
              onClick={handleStartResearch}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6">
                <Search className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Research Engine</h3>
              <p className="text-gray-600 mb-4">
                Investiga temas, analiza competidores y encuentra oportunidades de contenido
              </p>
              <div className="flex items-center text-blue-600 font-medium">
                <span>Iniciar Investigación</span>
                <Zap className="w-4 h-4 ml-2" />
              </div>
            </motion.div>

            <motion.div
              className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 cursor-pointer hover:shadow-md transition-all duration-200"
              onClick={handleOpenContentBuilder}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6">
                <PenTool className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Content Builder</h3>
              <p className="text-gray-600 mb-4">
                Crea y optimiza contenido con análisis GPT Rank en tiempo real
              </p>
              <div className="flex items-center text-purple-600 font-medium">
                <span>Crear Contenido</span>
                <Brain className="w-4 h-4 ml-2" />
              </div>
            </motion.div>

            <motion.div
              className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 cursor-pointer hover:shadow-md transition-all duration-200"
              onClick={handleViewAnalytics}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
            >
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6">
                <BarChart3 className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Analytics</h3>
              <p className="text-gray-600 mb-4">
                Analiza el rendimiento y evolución de tus proyectos
              </p>
              <div className="flex items-center text-green-600 font-medium">
                <span>Ver Analytics</span>
                <TrendingUp className="w-4 h-4 ml-2" />
              </div>
            </motion.div>
          </div>

          {/* Recent Projects */}
          {recentProjects.length > 0 && (
            <motion.div
              className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
            >
              <div className="p-6 border-b border-gray-100">
                <h2 className="text-xl font-bold text-gray-900">Proyectos Recientes</h2>
                <p className="text-gray-600 mt-1">Tus últimos proyectos de optimización</p>
              </div>
              
              <div className="divide-y divide-gray-100">
                {recentProjects.slice(0, 5).map((project, index) => (
                  <motion.div
                    key={project.project_id}
                    className="p-6 hover:bg-gray-50 cursor-pointer transition-colors duration-200"
                    onClick={() => setLocation(`/dashboard/herramientas/seo-gpt-optimizer/project/${project.project_id}`)}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.9 + index * 0.1 }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900">{project.title}</h3>
                        <p className="text-sm text-gray-600 mt-1">{project.topic}</p>
                        <div className="flex items-center gap-4 mt-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            project.status === 'completed' ? 'bg-green-100 text-green-700' :
                            project.status === 'writing' ? 'bg-blue-100 text-blue-700' :
                            project.status === 'researching' ? 'bg-yellow-100 text-yellow-700' :
                            'bg-gray-100 text-gray-700'
                          }`}>
                            {project.status}
                          </span>
                          <span className="text-xs text-gray-500">
                            {new Date(project.updated_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      
                      <div className="ml-6">
                        <GPTRankMeter
                          score={project.current_gpt_rank_score}
                          grade={project.current_gpt_rank_score >= 90 ? 'A+' : 
                                 project.current_gpt_rank_score >= 80 ? 'A' : 
                                 project.current_gpt_rank_score >= 70 ? 'B' : 'C'}
                          size="sm"
                          showDetails={false}
                          animated={false}
                        />
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
              
              <div className="p-6 bg-gray-50 border-t border-gray-100">
                <button
                  onClick={() => setLocation('/dashboard/herramientas/seo-gpt-optimizer/projects')}
                  className="text-blue-600 hover:text-blue-700 font-medium text-sm"
                >
                  Ver todos los proyectos →
                </button>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default SEOGPTOptimizerDashboard;

/**
 * SEO & GPT Optimizer™ - Projects Page
 * Main page for project management
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Download, Upload, Settings } from 'lucide-react';
import { useLocation } from 'wouter';

import { useProjects } from '../../hooks/seo-gpt-optimizer/useProjects';
import { 
  SEOGPTProject, 
  ProjectCreateRequest, 
  ProjectStatus 
} from '../../types/seo-gpt-optimizer';
import ErrorBoundary from '../../components/seo-gpt-optimizer/shared/ErrorBoundary';
import ProjectList from '../../components/seo-gpt-optimizer/project-management/ProjectList';
import ProjectForm from '../../components/seo-gpt-optimizer/project-management/ProjectForm';

const ProjectsPage: React.FC = () => {
  const [, setLocation] = useLocation();
  const {
    projects,
    loading,
    error,
    createProject,
    updateProject,
    deleteProject,
    loadProjects,
    clearError
  } = useProjects();

  const [showProjectForm, setShowProjectForm] = useState(false);
  const [editingProject, setEditingProject] = useState<SEOGPTProject | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Load projects on mount
  useEffect(() => {
    loadProjects();
  }, [loadProjects]);

  const handleBack = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer');
  };

  const handleCreateProject = () => {
    setEditingProject(null);
    setShowProjectForm(true);
  };

  const handleEditProject = (project: SEOGPTProject) => {
    setEditingProject(project);
    setShowProjectForm(true);
  };

  const handleCloseForm = () => {
    setShowProjectForm(false);
    setEditingProject(null);
  };

  const handleSaveProject = async (data: ProjectCreateRequest | Partial<SEOGPTProject>) => {
    try {
      if (editingProject) {
        // Update existing project
        await updateProject(editingProject.project_id, data as Partial<SEOGPTProject>);
      } else {
        // Create new project
        const projectId = await createProject(data as ProjectCreateRequest);
        if (projectId) {
          // Navigate to the new project's dashboard
          setLocation(`/dashboard/herramientas/seo-gpt-optimizer/project/${projectId}`);
          return; // Don't close the form here since we're navigating away
        }
      }
      setShowProjectForm(false);
      setEditingProject(null);
    } catch (error) {
      console.error('Error saving project:', error);
      // Error handling is managed by the useProjects hook
    }
  };

  const handleDeleteProject = async (projectId: string) => {
    try {
      await deleteProject(projectId);
    } catch (error) {
      console.error('Error deleting project:', error);
    }
  };

  const handleDuplicateProject = async (project: SEOGPTProject) => {
    try {
      const duplicateData: ProjectCreateRequest = {
        title: `${project.title} (Copia)`,
        topic: project.topic,
        target_language: project.target_language,
        content_type: project.content_type,
        target_gpt_rank_score: project.target_gpt_rank_score
      };
      
      const newProjectId = await createProject(duplicateData);
      if (newProjectId) {
        // Optionally show success message
        console.log('Project duplicated successfully');
      }
    } catch (error) {
      console.error('Error duplicating project:', error);
    }
  };

  const handleOpenProject = (projectId: string) => {
    setLocation(`/dashboard/herramientas/seo-gpt-optimizer/project/${projectId}`);
  };

  const handleStatusChange = async (projectId: string, status: ProjectStatus) => {
    try {
      await updateProject(projectId, { 
        status,
        updated_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error updating project status:', error);
    }
  };

  const handleRefresh = async () => {
    await loadProjects();
    setLastRefresh(new Date());
  };

  const handleExport = () => {
    try {
      const exportData = {
        exported_at: new Date().toISOString(),
        total_projects: projects.length,
        projects: projects.map(project => ({
          id: project.project_id,
          title: project.title,
          topic: project.topic,
          status: project.status,
          content_type: project.content_type,
          current_score: project.current_gpt_rank_score,
          target_score: project.target_gpt_rank_score,
          word_count: project.word_count,
          created_at: project.created_at,
          updated_at: project.updated_at
        }))
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `seo-gpt-projects-${Date.now()}.json`;
      link.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting projects:', error);
    }
  };

  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        const text = await file.text();
        const data = JSON.parse(text);
        
        if (data.projects && Array.isArray(data.projects)) {
          // Process imported projects
          console.log('Importing projects:', data.projects);
          // You could implement batch import functionality here
        } else {
          throw new Error('Invalid file format');
        }
      } catch (error) {
        console.error('Error importing projects:', error);
        alert('Error al importar el archivo. Verifica que sea un archivo JSON válido.');
      }
    };
    input.click();
  };

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleBack}
                  className="p-2 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600" />
                </button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Gestión de Proyectos</h1>
                  <p className="text-gray-600 text-sm">
                    Administra todos tus proyectos de SEO & GPT Optimizer™
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                {/* Import/Export */}
                <button
                  onClick={handleImport}
                  className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                  title="Importar proyectos"
                >
                  <Upload className="w-4 h-4" />
                  Importar
                </button>

                <button
                  onClick={handleExport}
                  className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                  title="Exportar proyectos"
                >
                  <Download className="w-4 h-4" />
                  Exportar
                </button>

                {/* Settings */}
                <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-xl transition-colors duration-200">
                  <Settings className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Last Refresh Info */}
            <div className="mt-4 text-sm text-gray-500">
              Última actualización: {lastRefresh.toLocaleString()}
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Error Display */}
          {error.hasError && (
            <motion.div
              className="bg-red-50 border border-red-200 rounded-2xl p-6 mb-8"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <span className="text-red-600 font-bold">!</span>
                </div>
                <div>
                  <h3 className="font-semibold text-red-900">Error en la gestión de proyectos</h3>
                  <p className="text-red-700 text-sm mt-1">{error.message}</p>
                </div>
                <button
                  onClick={clearError}
                  className="ml-auto text-red-600 hover:text-red-700"
                >
                  ×
                </button>
              </div>
            </motion.div>
          )}

          {/* Project Statistics */}
          {projects.length > 0 && (
            <motion.div
              className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                <div className="text-2xl font-bold text-blue-600">{projects.length}</div>
                <div className="text-sm text-gray-600">Total Proyectos</div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                <div className="text-2xl font-bold text-green-600">
                  {projects.filter(p => p.status === ProjectStatus.COMPLETED).length}
                </div>
                <div className="text-sm text-gray-600">Completados</div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                <div className="text-2xl font-bold text-yellow-600">
                  {projects.filter(p => [ProjectStatus.RESEARCHING, ProjectStatus.WRITING, ProjectStatus.OPTIMIZING].includes(p.status)).length}
                </div>
                <div className="text-sm text-gray-600">En Progreso</div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                <div className="text-2xl font-bold text-purple-600">
                  {projects.length > 0 
                    ? (projects.reduce((sum, p) => sum + p.current_gpt_rank_score, 0) / projects.length).toFixed(1)
                    : '0.0'
                  }
                </div>
                <div className="text-sm text-gray-600">Puntuación Promedio</div>
              </div>
            </motion.div>
          )}

          {/* Project List */}
          <ProjectList
            projects={projects}
            loading={loading.isLoading}
            onCreateProject={handleCreateProject}
            onEditProject={handleEditProject}
            onDeleteProject={handleDeleteProject}
            onDuplicateProject={handleDuplicateProject}
            onOpenProject={handleOpenProject}
            onStatusChange={handleStatusChange}
            onRefresh={handleRefresh}
            onExport={handleExport}
          />
        </div>

        {/* Project Form Modal */}
        <ProjectForm
          project={editingProject || undefined}
          isOpen={showProjectForm}
          onClose={handleCloseForm}
          onSave={handleSaveProject}
          loading={loading.isLoading}
        />
      </div>
    </ErrorBoundary>
  );
};

export default ProjectsPage;

/**
 * SEO & GPT Optimizer™ - Research Page
 * Main page for conducting research
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Download, Share2, Save, Check } from 'lucide-react';
import { useLocation } from 'wouter';

import { useResearch } from '../../hooks/seo-gpt-optimizer/useResearch';
import { useSavedResearch } from '../../hooks/seo-gpt-optimizer/useSavedResearch';
import ErrorBoundary from '../../components/seo-gpt-optimizer/shared/ErrorBoundary';
import LoadingSpinner from '../../components/seo-gpt-optimizer/shared/LoadingSpinner';
import ResearchForm from '../../components/seo-gpt-optimizer/research-engine/ResearchForm';
import ResearchResults from '../../components/seo-gpt-optimizer/research-engine/ResearchResults';

const ResearchPage: React.FC = () => {
  const [, setLocation] = useLocation();
  const [isSaved, setIsSaved] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const {
    researchResults,
    loading,
    error,
    conductResearch,
    clearResults,
    clearError,
    hasResults,
    researchConfidence,
    processingTime
  } = useResearch();

  const { saveResearch } = useSavedResearch();

  const handleBack = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer');
  };

  const handleNewResearch = () => {
    clearResults();
    clearError();
    setIsSaved(false);
  };

  const handleSaveResearch = async () => {
    if (researchResults) {
      try {
        await saveResearch({
          topic: researchResults.topic,
          results: researchResults,
          confidence: researchConfidence,
          processingTime: processingTime
        });

        setIsSaved(true);

        // Mostrar mensaje temporal
        setTimeout(() => setIsSaved(false), 3000);
      } catch (error) {
        console.error('Error al guardar investigación:', error);
        // Aquí podrías mostrar un mensaje de error al usuario
      }
    }
  };

  const handleExportResults = async () => {
    if (!researchResults) return;

    setIsExporting(true);

    try {
      // Generar contenido HTML estructurado para PDF
      const htmlContent = generatePDFContent(researchResults, researchConfidence, processingTime);

      // Crear blob con el contenido HTML
      const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
      const url = URL.createObjectURL(blob);

      // Crear enlace de descarga
      const link = document.createElement('a');
      link.href = url;
      link.download = `investigacion-${researchResults.topic.replace(/[^a-zA-Z0-9]/g, '-')}-${Date.now()}.html`;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Limpiar URL
      URL.revokeObjectURL(url);

    } catch (error) {
      console.error('Error al exportar:', error);
      // Fallback: descargar como JSON si falla
      const dataStr = JSON.stringify(researchResults, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `research-${researchResults.topic}-${Date.now()}.json`;
      link.click();
      URL.revokeObjectURL(url);
    } finally {
      setIsExporting(false);
    }
  };

  const generatePDFContent = (results: any, confidence: number, time: number) => {
    const currentDate = new Date().toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Investigación: ${results.topic}</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
          }
          .download-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
          }
          .download-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
          }
          .download-info p {
            margin: 5px 0;
            font-size: 14px;
            color: #555;
          }
          .print-button {
            background: #3018ef;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
          }
          .print-button:hover {
            background: #2516d6;
          }
          .header {
            text-align: center;
            border-bottom: 3px solid #3018ef;
            padding-bottom: 20px;
            margin-bottom: 30px;
          }
          .header h1 {
            color: #3018ef;
            margin: 0;
            font-size: 28px;
          }
          .header .subtitle {
            color: #666;
            margin: 10px 0;
          }
          .meta-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
          }
          .section {
            margin-bottom: 30px;
          }
          .section h2 {
            color: #3018ef;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 8px;
            margin-bottom: 15px;
          }
          .section h3 {
            color: #dd3a5a;
            margin-bottom: 10px;
          }
          .insights-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
          }
          .insight-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3018ef;
          }
          .questions-list {
            background: #fff7ed;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #dd3a5a;
          }
          .questions-list ul {
            margin: 0;
            padding-left: 20px;
          }
          .opportunities {
            background: #f0fdf4;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #10b981;
          }
          .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #666;
            font-size: 12px;
          }
          @media print {
            body { margin: 0; padding: 15px; }
            .insights-grid { grid-template-columns: 1fr; }
          }
        </style>
      </head>
      <body>
        <div class="download-info">
          <h3>📄 Reporte de Investigación Descargado</h3>
          <p>Haz clic en el botón para descargar como PDF:</p>
          <button class="print-button" onclick="window.print()">📄 Descargar como PDF</button>
          <p style="font-size: 12px; margin-top: 10px;">O usa: Ctrl+P (Cmd+P en Mac) → Guardar como PDF</p>
        </div>

        <script>
          // Auto-hide download info when printing
          window.addEventListener('beforeprint', function() {
            document.querySelector('.download-info').style.display = 'none';
          });
          window.addEventListener('afterprint', function() {
            document.querySelector('.download-info').style.display = 'block';
          });
        </script>

        <div class="header">
          <h1>📊 Investigación de Contenido</h1>
          <div class="subtitle">Análisis completo para: <strong>${results.topic}</strong></div>
          <div class="subtitle">Generado el ${currentDate}</div>
        </div>

        <div class="meta-info">
          <strong>📈 Métricas de la Investigación:</strong><br>
          • Confianza: ${(confidence * 100).toFixed(1)}%<br>
          • Tiempo de procesamiento: ${time.toFixed(1)} segundos<br>
          • Fuentes analizadas: ${results.research_quality_metrics?.data_sources_analyzed || 'N/A'}<br>
          • Resultados de búsqueda: ${results.google_results?.total_results || 0}
        </div>

        <div class="section">
          <h2>🎯 Análisis de Intención</h2>
          <div class="insights-grid">
            <div class="insight-card">
              <h3>Tipo de Intención</h3>
              <p>${results.intent_analysis?.intent_type || 'No disponible'}</p>
            </div>
            <div class="insight-card">
              <h3>Audiencia Objetivo</h3>
              <p>${results.intent_analysis?.target_audience || 'No disponible'}</p>
            </div>
          </div>
        </div>

        <div class="section">
          <h2>❓ Preguntas Frecuentes</h2>
          <div class="questions-list">
            <ul>
              ${results.entities_and_questions?.common_questions?.slice(0, 8).map((q: string) => `<li>${q}</li>`).join('') || '<li>No se encontraron preguntas</li>'}
            </ul>
          </div>
        </div>

        <div class="section">
          <h2>💡 Oportunidades de Contenido</h2>
          <div class="opportunities">
            <h3>Gaps de Contenido Identificados:</h3>
            <ul>
              ${results.content_opportunities?.content_gaps?.slice(0, 5).map((gap: string) => `<li>${gap}</li>`).join('') || '<li>No se identificaron gaps específicos</li>'}
            </ul>

            <h3>Palabras Clave Objetivo:</h3>
            <p>${results.content_opportunities?.target_keywords?.slice(0, 10).join(', ') || 'No disponibles'}</p>

            <h3>Puntuación de Oportunidad:</h3>
            <p><strong>${results.content_opportunities?.opportunity_score || 0}/100</strong></p>
          </div>
        </div>

        <div class="section">
          <h2>📋 Resumen Ejecutivo</h2>
          <div class="insight-card">
            <h3>Insights Clave:</h3>
            <ul>
              ${results.research_summary?.key_insights?.map((insight: string) => `<li>${insight}</li>`).join('') || '<li>No disponibles</li>'}
            </ul>

            <h3>Próximos Pasos Recomendados:</h3>
            <ul>
              ${results.research_summary?.next_steps?.map((step: string) => `<li>${step}</li>`).join('') || '<li>No disponibles</li>'}
            </ul>
          </div>
        </div>

        <div class="footer">
          <p>📊 Generado por SEO & GPT Optimizer™ - Emma Studio</p>
          <p>Este reporte contiene análisis basado en IA y datos de múltiples fuentes</p>
        </div>
      </body>
      </html>
    `;
  };

  const handleShareResults = async () => {
    if (researchResults && navigator.share) {
      try {
        await navigator.share({
          title: `Investigación: ${researchResults.topic}`,
          text: `Resultados de investigación para "${researchResults.topic}" - Confianza: ${(researchConfidence * 100).toFixed(1)}%`,
          url: window.location.href
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    }
  };

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleBack}
                  className="p-2 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600" />
                </button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Research Engine</h1>
                  <p className="text-gray-600 text-sm">
                    Investiga temas y encuentra oportunidades de contenido
                  </p>
                </div>
              </div>

              {hasResults && (
                <div className="flex items-center gap-3">
                  <button
                    onClick={handleSaveResearch}
                    className={`flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-200 ${
                      isSaved
                        ? 'bg-green-100 text-green-700 border border-green-200'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {isSaved ? (
                      <>
                        <Check className="w-4 h-4" />
                        Guardado
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4" />
                        Guardar
                      </>
                    )}
                  </button>

                  <button
                    onClick={handleExportResults}
                    disabled={isExporting}
                    className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-xl transition-colors duration-200 disabled:opacity-50"
                  >
                    <Download className="w-4 h-4" />
                    {isExporting ? 'Descargando...' : 'Descargar'}
                  </button>
                  
                  {navigator.share && (
                    <button
                      onClick={handleShareResults}
                      className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                    >
                      <Share2 className="w-4 h-4" />
                      Compartir
                    </button>
                  )}
                  
                  <button
                    onClick={handleNewResearch}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
                  >
                    Nueva Investigación
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Error Display */}
          {error.hasError && (
            <motion.div
              className="bg-red-50 border border-red-200 rounded-2xl p-6 mb-8"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <span className="text-red-600 font-bold">!</span>
                </div>
                <div>
                  <h3 className="font-semibold text-red-900">Error en la investigación</h3>
                  <p className="text-red-700 text-sm mt-1">{error.message}</p>
                </div>
                <button
                  onClick={clearError}
                  className="ml-auto text-red-600 hover:text-red-700"
                >
                  ×
                </button>
              </div>
            </motion.div>
          )}

          {/* Loading State */}
          {loading.isLoading && (
            <motion.div
              className="bg-white rounded-2xl shadow-sm border border-gray-100 p-12 text-center mb-8"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
            >
              <LoadingSpinner
                size="lg"
                message={loading.message}
                progress={loading.progress}
                showProgress={true}
              />
              
              <div className="mt-6 space-y-2">
                <div className="text-sm text-gray-600">
                  Esto puede tomar entre 30-60 segundos
                </div>
                <div className="text-xs text-gray-500">
                  Analizando múltiples fuentes para obtener los mejores insights
                </div>
              </div>
            </motion.div>
          )}

          {/* Research Form */}
          {!hasResults && !loading.isLoading && (
            <div className="max-w-2xl mx-auto">
              <ResearchForm
                onSubmit={conductResearch}
                loading={loading.isLoading}
              />
              
              {/* Tips */}
              <motion.div
                className="mt-8 bg-white rounded-2xl shadow-sm border border-gray-100 p-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <h3 className="font-semibold text-gray-900 mb-4">
                  💡 Tips para mejores resultados
                </h3>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <strong>Sé específico:</strong> En lugar de "marketing", usa "marketing digital para pequeñas empresas"
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <strong>Incluye contexto:</strong> "beneficios del yoga para principiantes" es mejor que solo "yoga"
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <strong>Piensa en tu audiencia:</strong> Considera qué buscarían tus usuarios objetivo
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          )}

          {/* Research Results */}
          {hasResults && researchResults && !loading.isLoading && (
            <ResearchResults
              results={researchResults}
              confidence={researchConfidence}
              processingTime={processingTime}
            />
          )}

          {/* Success Summary */}
          {hasResults && !loading.isLoading && (
            <motion.div
              className="mt-8 bg-green-50 border border-green-200 rounded-2xl p-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 font-bold">✓</span>
                </div>
                <div>
                  <h3 className="font-semibold text-green-900">
                    Investigación completada exitosamente
                  </h3>
                  <p className="text-green-700 text-sm mt-1">
                    Procesado en {processingTime.toFixed(1)}s con {(researchConfidence * 100).toFixed(1)}% de confianza
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default ResearchPage;

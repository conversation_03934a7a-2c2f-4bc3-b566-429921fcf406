/**
 * SEO & GPT Optimizer™ - Saved Research Page
 * Page for viewing saved research investigations
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Search, Calendar, Download, Trash2, Eye } from 'lucide-react';
import { useLocation } from 'wouter';
import { useSavedResearch, SavedResearch } from '../../hooks/seo-gpt-optimizer/useSavedResearch';

const SavedResearchPage: React.FC = () => {
  const [, setLocation] = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedResearch, setSelectedResearch] = useState<SavedResearch | null>(null);

  const {
    savedResearch,
    isLoading,
    deleteResearch,
    searchResearch
  } = useSavedResearch();

  const handleBack = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer');
  };

  const handleDeleteResearch = (id: string) => {
    deleteResearch(id);

    if (selectedResearch?.id === id) {
      setSelectedResearch(null);
    }
  };

  const handleViewResearch = (research: SavedResearch) => {
    setSelectedResearch(research);
  };

  const handleExportResearch = (research: SavedResearch) => {
    try {
      // Generar contenido HTML estructurado
      const htmlContent = generatePDFContent(research.results, research.confidence, research.processingTime);

      // Crear blob con el contenido HTML
      const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
      const url = URL.createObjectURL(blob);

      // Crear enlace de descarga
      const link = document.createElement('a');
      link.href = url;
      link.download = `investigacion-${research.topic.replace(/[^a-zA-Z0-9]/g, '-')}-${Date.now()}.html`;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Limpiar URL
      URL.revokeObjectURL(url);

    } catch (error) {
      console.error('Error al exportar:', error);
    }
  };

  const generatePDFContent = (results: any, confidence: number, time: number) => {
    // Misma función que en research-page.tsx
    const currentDate = new Date().toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Investigación: ${results.topic}</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
          }
          .download-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
          }
          .download-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
          }
          .download-info p {
            margin: 5px 0;
            font-size: 14px;
            color: #555;
          }
          .print-button {
            background: #3018ef;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
          }
          .print-button:hover {
            background: #2516d6;
          }
          .header {
            text-align: center;
            border-bottom: 3px solid #3018ef;
            padding-bottom: 20px;
            margin-bottom: 30px;
          }
          .header h1 {
            color: #3018ef;
            margin: 0;
            font-size: 28px;
          }
          .meta-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
          }
          .section {
            margin-bottom: 30px;
          }
          .section h2 {
            color: #3018ef;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 8px;
            margin-bottom: 15px;
          }
          .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #666;
            font-size: 12px;
          }
        </style>
      </head>
      <body>
        <div class="download-info">
          <h3>📄 Reporte de Investigación Descargado</h3>
          <p>Haz clic en el botón para descargar como PDF:</p>
          <button class="print-button" onclick="window.print()">📄 Descargar como PDF</button>
          <p style="font-size: 12px; margin-top: 10px;">O usa: Ctrl+P (Cmd+P en Mac) → Guardar como PDF</p>
        </div>

        <script>
          // Auto-hide download info when printing
          window.addEventListener('beforeprint', function() {
            document.querySelector('.download-info').style.display = 'none';
          });
          window.addEventListener('afterprint', function() {
            document.querySelector('.download-info').style.display = 'block';
          });
        </script>

        <div class="header">
          <h1>📊 Investigación Guardada</h1>
          <div>Tema: <strong>${results.topic}</strong></div>
          <div>Exportado el ${currentDate}</div>
        </div>
        <div class="meta-info">
          <strong>Métricas:</strong><br>
          • Confianza: ${(confidence * 100).toFixed(1)}%<br>
          • Tiempo de procesamiento: ${time.toFixed(1)}s
        </div>
        <div class="footer">
          <p>📊 Generado por SEO & GPT Optimizer™ - Emma Studio</p>
        </div>
      </body>
      </html>
    `;
  };

  const filteredResearch = searchQuery.trim()
    ? searchResearch(searchQuery)
    : savedResearch;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={handleBack}
                className="p-2 hover:bg-gray-100 rounded-xl transition-colors duration-200"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Investigaciones Guardadas</h1>
                <p className="text-gray-600 text-sm">
                  Revisa y gestiona tus investigaciones guardadas
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Bar */}
        <div className="mb-8">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Buscar investigaciones..."
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl bg-white text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Loading State */}
        {isLoading ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
              <Search className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Cargando investigaciones...
            </h3>
          </div>
        ) : filteredResearch.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery ? 'No se encontraron investigaciones' : 'No hay investigaciones guardadas'}
            </h3>
            <p className="text-gray-600">
              {searchQuery ? 'Intenta con otros términos de búsqueda' : 'Guarda investigaciones desde el Research Engine para verlas aquí'}
            </p>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredResearch.map((research) => (
              <motion.div
                key={research.id}
                className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <div className="flex items-start justify-between mb-4">
                  <h3 className="font-semibold text-gray-900 text-lg line-clamp-2">
                    {research.topic}
                  </h3>
                </div>

                <div className="space-y-2 mb-4 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    {formatDate(research.savedAt)}
                  </div>
                  <div>
                    Confianza: {(research.confidence * 100).toFixed(1)}%
                  </div>
                  <div>
                    Procesamiento: {research.processingTime.toFixed(1)}s
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleViewResearch(research)}
                    className="flex items-center gap-1 px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 text-sm"
                  >
                    <Eye className="w-4 h-4" />
                    Ver
                  </button>
                  <button
                    onClick={() => handleExportResearch(research)}
                    className="flex items-center gap-1 px-3 py-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 text-sm"
                  >
                    <Download className="w-4 h-4" />
                    Descargar
                  </button>
                  <button
                    onClick={() => handleDeleteResearch(research.id)}
                    className="flex items-center gap-1 px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 text-sm"
                  >
                    <Trash2 className="w-4 h-4" />
                    Eliminar
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SavedResearchPage;

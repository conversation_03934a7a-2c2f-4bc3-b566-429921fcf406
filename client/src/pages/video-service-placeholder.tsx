import React from "react";
import { useLocation } from "wouter";
import { ArrowLeft, Construction, Video } from "lucide-react";
import { Button } from "@/components/ui/button";

interface VideoServicePlaceholderProps {
  serviceName: string;
  description: string;
  comingSoon?: boolean;
}

export default function VideoServicePlaceholder({ 
  serviceName, 
  description, 
  comingSoon = true 
}: VideoServicePlaceholderProps) {
  const [, navigate] = useLocation();

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="max-w-2xl mx-auto text-center p-8">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-12 border border-white/20">
          <div className="flex items-center justify-center mb-8">
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-6 rounded-full">
              {comingSoon ? (
                <Construction className="w-16 h-16 text-white" />
              ) : (
                <Video className="w-16 h-16 text-white" />
              )}
            </div>
          </div>
          
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-4">
            {serviceName}
          </h1>
          
          <p className="text-xl text-gray-600 mb-8 leading-relaxed">
            {description}
          </p>
          
          {comingSoon && (
            <div className="bg-gradient-to-r from-purple-100 to-blue-100 rounded-lg p-6 mb-8">
              <h3 className="text-lg font-semibold text-purple-800 mb-2">
                🚀 Próximamente
              </h3>
              <p className="text-purple-700">
                Estamos trabajando en esta increíble funcionalidad. 
                ¡Mantente atento para las actualizaciones!
              </p>
            </div>
          )}
          
          <div className="flex gap-4 justify-center">
            <Button
              onClick={() => navigate("/video-studio")}
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver a Video Studio
            </Button>
            
            <Button
              variant="outline"
              onClick={() => navigate("/dashboard")}
              className="border-purple-200 text-purple-600 hover:bg-purple-50"
            >
              Ir al Dashboard
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

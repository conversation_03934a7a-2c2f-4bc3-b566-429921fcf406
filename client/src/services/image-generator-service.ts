/**
 * Service for image generation using Ideogram AI v3.
 * Based on poster-service.ts but adapted for general image generation without prompt modifications.
 */

import { ensureFullImageUrl } from "./detect-deployment";

const API_BASE_URL = "/api/image-generator";

export interface ImageGeneratorOptions {
  prompt: string;
  resolution?: string;
  aspect_ratio?: string;
  rendering_speed?: "TURBO" | "DEFAULT" | "QUALITY";
  magic_prompt?: "AUTO" | "ON" | "OFF";
  negative_prompt?: string;
  num_images?: number;
  style_type?: "AUTO" | "GENERAL" | "REALISTIC" | "DESIGN";
}

export interface ImageGeneratorResponse {
  success: boolean;
  image_url?: string;
  images?: string[];
  revised_prompt?: string;
  response_id?: string;
  metadata?: any;
  error?: string;
}

export interface ReferenceEditOptions {
  prompt: string;
  referenceImages: File[];
  resolution?: string;
  aspect_ratio?: string;
}

export interface MaskEditOptions {
  prompt: string;
  image: File;
  mask: File;
}

export interface MultiTurnEditOptions {
  previousResponseId: string;
  editPrompt: string;
}

/**
 * Validate image file for upload
 */
export function validateImageFile(file: File): { isValid: boolean; error?: string } {
  // Check file type
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!validTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Tipo de archivo no válido. Use JPEG, PNG o WebP.'
    };
  }

  // Check file size (max 10MB)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'El archivo es demasiado grande. Máximo 10MB.'
    };
  }

  return { isValid: true };
}

/**
 * Generate an image using Ideogram AI v3
 */
export async function generateImage(options: ImageGeneratorOptions): Promise<ImageGeneratorResponse> {
  try {
    const formData = new FormData();
    formData.append("prompt", options.prompt);

    // Add Ideogram v3 specific parameters
    if (options.resolution) {
      formData.append("resolution", options.resolution);
    }
    if (options.aspect_ratio) {
      formData.append("aspect_ratio", options.aspect_ratio);
    }
    if (options.rendering_speed) {
      formData.append("rendering_speed", options.rendering_speed);
    }
    if (options.magic_prompt) {
      formData.append("magic_prompt", options.magic_prompt);
    }
    if (options.negative_prompt) {
      formData.append("negative_prompt", options.negative_prompt);
    }
    if (options.num_images) {
      formData.append("num_images", options.num_images.toString());
    }
    if (options.style_type) {
      formData.append("style_type", options.style_type);
    }

    const response = await fetch(`${API_BASE_URL}/generate`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // Ensure image URLs are properly formatted
    if (result.success && result.image_url) {
      result.image_url = ensureFullImageUrl(result.image_url);
      if (result.images) {
        result.images = result.images.map((url: string) => ensureFullImageUrl(url));
      }
    }

    return result;
  } catch (error) {
    console.error("Error generating image:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido al generar imagen",
    };
  }
}

/**
 * Edit image using reference images
 */
export async function editWithReferences(options: ReferenceEditOptions): Promise<ImageGeneratorResponse> {
  try {
    if (options.referenceImages.length > 4) {
      return {
        success: false,
        error: "Máximo 4 imágenes de referencia permitidas",
      };
    }

    const formData = new FormData();
    formData.append("prompt", options.prompt);
    
    if (options.resolution) {
      formData.append("resolution", options.resolution);
    }
    if (options.aspect_ratio) {
      formData.append("aspect_ratio", options.aspect_ratio);
    }
    
    options.referenceImages.forEach((file, index) => {
      formData.append("reference_images", file);
    });

    const response = await fetch(`${API_BASE_URL}/edit-with-references`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Ensure image URLs are properly formatted
    if (result.success && result.image_url) {
      result.image_url = ensureFullImageUrl(result.image_url);
      if (result.images) {
        result.images = result.images.map((url: string) => ensureFullImageUrl(url));
      }
    }

    return result;
  } catch (error) {
    console.error("Error editing with references:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido al editar con referencias",
    };
  }
}

/**
 * Edit image using mask
 */
export async function editWithMask(options: MaskEditOptions): Promise<ImageGeneratorResponse> {
  try {
    const formData = new FormData();
    formData.append("prompt", options.prompt);
    formData.append("image", options.image);
    formData.append("mask", options.mask);

    const response = await fetch(`${API_BASE_URL}/edit-with-mask`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Ensure image URLs are properly formatted
    if (result.success && result.image_url) {
      result.image_url = ensureFullImageUrl(result.image_url);
      if (result.images) {
        result.images = result.images.map((url: string) => ensureFullImageUrl(url));
      }
    }

    return result;
  } catch (error) {
    console.error("Error editing with mask:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido al editar con máscara",
    };
  }
}

/**
 * Multi-turn edit (generate new image based on previous)
 */
export async function multiTurnEdit(options: MultiTurnEditOptions): Promise<ImageGeneratorResponse> {
  try {
    const formData = new FormData();
    formData.append("previous_response_id", options.previousResponseId);
    formData.append("edit_prompt", options.editPrompt);

    const response = await fetch(`${API_BASE_URL}/multi-turn-edit`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Ensure image URLs are properly formatted
    if (result.success && result.image_url) {
      result.image_url = ensureFullImageUrl(result.image_url);
      if (result.images) {
        result.images = result.images.map((url: string) => ensureFullImageUrl(url));
      }
    }

    return result;
  } catch (error) {
    console.error("Error in multi-turn edit:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido en edición multi-turno",
    };
  }
}

/**
 * Download image from URL
 */
export async function downloadImage(imageUrl: string, filename: string = "image"): Promise<void> {
  try {
    const response = await fetch(imageUrl);
    const blob = await response.blob();
    
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Error downloading image:", error);
    throw new Error("Error al descargar la imagen");
  }
}

/**
 * Copy image to clipboard
 */
export async function copyImageToClipboard(imageUrl: string): Promise<void> {
  try {
    const response = await fetch(imageUrl);
    const blob = await response.blob();
    
    await navigator.clipboard.write([
      new ClipboardItem({
        [blob.type]: blob
      })
    ]);
  } catch (error) {
    console.error("Error copying image to clipboard:", error);
    throw new Error("Error al copiar imagen al portapapeles");
  }
}

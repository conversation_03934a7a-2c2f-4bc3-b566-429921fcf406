/**
 * Service for influencer generation and management operations
 */

const API_BASE_URL = "/api/influencers";

export interface TestimonialGenerationOptions {
  product_name: string;
  testimonial_text: string;
  influencer_style?: "lifestyle" | "fitness" | "beauty" | "tech" | "fashion" | "travel";
  resolution?: string;
  aspect_ratio?: string;
  rendering_speed?: "TURBO" | "DEFAULT" | "QUALITY";
  magic_prompt?: "AUTO" | "ON" | "OFF";
  negative_prompt?: string;
  num_images?: number;
  style_type?: "AUTO" | "GENERAL" | "REALISTIC" | "DESIGN";
}

export interface ProductPlacementOptions {
  product_name: string;
  placement_context: string;
  influencer_style?: "lifestyle" | "fitness" | "beauty" | "tech" | "fashion" | "travel";
  resolution?: string;
  aspect_ratio?: string;
  rendering_speed?: "TURBO" | "DEFAULT" | "QUALITY";
  magic_prompt?: "AUTO" | "ON" | "OFF";
  negative_prompt?: string;
  num_images?: number;
  style_type?: "AUTO" | "GENERAL" | "REALISTIC" | "DESIGN";
}

export interface InfluencerResponse {
  success: boolean;
  image_url?: string;
  images?: string[];
  revised_prompt?: string;
  response_id?: string;
  metadata?: any;
  error?: string;
}

export interface ReferenceEditOptions {
  prompt: string;
  referenceImages: File[];
  resolution?: string;
  aspect_ratio?: string;
}

export interface InfluencerProfile {
  id: string;
  user_id: string;
  name: string;
  style: string;
  description: string;
  image_url: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

export interface InfluencerGalleryResponse {
  success: boolean;
  influencers: InfluencerProfile[];
  total_count: number;
  error?: string;
}

export interface SaveInfluencerOptions {
  name: string;
  style: string;
  description: string;
  image_url: string;
  user_id: string;
  metadata?: any;
}

export interface SaveInfluencerResponse {
  success: boolean;
  influencer_id?: string;
  message?: string;
  error?: string;
}

class InfluencerService {
  /**
   * Generate a testimonial with a virtual influencer
   */
  async generateTestimonial(options: TestimonialGenerationOptions): Promise<InfluencerResponse> {
    try {
      const formData = new FormData();
      formData.append("product_name", options.product_name);
      formData.append("testimonial_text", options.testimonial_text);
      
      if (options.influencer_style) {
        formData.append("influencer_style", options.influencer_style);
      }
      if (options.resolution) {
        formData.append("resolution", options.resolution);
      }
      if (options.aspect_ratio) {
        formData.append("aspect_ratio", options.aspect_ratio);
      }
      if (options.rendering_speed) {
        formData.append("rendering_speed", options.rendering_speed);
      }
      if (options.magic_prompt) {
        formData.append("magic_prompt", options.magic_prompt);
      }
      if (options.negative_prompt) {
        formData.append("negative_prompt", options.negative_prompt);
      }
      if (options.num_images) {
        formData.append("num_images", options.num_images.toString());
      }
      if (options.style_type) {
        formData.append("style_type", options.style_type);
      }

      const response = await fetch(`${API_BASE_URL}/generate-testimonial`, {
        method: "POST",
        body: formData,
        headers: {
          "X-API-Key": "emma-studio-api-key-2024",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error generating testimonial:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Generate product placement content with a virtual influencer
   */
  async generateProductPlacement(options: ProductPlacementOptions): Promise<InfluencerResponse> {
    try {
      const formData = new FormData();
      formData.append("product_name", options.product_name);
      formData.append("placement_context", options.placement_context);
      
      if (options.influencer_style) {
        formData.append("influencer_style", options.influencer_style);
      }
      if (options.resolution) {
        formData.append("resolution", options.resolution);
      }
      if (options.aspect_ratio) {
        formData.append("aspect_ratio", options.aspect_ratio);
      }
      if (options.rendering_speed) {
        formData.append("rendering_speed", options.rendering_speed);
      }
      if (options.magic_prompt) {
        formData.append("magic_prompt", options.magic_prompt);
      }
      if (options.negative_prompt) {
        formData.append("negative_prompt", options.negative_prompt);
      }
      if (options.num_images) {
        formData.append("num_images", options.num_images.toString());
      }
      if (options.style_type) {
        formData.append("style_type", options.style_type);
      }

      const response = await fetch(`${API_BASE_URL}/generate-product-placement`, {
        method: "POST",
        body: formData,
        headers: {
          "X-API-Key": "emma-studio-api-key-2024",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error generating product placement:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Edit influencer content using reference images
   */
  async editWithReferences(options: ReferenceEditOptions): Promise<InfluencerResponse> {
    try {
      const formData = new FormData();
      formData.append("prompt", options.prompt);

      // Add reference images
      options.referenceImages.forEach((file, index) => {
        formData.append("reference_images", file);
      });

      if (options.resolution) {
        formData.append("resolution", options.resolution);
      }
      if (options.aspect_ratio) {
        formData.append("aspect_ratio", options.aspect_ratio);
      }

      const response = await fetch(`${API_BASE_URL}/edit-with-references`, {
        method: "POST",
        body: formData,
        headers: {
          "X-API-Key": "emma-studio-api-key-2024",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error editing with references:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Get user's influencer gallery
   */
  async getUserInfluencers(userId: string, limit: number = 20, offset: number = 0): Promise<InfluencerGalleryResponse> {
    try {
      const params = new URLSearchParams({
        user_id: userId,
        limit: limit.toString(),
        offset: offset.toString(),
      });

      const response = await fetch(`${API_BASE_URL}/gallery?${params}`, {
        method: "GET",
        headers: {
          "X-API-Key": "emma-studio-api-key-2024",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error getting user influencers:", error);
      return {
        success: false,
        influencers: [],
        total_count: 0,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Save an influencer to user's gallery
   */
  async saveInfluencer(options: SaveInfluencerOptions): Promise<SaveInfluencerResponse> {
    try {
      const formData = new FormData();
      formData.append("name", options.name);
      formData.append("style", options.style);
      formData.append("description", options.description);
      formData.append("image_url", options.image_url);
      formData.append("user_id", options.user_id);
      
      if (options.metadata) {
        formData.append("metadata", JSON.stringify(options.metadata));
      }

      const response = await fetch(`${API_BASE_URL}/save`, {
        method: "POST",
        body: formData,
        headers: {
          "X-API-Key": "emma-studio-api-key-2024",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error saving influencer:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Stream testimonial generation with progress updates
   */
  async streamTestimonialGeneration(
    options: TestimonialGenerationOptions,
    onProgress: (data: any) => void
  ): Promise<void> {
    try {
      const formData = new FormData();
      formData.append("product_name", options.product_name);
      formData.append("testimonial_text", options.testimonial_text);
      
      if (options.influencer_style) {
        formData.append("influencer_style", options.influencer_style);
      }
      if (options.resolution) {
        formData.append("resolution", options.resolution);
      }
      if (options.aspect_ratio) {
        formData.append("aspect_ratio", options.aspect_ratio);
      }

      const response = await fetch(`${API_BASE_URL}/stream-generate-testimonial`, {
        method: "POST",
        body: formData,
        headers: {
          "X-API-Key": "emma-studio-api-key-2024",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("No response body reader available");
      }

      const decoder = new TextDecoder();
      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const data = JSON.parse(line.slice(6));
              onProgress(data);
            } catch (e) {
              console.warn("Failed to parse SSE data:", line);
            }
          }
        }
      }
    } catch (error) {
      console.error("Error in stream generation:", error);
      onProgress({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      });
    }
  }
}

// Export singleton instance
export const influencerService = new InfluencerService();

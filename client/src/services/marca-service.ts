import { supabase, Marca, CreateMarcaData, UpdateMarcaData } from '@/lib/supabase'

// Verificar si Supabase está disponible
const isSupabaseAvailable = () => {
  try {
    return import.meta.env.VITE_SUPABASE_URL &&
           import.meta.env.VITE_SUPABASE_URL !== 'https://placeholder.supabase.co'
  } catch {
    return false
  }
}

export class MarcaService {
  
  /**
   * Obtener todos los marcas del usuario
   */
  static async getMarcas(userId?: string): Promise<Marca[]> {
    // Si Supabase no está disponible, devolver datos mock
    if (!isSupabaseAvailable()) {
      console.warn('Supabase not available, returning mock data')
      return []
    }

    try {
      let query = supabase
        .from('marcas')
        .select('*')
        .order('updated_at', { ascending: false })

      if (userId) {
        query = query.eq('user_id', userId)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching marcas:', error)
        throw new Error(`Error al obtener marcas: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('Error in getMarcas:', error)
      throw error
    }
  }

  /**
   * Obtener un marca por ID
   */
  static async getMarcaById(id: string): Promise<Marca | null> {
    try {
      const { data, error } = await supabase
        .from('marcas')
        .select('*')
        .eq('id', id)
        .single()

      if (error) {
        console.error('Error fetching marca:', error)
        throw new Error(`Error al obtener marca: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error('Error in getMarcaById:', error)
      throw error
    }
  }

  /**
   * Crear un nuevo marca
   */
  static async createMarca(marcaData: CreateMarcaData): Promise<Marca> {
    try {
      const newMarca = {
        ...marcaData,
        status: 'draft' as const,
        campaigns_count: 0,
        assets_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      const { data, error } = await supabase
        .from('marcas')
        .insert([newMarca])
        .select()
        .single()

      if (error) {
        console.error('Error creating marca:', error)
        throw new Error(`Error al crear marca: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error('Error in createMarca:', error)
      throw error
    }
  }

  /**
   * Actualizar un marca existente
   */
  static async updateMarca(marcaData: UpdateMarcaData): Promise<Marca> {
    try {
      const { id, ...updateData } = marcaData
      
      const updatedMarca = {
        ...updateData,
        updated_at: new Date().toISOString()
      }

      const { data, error } = await supabase
        .from('marcas')
        .update(updatedMarca)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Error updating marca:', error)
        throw new Error(`Error al actualizar marca: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error('Error in updateMarca:', error)
      throw error
    }
  }

  /**
   * Eliminar un marca
   */
  static async deleteMarca(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('marcas')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Error deleting marca:', error)
        throw new Error(`Error al eliminar marca: ${error.message}`)
      }
    } catch (error) {
      console.error('Error in deleteMarca:', error)
      throw error
    }
  }

  /**
   * Duplicar un marca
   */
  static async duplicateMarca(id: string): Promise<Marca> {
    try {
      // Obtener el marca original
      const original = await this.getMarcaById(id)
      if (!original) {
        throw new Error('Marca no encontrado')
      }

      // Crear una copia con nuevo nombre
      const duplicateData: CreateMarcaData = {
        brand_name: `${original.brand_name} (Copia)`,
        website: original.website,
        industry: original.industry,
        logo_url: original.logo_url,
        primary_color: original.primary_color,
        secondary_color: original.secondary_color,
        target_audience: original.target_audience,
        tone: original.tone,
        personality: original.personality,
        description: original.description,
        unique_value: original.unique_value,
        competitors: original.competitors,
        documents: original.documents,
        examples: original.examples,
        user_id: original.user_id
      }

      return await this.createMarca(duplicateData)
    } catch (error) {
      console.error('Error in duplicateMarca:', error)
      throw error
    }
  }

  /**
   * Cambiar el estado de un marca
   */
  static async updateMarcaStatus(id: string, status: 'draft' | 'active' | 'archived'): Promise<Marca> {
    try {
      const { data, error } = await supabase
        .from('marcas')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Error updating marca status:', error)
        throw new Error(`Error al actualizar estado: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error('Error in updateMarcaStatus:', error)
      throw error
    }
  }

  /**
   * Buscar marcas por texto
   */
  static async searchMarcas(searchTerm: string, userId?: string): Promise<Marca[]> {
    try {
      let query = supabase
        .from('marcas')
        .select('*')
        .or(`brand_name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,industry.ilike.%${searchTerm}%`)
        .order('updated_at', { ascending: false })

      if (userId) {
        query = query.eq('user_id', userId)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error searching marcas:', error)
        throw new Error(`Error en búsqueda: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('Error in searchMarcas:', error)
      throw error
    }
  }

  /**
   * Obtener estadísticas de marcas
   */
  static async getMarcasStats(userId?: string): Promise<{
    total: number
    active: number
    draft: number
    archived: number
    totalCampaigns: number
    totalAssets: number
  }> {
    // Si Supabase no está disponible, devolver stats mock
    if (!isSupabaseAvailable()) {
      console.warn('Supabase not available, returning mock stats')
      return {
        total: 0,
        active: 0,
        draft: 0,
        archived: 0,
        totalCampaigns: 0,
        totalAssets: 0
      }
    }

    try {
      let query = supabase
        .from('marcas')
        .select('status, campaigns_count, assets_count')

      if (userId) {
        query = query.eq('user_id', userId)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching stats:', error)
        throw new Error(`Error al obtener estadísticas: ${error.message}`)
      }

      const stats = {
        total: data?.length || 0,
        active: data?.filter(n => n.status === 'active').length || 0,
        draft: data?.filter(n => n.status === 'draft').length || 0,
        archived: data?.filter(n => n.status === 'archived').length || 0,
        totalCampaigns: data?.reduce((sum, n) => sum + (n.campaigns_count || 0), 0) || 0,
        totalAssets: data?.reduce((sum, n) => sum + (n.assets_count || 0), 0) || 0
      }

      return stats
    } catch (error) {
      console.error('Error in getMarcasStats:', error)
      throw error
    }
  }
}

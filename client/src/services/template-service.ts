/**
 * Service for managing ad templates
 */

export interface AdTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  platform: string;
  width: number;
  height: number;
  thumbnail: string;
  fileUrl: string;
  tags: string[];
  isFavorite: boolean;
  createdAt: string;
  fileType: 'image' | 'svg' | 'json';
  polotnoData?: any; // Para plantillas de Polotno
}

export interface TemplateUploadData {
  name: string;
  description: string;
  category: string;
  platform: string;
  tags: string[];
  file: File;
}

// Simulación de base de datos local (en producción esto sería una API)
const STORAGE_KEY = 'emma_ad_templates';

class TemplateService {
  private templates: AdTemplate[] = [];

  constructor() {
    this.loadTemplates();
  }

  // Cargar plantillas desde localStorage
  private loadTemplates(): void {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        this.templates = JSON.parse(stored);
      } else {
        // Cargar plantillas de ejemplo si no hay ninguna
        this.templates = this.getDefaultTemplates();
        this.saveTemplates();
      }
    } catch (error) {
      console.error('Error loading templates:', error);
      this.templates = this.getDefaultTemplates();
    }
  }

  // Guardar plantillas en localStorage
  private saveTemplates(): void {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.templates));
    } catch (error) {
      console.error('Error saving templates:', error);
    }
  }

  // Plantillas predefinidas globales
  private getDefaultTemplates(): AdTemplate[] {
    return [
      // PLANTILLAS ENFOQUE DE PRODUCTO (las que subiste)
      ...this.getProductFocusedTemplates(),

      // Plantillas de ejemplo originales
      {
        id: "default-facebook-1",
        name: "Facebook Ad - Producto",
        description: "Plantilla profesional para anuncio de producto en Facebook",
        category: "facebook",
        platform: "facebook",
        width: 1200,
        height: 630,
        thumbnail: "/api/placeholder/300/200",
        fileUrl: "/templates/facebook-product.json",
        tags: ["producto", "venta", "promoción", "facebook"],
        isFavorite: true,
        createdAt: "2024-01-15",
        fileType: "json",
        polotnoData: {
          width: 1200,
          height: 630,
          elements: [
            {
              type: "rect",
              x: 0,
              y: 0,
              width: 1200,
              height: 630,
              fill: "#3b82f6",
            },
            {
              type: "text",
              x: 600,
              y: 200,
              width: 800,
              height: 80,
              text: "PRODUCTO INCREÍBLE",
              fontSize: 48,
              fontFamily: "Arial",
              fill: "#ffffff",
              align: "center",
            },
            {
              type: "text",
              x: 600,
              y: 350,
              width: 800,
              height: 60,
              text: "Descubre la innovación que cambiará tu vida",
              fontSize: 24,
              fontFamily: "Arial",
              fill: "#ffffff",
              align: "center",
            }
          ]
        }
      },
      {
        id: "default-instagram-1",
        name: "Instagram Post - Oferta",
        description: "Plantilla cuadrada para ofertas especiales en Instagram",
        category: "instagram",
        platform: "instagram",
        width: 1080,
        height: 1080,
        thumbnail: "/api/placeholder/300/300",
        fileUrl: "/templates/instagram-offer.json",
        tags: ["oferta", "descuento", "instagram", "cuadrado"],
        isFavorite: false,
        createdAt: "2024-01-14",
        fileType: "json",
        polotnoData: {
          width: 1080,
          height: 1080,
          elements: [
            {
              type: "rect",
              x: 0,
              y: 0,
              width: 1080,
              height: 1080,
              fill: "#ec4899",
            },
            {
              type: "text",
              x: 540,
              y: 400,
              width: 900,
              height: 100,
              text: "50% OFF",
              fontSize: 72,
              fontFamily: "Impact",
              fill: "#ffffff",
              align: "center",
            },
            {
              type: "text",
              x: 540,
              y: 550,
              width: 900,
              height: 60,
              text: "OFERTA LIMITADA",
              fontSize: 32,
              fontFamily: "Arial",
              fill: "#ffffff",
              align: "center",
            }
          ]
        }
      },
      {
        id: "default-google-1",
        name: "Google Ads - Banner",
        description: "Banner horizontal para Google Ads",
        category: "google",
        platform: "google",
        width: 728,
        height: 90,
        thumbnail: "/api/placeholder/300/100",
        fileUrl: "/templates/google-banner.json",
        tags: ["banner", "google", "horizontal", "ads"],
        isFavorite: false,
        createdAt: "2024-01-13",
        fileType: "json",
        polotnoData: {
          width: 728,
          height: 90,
          elements: [
            {
              type: "rect",
              x: 0,
              y: 0,
              width: 728,
              height: 90,
              fill: "#10b981",
            },
            {
              type: "text",
              x: 364,
              y: 45,
              width: 600,
              height: 40,
              text: "Tu Anuncio Aquí - Haz Clic",
              fontSize: 18,
              fontFamily: "Arial",
              fill: "#ffffff",
              align: "center",
            }
          ]
        }
      }
    ];
  }

  // Plantillas de Enfoque de Producto (las que subiste)
  private getProductFocusedTemplates(): AdTemplate[] {
    // Obtener plantillas del localStorage del usuario y convertirlas a globales
    try {
      const stored = localStorage.getItem('emma_ad_templates');
      if (stored) {
        const userTemplates = JSON.parse(stored);
        const productFocusedTemplates = userTemplates
          .filter((template: any) => !template.id.startsWith('default-'))
          .map((template: any, index: number) => ({
            id: `global-product-${index + 1}`,
            name: template.name,
            description: "Plantilla profesional con enfoque de producto",
            category: "enfoque-producto",
            platform: "enfoque-producto",
            width: template.width || 1080,
            height: template.height || 1080,
            thumbnail: template.thumbnail || "/api/placeholder/300/200",
            fileUrl: template.fileUrl,
            tags: ["producto", "enfoque", "profesional"],
            isFavorite: false,
            createdAt: "2024-01-15",
            fileType: template.fileType || "image",
            polotnoData: template.polotnoData
          }));

        return productFocusedTemplates;
      }
    } catch (error) {
      console.log('No hay plantillas de usuario para convertir a globales');
    }

    return [];
  }

  // Obtener todas las plantillas (globales + personales del usuario)
  getAllTemplates(): AdTemplate[] {
    const globalTemplates = this.getDefaultTemplates();

    // Solo plantillas personales nuevas (no las que ya se convirtieron a globales)
    const userTemplates = this.templates.filter(t =>
      !t.id.startsWith('default-') &&
      !t.id.startsWith('global-')
    );

    return [...globalTemplates, ...userTemplates];
  }

  // Obtener plantilla por ID
  getTemplateById(id: string): AdTemplate | null {
    return this.templates.find(template => template.id === id) || null;
  }

  // Filtrar plantillas
  filterTemplates(filters: {
    category?: string;
    platform?: string;
    search?: string;
    favorites?: boolean;
  }): AdTemplate[] {
    let filtered = [...this.templates];

    if (filters.category && filters.category !== 'all') {
      filtered = filtered.filter(t => t.category === filters.category);
    }

    if (filters.platform && filters.platform !== 'all') {
      filtered = filtered.filter(t => t.platform === filters.platform);
    }

    if (filters.search) {
      const search = filters.search.toLowerCase();
      filtered = filtered.filter(t => 
        t.name.toLowerCase().includes(search) ||
        t.description.toLowerCase().includes(search) ||
        t.tags.some(tag => tag.toLowerCase().includes(search))
      );
    }

    if (filters.favorites) {
      filtered = filtered.filter(t => t.isFavorite);
    }

    return filtered;
  }

  // Agregar nueva plantilla
  async addTemplate(data: TemplateUploadData): Promise<AdTemplate> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const result = e.target?.result as string;
          
          // Detectar dimensiones automáticamente si es una imagen
          let width = 1080;
          let height = 1080;
          
          // Para archivos JSON de Polotno, intentar extraer dimensiones
          if (data.file.type === 'application/json') {
            try {
              const jsonData = JSON.parse(result);
              if (jsonData.width && jsonData.height) {
                width = jsonData.width;
                height = jsonData.height;
              }
            } catch (jsonError) {
              console.warn('Could not parse JSON for dimensions');
            }
          }

          const newTemplate: AdTemplate = {
            id: `template_${Date.now()}`,
            name: data.name || data.file.name,
            description: data.description,
            category: data.category,
            platform: data.platform,
            width,
            height,
            thumbnail: data.file.type.startsWith('image/') ? result : "/api/placeholder/300/200",
            fileUrl: result,
            tags: data.tags,
            isFavorite: false,
            createdAt: new Date().toISOString().split('T')[0],
            fileType: data.file.type === 'application/json' ? 'json' : 
                     data.file.type === 'image/svg+xml' ? 'svg' : 'image',
            polotnoData: data.file.type === 'application/json' ? 
                        JSON.parse(result) : undefined
          };

          this.templates.unshift(newTemplate);
          this.saveTemplates();
          resolve(newTemplate);
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = () => reject(new Error('Error reading file'));
      reader.readAsDataURL(data.file);
    });
  }

  // Eliminar plantilla
  deleteTemplate(id: string): boolean {
    const index = this.templates.findIndex(t => t.id === id);
    if (index !== -1) {
      this.templates.splice(index, 1);
      this.saveTemplates();
      return true;
    }
    return false;
  }

  // Alternar favorito
  toggleFavorite(id: string): boolean {
    const template = this.templates.find(t => t.id === id);
    if (template) {
      template.isFavorite = !template.isFavorite;
      this.saveTemplates();
      return template.isFavorite;
    }
    return false;
  }

  // Actualizar plantilla
  updateTemplate(id: string, updates: Partial<AdTemplate>): AdTemplate | null {
    const template = this.templates.find(t => t.id === id);
    if (template) {
      Object.assign(template, updates);
      this.saveTemplates();
      return template;
    }
    return null;
  }

  // Obtener estadísticas
  getStats() {
    return {
      total: this.templates.length,
      favorites: this.templates.filter(t => t.isFavorite).length,
      byCategory: this.templates.reduce((acc, template) => {
        acc[template.category] = (acc[template.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      byPlatform: this.templates.reduce((acc, template) => {
        acc[template.platform] = (acc[template.platform] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };
  }
}

// Instancia singleton del servicio
export const templateService = new TemplateService();

// Funciones de utilidad para usar en componentes
export const useTemplates = () => {
  return {
    getAllTemplates: () => templateService.getAllTemplates(),
    getTemplateById: (id: string) => templateService.getTemplateById(id),
    filterTemplates: (filters: any) => templateService.filterTemplates(filters),
    addTemplate: (data: TemplateUploadData) => templateService.addTemplate(data),
    deleteTemplate: (id: string) => templateService.deleteTemplate(id),
    toggleFavorite: (id: string) => templateService.toggleFavorite(id),
    updateTemplate: (id: string, updates: Partial<AdTemplate>) => templateService.updateTemplate(id, updates),
    getStats: () => templateService.getStats(),
  };
};

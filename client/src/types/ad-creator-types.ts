/**
 * Unified Ad Creator Type Definitions
 * Centralized types for the ad creator system to eliminate duplication
 */

export interface GeneratedAd {
  id: string;
  image_url: string;
  prompt: string;
  revised_prompt?: string;
  response_id?: string;
  metadata?: AdMetadata;
  timestamp: number;
}

export interface AdMetadata {
  platform?: string;
  size?: string;
  style?: string;
  model?: string;
  usedProductImages?: boolean;
  productImageCount?: number;
  reference_count?: number;
  used_references?: boolean;
  generated_at?: string;
  originalPrompt?: string;
  enhanced_prompt?: string;
  type?: string;
  [key: string]: any;
}

export interface SavedAd extends GeneratedAd {
  isFavorite: boolean;
  platform?: string;
  tags?: string[];
  category?: string;
}

export interface PlatformConfig {
  name: string;
  icon: any;
  color: string;
  sizes: string[];
  defaultSize: string;
  description: string;
  promptPrefix: string;
  specifications: PlatformSpecifications;
}

export interface PlatformSpecifications {
  aspectRatio: string;
  fileSize: string;
  fileTypes: string[];
  textLimit: string;
  guidelines: string[];
}

export interface AdEditorProps {
  platform: string;
  config: PlatformConfig;
}

export interface AdControlsProps {
  prompt: string;
  size: string;
  isGenerating: boolean;
  useProductImages: boolean;
  productImages: File[];
  onPromptChange: (prompt: string) => void;
  onSizeChange: (size: string) => void;
  onUseProductImagesChange: (use: boolean) => void;
  onProductImageUpload: (files: FileList | null) => void;
  onRemoveProductImage: (index: number) => void;
  onGenerate: () => void;
  productInputRef: React.RefObject<HTMLInputElement>;
  config: PlatformConfig;
}

export interface AdPreviewProps {
  currentAd: GeneratedAd | null;
  isGenerating: boolean;
  mainTab: "latest" | "saved";
  savedAds: SavedAd[];
  productImages: File[];
  useProductImages: boolean;
  onTabChange: (tab: "latest" | "saved") => void;
  onSaveAd: (ad: GeneratedAd) => void;
  onDownload: (imageUrl: string) => void;
  onRemoveAd: (id: string) => void;
  getStats: () => { total: number };
  config: PlatformConfig;
}

export interface AdToolsProps {
  prompt: string;
  onPromptEnhance: (prompt: string) => void;
  onStyleApply: (style: string) => void;
  toast: any;
  platform: string;
}

export interface AdHeaderProps {
  onBack: () => void;
  config: PlatformConfig;
}

export interface AdSpecsProps {
  config: PlatformConfig;
}

export type MainTab = "latest" | "saved";
export type AdSize = string;
export type Platform = "facebook" | "instagram" | "google" | "linkedin" | "twitter" | "tiktok" | "youtube" | "display";

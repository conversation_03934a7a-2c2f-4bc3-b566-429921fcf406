/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_API_BASE: string
  readonly VITE_API_VERSION: string
  readonly VITE_ENABLE_AGENT_SYSTEM: string
  readonly VITE_ENABLE_CONTENT_GENERATION: string
  readonly VITE_ENABLE_SEO_TOOLS: string
  readonly VITE_DEFAULT_AGENT: string
  readonly DEV: boolean
  readonly PROD: boolean
  readonly SSR: boolean
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

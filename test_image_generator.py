#!/usr/bin/env python3
"""
Test script for the image generator API
"""

import requests
import json

def test_image_generator():
    """Test the image generator API endpoint"""
    
    url = "http://localhost:8000/api/image-generator/generate"
    
    # Test data
    data = {
        "prompt": "a beautiful sunset over mountains",
        "resolution": "1024x1024",
        "rendering_speed": "DEFAULT",
        "magic_prompt": "AUTO",
        "num_images": "1",
        "style_type": "GENERAL"
    }
    
    print("Testing Image Generator API...")
    print(f"URL: {url}")
    print(f"Data: {data}")
    
    try:
        # Make the request
        response = requests.post(url, data=data, timeout=120)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(f"Response: {json.dumps(result, indent=2)}")
            
            if result.get("success") and result.get("image_url"):
                print(f"🎨 Image generated successfully!")
                print(f"Image URL: {result['image_url']}")
            else:
                print(f"❌ API returned success=False: {result.get('error', 'Unknown error')}")
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out (this is normal for image generation)")
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_health():
    """Test the health endpoint"""
    
    url = "http://localhost:8000/api/image-generator/health"
    
    print("\nTesting Health Endpoint...")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Health check passed!")
            print(f"Response: {json.dumps(result, indent=2)}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Health check error: {e}")

if __name__ == "__main__":
    test_health()
    test_image_generator()

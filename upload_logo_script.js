const puppeteer = require('puppeteer');
const path = require('path');

(async () => {
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();
  await page.goto('http://localhost:3002/dashboard/herramientas/generador-posts-profesional');

  // Wait for the file input to be available
  const fileInputSelector = 'input[type="file"]';
  await page.waitForSelector(fileInputSelector);

  // Upload the existing test-logo.svg file
  const filePath = path.resolve(process.cwd(), 'test-logo.svg');
  const inputUploadHandle = await page.$(fileInputSelector);
  await inputUploadHandle.uploadFile(filePath);

  // Wait a bit for upload to process
  await page.waitForTimeout(2000);

  // Click the continue button
  const continueButtonSelector = 'button:has-text("Continuar")';
  await page.click(continueButtonSelector);

  // Wait for navigation or next step
  await page.waitForTimeout(3000);

  await browser.close();
})();
